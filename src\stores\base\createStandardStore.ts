/**
 * Standardized Store Pattern
 * 
 * Creates consistent Zustand stores with unified patterns for:
 * - Error handling
 * - Loading states
 * - Data operations
 * - Type safety
 * - Persistence
 */

import { create, StateCreator, StoreApi } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Base state interface that all stores should extend
export interface BaseStoreState {
  // Loading states
  loading: boolean;
  error: string | null;
  
  // Metadata
  lastUpdated: number | null;
  version: string;
}

// Base actions interface that all stores should extend
export interface BaseStoreActions {
  // Loading state management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Metadata management
  updateTimestamp: () => void;
  
  // Reset functionality
  reset: () => void;
}

// Combined base store interface
export interface BaseStore extends BaseStoreState, BaseStoreActions {}

// Configuration for creating a standard store
export interface StoreConfig<T> {
  name: string;
  version?: string;
  persist?: boolean;
  persistConfig?: {
    partialize?: (state: T) => Partial<T>;
    onRehydrateStorage?: () => (state?: T) => void;
  };
  initialState: Omit<T, keyof BaseStore>;
}

// Error handling utilities
export const createErrorHandler = (setError: (error: string | null) => void) => {
  return {
    handleError: (error: unknown, context?: string) => {
      const errorMessage = error instanceof Error 
        ? error.message 
        : typeof error === 'string' 
        ? error 
        : 'An unknown error occurred';
      
      const fullMessage = context 
        ? `${context}: ${errorMessage}` 
        : errorMessage;
      
      setError(fullMessage);
      console.error(`Store Error${context ? ` (${context})` : ''}:`, error);
    },
    
    handleAsyncOperation: async <R>(
      operation: () => Promise<R>,
      context?: string
    ): Promise<R | null> => {
      try {
        setError(null);
        return await operation();
      } catch (error) {
        const errorMessage = error instanceof Error 
          ? error.message 
          : typeof error === 'string' 
          ? error 
          : 'An unknown error occurred';
        
        const fullMessage = context 
          ? `${context}: ${errorMessage}` 
          : errorMessage;
        
        setError(fullMessage);
        console.error(`Store Error${context ? ` (${context})` : ''}:`, error);
        return null;
      }
    }
  };
};

// Loading state utilities
export const createLoadingHandler = (
  setLoading: (loading: boolean) => void,
  setError: (error: string | null) => void
) => {
  return {
    withLoading: async <T>(
      operation: () => Promise<T>,
      context?: string
    ): Promise<T | null> => {
      try {
        setLoading(true);
        setError(null);
        const result = await operation();
        return result;
      } catch (error) {
        const errorMessage = error instanceof Error 
          ? error.message 
          : typeof error === 'string' 
          ? error 
          : 'An unknown error occurred';
        
        const fullMessage = context 
          ? `${context}: ${errorMessage}` 
          : errorMessage;
        
        setError(fullMessage);
        console.error(`Store Error${context ? ` (${context})` : ''}:`, error);
        return null;
      } finally {
        setLoading(false);
      }
    }
  };
};

// Create a standardized store
export function createStandardStore<T extends BaseStore>(
  config: StoreConfig<T>,
  storeImplementation: StateCreator<T, [], [], Omit<T, keyof BaseStore>>
) {
  const baseState: BaseStoreState = {
    loading: false,
    error: null,
    lastUpdated: null,
    version: config.version || '1.0.0',
  };

  const createStore = (set: any, get: any, api: any) => {
    // Base actions
    const baseActions: BaseStoreActions = {
      setLoading: (loading: boolean) => set({ loading }),
      setError: (error: string | null) => set({ error }),
      clearError: () => set({ error: null }),
      updateTimestamp: () => set({ lastUpdated: Date.now() }),
      reset: () => set({ ...baseState, ...config.initialState }),
    };

    // Create error and loading handlers
    const errorHandler = createErrorHandler(baseActions.setError);
    const loadingHandler = createLoadingHandler(baseActions.setLoading, baseActions.setError);

    // Get the store implementation
    const storeImpl = storeImplementation(set, get, api, errorHandler, loadingHandler);

    // Combine base state, initial state, base actions, and store implementation
    return {
      ...baseState,
      ...config.initialState,
      ...baseActions,
      ...storeImpl,
    } as T;
  };

  // Create the store with or without persistence
  if (config.persist) {
    return create<T>()(
      persist(
        immer(createStore),
        {
          name: config.name,
          storage: createJSONStorage(() => localStorage),
          version: 1,
          ...config.persistConfig,
        }
      )
    );
  } else {
    return create<T>()(immer(createStore));
  }
}

// Utility types for better type inference
export type StoreImplementation<T extends BaseStore> = (
  set: any,
  get: any,
  api: any,
  errorHandler: ReturnType<typeof createErrorHandler>,
  loadingHandler: ReturnType<typeof createLoadingHandler>
) => Omit<T, keyof BaseStore>;

// Common store patterns
export const createDataStore = <TData, TStore extends BaseStore>(
  config: StoreConfig<TStore> & {
    dataKey: keyof TStore;
  },
  implementation: StoreImplementation<TStore>
) => {
  return createStandardStore(config, implementation);
};

// Async operation wrapper with optimistic updates
export const createOptimisticUpdate = <T>(
  get: () => T,
  set: (partial: Partial<T>) => void,
  operation: () => Promise<void>,
  optimisticUpdate: Partial<T>,
  context?: string
) => {
  return async () => {
    const originalState = get();
    
    try {
      // Apply optimistic update
      set(optimisticUpdate);
      
      // Perform the actual operation
      await operation();
    } catch (error) {
      // Rollback on failure
      set(originalState);
      throw error;
    }
  };
};

// Store composition utilities
export const composeStores = <T extends Record<string, any>>(stores: T): T => {
  return stores;
};

// Development utilities
export const createStoreDevtools = (storeName: string) => {
  if (process.env.NODE_ENV === 'development') {
    return {
      name: storeName,
      serialize: true,
      trace: true,
    };
  }
  return undefined;
};
