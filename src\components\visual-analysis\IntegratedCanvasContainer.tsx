/**
 * Integrated Canvas Container
 * 
 * Enhanced integration of the Living Data Canvas into the main application GUI.
 * Provides seamless integration with the design system, navigation, and user experience.
 */

import React, { useState, useEffect, Suspense } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigationStore } from '@/stores/useNavigationStore';
import { useAppTheme } from '@/design-system';
import { cn } from '@/lib/utils';
import {
  Brain,
  Zap,
  Shield,
  Settings,
  Info,
  Play,
  Pause,
  RotateCcw,
  Maximize2,
  Minimize2,
  HelpCircle,
  Palette
} from 'lucide-react';

// Lazy load canvas components
const LivingDataCanvasContainer = React.lazy(() =>
  import('./LivingDataCanvas').then(module => ({ default: module.LivingDataCanvasContainer }))
);

const LivingDataCanvasSafeContainer = React.lazy(() =>
  import('./LivingDataCanvasSafe').then(module => ({ default: module.LivingDataCanvasSafeContainer }))
);

const ChatAnalysisCanvasContainer = React.lazy(() =>
  import('./LivingDataCanvas').then(module => ({ default: module.ChatAnalysisCanvasContainer }))
);

const FigmaIntegratedCanvas = React.lazy(() =>
  import('./FigmaIntegratedCanvas').then(module => ({ default: module.FigmaIntegratedCanvas }))
);

interface CanvasMode {
  id: 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated';
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  component: React.ComponentType;
}

const canvasModes: CanvasMode[] = [
  {
    id: 'figma-integrated',
    name: 'Design Studio',
    description: 'Professional design tools with analysis integration',
    icon: Palette,
    badge: 'New',
    component: FigmaIntegratedCanvas,
  },
  {
    id: 'standard',
    name: 'Enhanced Canvas',
    description: 'Full-featured 3D visualization with advanced interactions',
    icon: Brain,
    badge: 'Recommended',
    component: LivingDataCanvasContainer,
  },
  {
    id: 'safe',
    name: 'Safe Mode',
    description: 'Simplified rendering for better compatibility',
    icon: Shield,
    badge: 'Compatible',
    component: LivingDataCanvasSafeContainer,
  },
  {
    id: 'chat-analysis',
    name: 'Chat Analysis',
    description: 'Specialized canvas for chat analysis visualization',
    icon: Zap,
    badge: 'Beta',
    component: ChatAnalysisCanvasContainer,
  },
];

export const IntegratedCanvasContainer: React.FC = () => {
  const { canvasMode, setCanvasMode } = useNavigationStore();
  const { mode: themeMode } = useAppTheme();
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const [showHelp, setShowHelp] = useState(false);

  const currentMode = canvasModes.find(mode => mode.id === canvasMode) || canvasModes[0];

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const CanvasComponent = currentMode.component;

  return (
    <div className={cn(
      "w-full h-full flex flex-col",
      "bg-gradient-to-br from-background via-background to-muted/20",
      "transition-all duration-normal"
    )}>
      {/* Canvas Header */}
      <Card className="mb-6 shadow-soft border-border/50">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20">
                <Brain className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-gradient-primary">
                  Visual Canvas
                </h2>
                <p className="text-sm text-muted-foreground">
                  Interactive data visualization and analysis workspace
                </p>
              </div>
            </div>
            
            {/* Canvas Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowHelp(!showHelp)}
                className="text-muted-foreground hover:text-foreground"
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="text-muted-foreground hover:text-foreground"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                className="text-muted-foreground hover:text-foreground"
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Canvas Mode Selection */}
      <Card className="mb-6 shadow-soft border-border/50">
        <CardContent className="pt-6">
          <Tabs value={canvasMode} onValueChange={(value) => setCanvasMode(value as any)}>
            <TabsList className="grid w-full grid-cols-4 bg-muted/60 backdrop-blur-sm">
              {canvasModes.map((mode) => {
                const Icon = mode.icon;
                return (
                  <TabsTrigger
                    key={mode.id}
                    value={mode.id}
                    className={cn(
                      "flex items-center gap-2 py-3 px-4",
                      "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                      "transition-all duration-normal"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{mode.name}</span>
                    {mode.badge && (
                      <Badge 
                        variant="secondary" 
                        className="ml-1 text-xs hidden md:inline-flex"
                      >
                        {mode.badge}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>
            
            {/* Mode Description */}
            <div className="mt-4 p-4 rounded-lg bg-muted/30 border border-border/50">
              <div className="flex items-center gap-2 mb-2">
                <currentMode.icon className="h-4 w-4 text-primary" />
                <span className="font-medium">{currentMode.name}</span>
                {currentMode.badge && (
                  <Badge variant="outline" className="text-xs">
                    {currentMode.badge}
                  </Badge>
                )}
              </div>
              <p className="text-sm text-muted-foreground">
                {currentMode.description}
              </p>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Help Panel */}
      {showHelp && (
        <Card className="mb-6 shadow-soft border-border/50 animate-fade-in">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-primary mt-0.5" />
              <div className="space-y-2">
                <h3 className="font-semibold">Canvas Controls</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <div>
                    <p><strong>Mouse:</strong> Drag to rotate, scroll to zoom</p>
                    <p><strong>Click:</strong> Select nodes and connections</p>
                    <p><strong>Right-click:</strong> Context menu</p>
                  </div>
                  <div>
                    <p><strong>Space:</strong> Play/pause animation</p>
                    <p><strong>R:</strong> Reset view</p>
                    <p><strong>F:</strong> Toggle fullscreen</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Canvas Container */}
      <Card className="flex-1 shadow-soft border-border/50 overflow-hidden">
        <CardContent className="p-0 h-full">
          <div className="relative h-full">
            <Suspense 
              fallback={
                <div className="h-full flex items-center justify-center bg-gradient-to-br from-muted/20 to-muted/40">
                  <div className="text-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                    <p className="text-muted-foreground">Loading Canvas...</p>
                  </div>
                </div>
              }
            >
              <div className="h-full">
                <CanvasComponent />
              </div>
            </Suspense>
            
            {/* Canvas Overlay Controls */}
            <div className="absolute top-4 right-4 flex flex-col gap-2">
              <Button
                variant="secondary"
                size="sm"
                className="bg-background/80 backdrop-blur-sm border border-border/50"
                onClick={() => window.location.reload()}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                className="bg-background/80 backdrop-blur-sm border border-border/50"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IntegratedCanvasContainer;
