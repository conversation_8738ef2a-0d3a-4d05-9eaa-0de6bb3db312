// Dependency vulnerability scan using npm audit
const { execSync } = require('child_process');

try {
  const output = execSync('npm audit --json', { encoding: 'utf8' });
  const audit = JSON.parse(output);
  if (audit.metadata && audit.metadata.vulnerabilities) {
    const { critical, high, moderate } = audit.metadata.vulnerabilities;
    if (critical > 0 || high > 0) {
      console.error(`Vulnerabilities found: critical=${critical}, high=${high}`);
      process.exit(1);
    }
    console.log('No critical/high vulnerabilities found.');
  } else {
    console.log('No vulnerabilities found.');
  }
} catch (e) {
  console.error('Error running npm audit:', e.message);
  process.exit(1);
}
