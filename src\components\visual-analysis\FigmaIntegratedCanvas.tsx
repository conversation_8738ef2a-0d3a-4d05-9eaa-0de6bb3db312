/**
 * Figma Integrated Canvas
 * 
 * Combines the professional Figma-style design tools with Visual Canvas analysis capabilities.
 * This creates a unified design and analysis platform that supports both creative design work
 * and data visualization/analysis workflows.
 */

import React, { useState, useEffect, useRef, Suspense } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Palette, 
  Layers, 
  Settings, 
  Brain,
  Zap,
  MousePointer,
  Square,
  Circle,
  Type,
  PenTool,
  Move,
  RotateCcw,
  Download,
  Upload,
  Share2,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "../../utils/cn";

// Lazy load components
const FigmaCanvasContainer = React.lazy(() =>
  import('@/components/figma-canvas/FigmaCanvasContainer').then(module => ({
    default: module.FigmaCanvasContainer
  }))
);

const EnhancedFigmaCanvas = React.lazy(() =>
  import('@/components/figma-canvas/EnhancedFigmaCanvas').then(module => ({
    default: module.EnhancedFigmaCanvas
  }))
);

const LivingDataCanvas = React.lazy(() =>
  import('./LivingDataCanvas').then(module => ({
    default: module.LivingDataCanvas
  }))
);

const DesignAnalysisBridge = React.lazy(() =>
  import('./DesignAnalysisBridge').then(module => ({
    default: module.default
  }))
);

interface FigmaIntegratedCanvasProps {
  className?: string;
  initialMode?: 'design' | 'analysis' | 'hybrid';
  showModeToggle?: boolean;
  enableDataBinding?: boolean;
}

type CanvasMode = 'design' | 'analysis' | 'hybrid';
type ToolMode = 'select' | 'rectangle' | 'circle' | 'text' | 'pen' | 'move';

// Hybrid Canvas View Component
interface HybridCanvasViewProps {
  activeTool: ToolMode;
  showAnalysisPanel: boolean;
  onToolChange: (tool: ToolMode) => void;
}

const HybridCanvasView: React.FC<HybridCanvasViewProps> = ({
  activeTool,
  showAnalysisPanel,
  onToolChange,
}) => {
  const [designOpacity, setDesignOpacity] = useState(0.8);
  const [analysisOpacity, setAnalysisOpacity] = useState(0.6);
  const [blendMode, setBlendMode] = useState<'normal' | 'multiply' | 'overlay'>('normal');

  return (
    <div className="w-full h-full relative">
      {/* Design Canvas Layer */}
      <div
        className="absolute inset-0 z-10"
        style={{
          opacity: designOpacity,
          mixBlendMode: blendMode
        }}
      >
        <FigmaCanvasContainer
          className="w-full h-full"
          showLayersPanel={false}
          showPropertiesPanel={false}
          onSelectionChange={(ids) => console.log('Design selection:', ids)}
          onObjectCreated={(obj) => console.log('Object created:', obj)}
        />
      </div>

      {/* Analysis Canvas Layer */}
      <div
        className="absolute inset-0 z-5"
        style={{
          opacity: analysisOpacity,
          pointerEvents: activeTool === 'select' ? 'auto' : 'none'
        }}
      >
        <LivingDataCanvas
          enableChatAnalysisIntegration={true}
        />
      </div>

      {/* Hybrid Controls */}
      <div className="absolute top-4 left-4 z-20 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <div className="space-y-3">
          <div className="text-xs font-medium text-gray-700">Layer Controls</div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600 w-12">Design</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={designOpacity}
                onChange={(e) => setDesignOpacity(parseFloat(e.target.value))}
                className="flex-1"
              />
              <span className="text-xs text-gray-500 w-8">{Math.round(designOpacity * 100)}%</span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600 w-12">Analysis</span>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={analysisOpacity}
                onChange={(e) => setAnalysisOpacity(parseFloat(e.target.value))}
                className="flex-1"
              />
              <span className="text-xs text-gray-500 w-8">{Math.round(analysisOpacity * 100)}%</span>
            </div>
          </div>

          <div className="pt-2 border-t border-gray-200">
            <select
              value={blendMode}
              onChange={(e) => setBlendMode(e.target.value as any)}
              className="text-xs w-full p-1 border border-gray-300 rounded"
            >
              <option value="normal">Normal</option>
              <option value="multiply">Multiply</option>
              <option value="overlay">Overlay</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export const FigmaIntegratedCanvas: React.FC<FigmaIntegratedCanvasProps> = ({
  className,
  initialMode = 'hybrid',
  showModeToggle = true,
  enableDataBinding = true,
}) => {
  const [canvasMode, setCanvasMode] = useState<CanvasMode>(initialMode);
  const [activeTool, setActiveTool] = useState<ToolMode>('select');
  const [showLayers, setShowLayers] = useState(true);
  const [showProperties, setShowProperties] = useState(true);
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const canvasRef = useRef<HTMLDivElement>(null);

  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      canvasRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  const tools = [
    { id: 'select', icon: MousePointer, label: 'Select' },
    { id: 'rectangle', icon: Square, label: 'Rectangle' },
    { id: 'circle', icon: Circle, label: 'Circle' },
    { id: 'text', icon: Type, label: 'Text' },
    { id: 'pen', icon: PenTool, label: 'Pen' },
    { id: 'move', icon: Move, label: 'Move' },
  ];

  const canvasModes = [
    { id: 'design', label: 'Design Mode', icon: Palette, description: 'Professional design tools' },
    { id: 'analysis', label: 'Analysis Mode', icon: Brain, description: 'Data visualization' },
    { id: 'hybrid', label: 'Hybrid Mode', icon: Zap, description: 'Combined design & analysis' },
  ];

  return (
    <div 
      ref={canvasRef}
      className={cn(
        "w-full h-full flex flex-col bg-background",
        "border border-border rounded-lg overflow-hidden",
        className
      )}
    >
      {/* Header Toolbar */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            {/* Left: Mode Toggle */}
            {showModeToggle && (
              <Tabs value={canvasMode} onValueChange={(value) => setCanvasMode(value as CanvasMode)}>
                <TabsList className="bg-muted/60">
                  {canvasModes.map((mode) => {
                    const Icon = mode.icon;
                    return (
                      <TabsTrigger
                        key={mode.id}
                        value={mode.id}
                        className="flex items-center gap-2 px-3 py-2"
                      >
                        <Icon className="h-4 w-4" />
                        <span className="hidden sm:inline">{mode.label}</span>
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
              </Tabs>
            )}

            {/* Center: Tools */}
            <div className="flex items-center gap-1 bg-muted/30 rounded-lg p-1">
              {tools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <Button
                    key={tool.id}
                    variant={activeTool === tool.id ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setActiveTool(tool.id as ToolMode)}
                    className="h-8 w-8 p-0"
                    title={tool.label}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                );
              })}
            </div>

            {/* Right: View Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowLayers(!showLayers)}
                className={cn("h-8", showLayers && "bg-muted")}
              >
                <Layers className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowProperties(!showProperties)}
                className={cn("h-8", showProperties && "bg-muted")}
              >
                <Settings className="h-4 w-4" />
              </Button>

              {(canvasMode === 'analysis' || canvasMode === 'hybrid') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAnalysisPanel(!showAnalysisPanel)}
                  className={cn("h-8", showAnalysisPanel && "bg-muted")}
                >
                  <Brain className="h-4 w-4" />
                </Button>
              )}

              <Separator orientation="vertical" className="h-6" />

              <Button variant="ghost" size="sm" className="h-8">
                <Upload className="h-4 w-4" />
              </Button>
              
              <Button variant="ghost" size="sm" className="h-8">
                <Download className="h-4 w-4" />
              </Button>
              
              <Button variant="ghost" size="sm" className="h-8">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Layers */}
        {showLayers && (
          <Card className="w-64 rounded-none border-y-0 border-l-0 flex-shrink-0">
            <CardHeader className="pb-3">
              <h3 className="font-semibold text-sm">Layers</h3>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  Layers panel will be integrated here
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Center - Canvas Area */}
        <div className="flex-1 relative">
          <Suspense
            fallback={
              <div className="h-full flex items-center justify-center bg-muted/20">
                <div className="text-center space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <p className="text-muted-foreground">Loading Canvas...</p>
                </div>
              </div>
            }
          >
            {canvasMode === 'design' && (
              <EnhancedFigmaCanvas
                className="w-full h-full"
                showAdvancedTools={true}
                enableAIFeatures={true}
                onExport={(format) => console.log('Export:', format)}
              />
            )}

            {canvasMode === 'analysis' && (
              <LivingDataCanvas
                enableChatAnalysisIntegration={true}
              />
            )}

            {canvasMode === 'hybrid' && (
              <HybridCanvasView
                activeTool={activeTool}
                showAnalysisPanel={showAnalysisPanel}
                onToolChange={setActiveTool}
              />
            )}
          </Suspense>
        </div>

        {/* Right Panel - Properties */}
        {showProperties && (
          <Card className="w-64 rounded-none border-y-0 border-r-0 flex-shrink-0">
            <CardHeader className="pb-3">
              <h3 className="font-semibold text-sm">Properties</h3>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Properties panel will be integrated here
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Analysis Panel */}
        {showAnalysisPanel && (canvasMode === 'analysis' || canvasMode === 'hybrid') && (
          <Card className="w-80 rounded-none border-y-0 border-r-0 flex-shrink-0">
            <CardHeader className="pb-3">
              <h3 className="font-semibold text-sm">Analysis Tools</h3>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto p-0">
              <Suspense fallback={
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-xs text-muted-foreground">Loading tools...</p>
                </div>
              }>
                <DesignAnalysisBridge
                  analysisResults={[]} // This would come from props or context
                  selectedResultId={undefined}
                  onCreateDesignElement={(element) => {
                    console.log('Creating design element:', element);
                    // Integration with Figma canvas would happen here
                  }}
                  onExportPresentation={(format) => {
                    console.log('Exporting presentation:', format);
                  }}
                  className="border-0 shadow-none"
                />
              </Suspense>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default FigmaIntegratedCanvas;
