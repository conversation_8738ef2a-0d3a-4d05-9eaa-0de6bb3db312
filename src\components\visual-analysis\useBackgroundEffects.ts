import { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { createParticleField, update<PERSON><PERSON><PERSON><PERSON>ield, createGradientBackground, updateGradientBackground, BackgroundEffectOptions } from './backgroundEffects';

/**
 * Custom hook to encapsulate background and particle field setup for LivingDataCanvas.
 * Returns refs to particleField and backgroundMesh.
 */
export function useBackgroundEffects(sceneRef: React.MutableRefObject<THREE.Scene | null>, cameraRef: React.MutableRefObject<THREE.OrthographicCamera | null>) {
  const particleFieldRef = useRef<THREE.Points | null>(null);
  const backgroundMeshRef = useRef<THREE.Mesh | null>(null);

  useEffect(() => {
    if (!sceneRef.current || !cameraRef.current) return;
    // Enhanced gradient background
    sceneRef.current.background = new THREE.Color(0x0a0a1e);
    backgroundMeshRef.current = createGradientBackground(sceneRef.current, cameraRef.current);
    // Particle field
    const particleOptions: BackgroundEffectOptions = {
      particleCount: 50,
      particleSize: 0.6,
      animationSpeed: 0.2,
      opacity: 0.3,
      color: 0x6366f1
    };
    particleFieldRef.current = createParticleField(particleOptions);
    sceneRef.current.add(particleFieldRef.current);
    return () => {
      if (particleFieldRef.current) {
        sceneRef.current?.remove(particleFieldRef.current);
        particleFieldRef.current.geometry.dispose();
        (particleFieldRef.current.material as THREE.PointsMaterial).dispose();
        particleFieldRef.current = null;
      }
      if (backgroundMeshRef.current) {
        sceneRef.current?.remove(backgroundMeshRef.current);
        backgroundMeshRef.current.geometry.dispose();
        (backgroundMeshRef.current.material as THREE.ShaderMaterial).dispose();
        backgroundMeshRef.current = null;
      }
    };
  }, [sceneRef, cameraRef]);

  return { particleFieldRef, backgroundMeshRef };
}
