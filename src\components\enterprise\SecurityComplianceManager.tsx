import React, { useState, use<PERSON><PERSON>back, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  Lock, 
  Eye, 
  FileText, 
  Users, 
  Globe,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  Key,
  Fingerprint,
  Settings,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react';

export interface SecurityPolicy {
  id: string;
  name: string;
  category: 'access' | 'data' | 'network' | 'audit' | 'privacy';
  status: 'active' | 'inactive' | 'pending';
  compliance: string[];
  lastUpdated: Date;
  description: string;
  rules: SecurityRule[];
}

export interface SecurityRule {
  id: string;
  type: 'allow' | 'deny' | 'require';
  condition: string;
  action: string;
  priority: number;
}

export interface ComplianceFramework {
  id: string;
  name: string;
  version: string;
  status: 'compliant' | 'non-compliant' | 'partial' | 'pending';
  requirements: ComplianceRequirement[];
  lastAudit: Date;
  nextAudit: Date;
  score: number;
}

export interface ComplianceRequirement {
  id: string;
  title: string;
  description: string;
  status: 'met' | 'not-met' | 'partial' | 'not-applicable';
  evidence: string[];
  lastChecked: Date;
}

export interface AuditLog {
  id: string;
  timestamp: Date;
  user: string;
  action: string;
  resource: string;
  outcome: 'success' | 'failure' | 'warning';
  details: { method: string } | { format: string; recordCount: number } | Record<string, unknown>;
  ipAddress: string;
  userAgent: string;
}

interface SecurityComplianceManagerProps {
  onPolicyUpdate?: (policy: SecurityPolicy) => void;
  onAuditExport?: (logs: AuditLog[]) => void;
  className?: string;
}

export const SecurityComplianceManager: React.FC<SecurityComplianceManagerProps> = ({
  onPolicyUpdate,
  onAuditExport,
  className,
}) => {
  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([
    {
      id: 'pol-001',
      name: 'Data Encryption Policy',
      category: 'data',
      status: 'active',
      compliance: ['SOC2', 'GDPR', 'HIPAA'],
      lastUpdated: new Date(),
      description: 'All data must be encrypted at rest and in transit',
      rules: [
        { id: 'rule-001', type: 'require', condition: 'data_storage', action: 'encrypt_aes256', priority: 1 },
        { id: 'rule-002', type: 'require', condition: 'data_transmission', action: 'encrypt_tls13', priority: 1 },
      ],
    },
    {
      id: 'pol-002',
      name: 'Access Control Policy',
      category: 'access',
      status: 'active',
      compliance: ['SOC2', 'ISO27001'],
      lastUpdated: new Date(),
      description: 'Multi-factor authentication required for all users',
      rules: [
        { id: 'rule-003', type: 'require', condition: 'user_login', action: 'mfa_required', priority: 1 },
        { id: 'rule-004', type: 'deny', condition: 'failed_attempts > 5', action: 'account_lockout', priority: 2 },
      ],
    },
  ]);

  const [complianceFrameworks, setComplianceFrameworks] = useState<ComplianceFramework[]>([
    {
      id: 'soc2',
      name: 'SOC 2 Type II',
      version: '2023',
      status: 'compliant',
      score: 95,
      lastAudit: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      nextAudit: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000),
      requirements: [
        {
          id: 'soc2-001',
          title: 'Security Controls',
          description: 'Implement comprehensive security controls',
          status: 'met',
          evidence: ['security-policy.pdf', 'access-controls.json'],
          lastChecked: new Date(),
        },
        {
          id: 'soc2-002',
          title: 'Availability Controls',
          description: 'Ensure system availability and performance',
          status: 'met',
          evidence: ['uptime-reports.pdf', 'monitoring-config.json'],
          lastChecked: new Date(),
        },
      ],
    },
    {
      id: 'gdpr',
      name: 'GDPR',
      version: '2018',
      status: 'compliant',
      score: 88,
      lastAudit: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      nextAudit: new Date(Date.now() + 305 * 24 * 60 * 60 * 1000),
      requirements: [
        {
          id: 'gdpr-001',
          title: 'Data Protection by Design',
          description: 'Implement privacy by design principles',
          status: 'met',
          evidence: ['privacy-impact-assessment.pdf'],
          lastChecked: new Date(),
        },
        {
          id: 'gdpr-002',
          title: 'Right to be Forgotten',
          description: 'Implement data deletion capabilities',
          status: 'partial',
          evidence: ['deletion-procedures.pdf'],
          lastChecked: new Date(),
        },
      ],
    },
  ]);

  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([
    {
      id: 'audit-001',
      timestamp: new Date(),
      user: '<EMAIL>',
      action: 'LOGIN',
      resource: 'application',
      outcome: 'success',
      details: { method: 'mfa' },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
    },
    {
      id: 'audit-002',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      user: '<EMAIL>',
      action: 'DATA_EXPORT',
      resource: 'analysis-results',
      outcome: 'success',
      details: { format: 'pdf', recordCount: 150 },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0...',
    },
  ]);

  const [securityMetrics, setSecurityMetrics] = useState({
    threatLevel: 'low',
    vulnerabilities: 2,
    patchCompliance: 98,
    accessViolations: 0,
    dataBreaches: 0,
    complianceScore: 92,
  });

  // Update security policy
  const updatePolicy = useCallback((policyId: string, updates: Partial<SecurityPolicy>) => {
    setSecurityPolicies(prev => prev.map(policy => 
      policy.id === policyId 
        ? { ...policy, ...updates, lastUpdated: new Date() }
        : policy
    ));
  }, []);

  // Run compliance check
  const runComplianceCheck = useCallback(async (frameworkId: string) => {
    const framework = complianceFrameworks.find(f => f.id === frameworkId);
    if (!framework) return;

    // Simulate compliance checking
    const updatedRequirements = framework.requirements.map(req => ({
      ...req,
      lastChecked: new Date(),
      status: Math.random() > 0.1 ? 'met' : 'partial', // status is typed correctly
    }));

    const score = (updatedRequirements.filter(r => r.status === 'met').length / updatedRequirements.length) * 100;

    setComplianceFrameworks(prev => prev.map(f => 
      f.id === frameworkId 
        ? { 
            ...f, 
            requirements: updatedRequirements,
            score: Math.round(score),
            status: score >= 90 ? 'compliant' : score >= 70 ? 'partial' : 'non-compliant',
          } as ComplianceFramework // ensure type
        : f
    ));
  }, [complianceFrameworks]);

  // Generate audit report
  const generateAuditReport = useCallback(() => {
    const report = {
      timestamp: new Date(),
      policies: securityPolicies,
      compliance: complianceFrameworks,
      metrics: securityMetrics,
      logs: auditLogs.slice(0, 100), // Last 100 logs
    };

    onAuditExport?.(auditLogs);
    
    // Download report
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `security-audit-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [securityPolicies, complianceFrameworks, securityMetrics, auditLogs, onAuditExport]);

  // Security dashboard metrics
  const overallComplianceScore = Math.round(
    complianceFrameworks.reduce((sum, f) => sum + f.score, 0) / complianceFrameworks.length
  );

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security & Compliance Manager
              <Badge variant={overallComplianceScore >= 90 ? "default" : "destructive"}>
                {overallComplianceScore}% Compliant
              </Badge>
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={generateAuditReport}>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
              
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <Tabs defaultValue="dashboard" className="w-full h-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="policies">Policies</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="audit">Audit Logs</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Security Dashboard */}
          <TabsContent value="dashboard" className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Threat Level</div>
                      <div className="font-medium capitalize">{securityMetrics.threatLevel}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Vulnerabilities</div>
                      <div className="font-medium">{securityMetrics.vulnerabilities}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Patch Compliance</div>
                      <div className="font-medium">{securityMetrics.patchCompliance}%</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-5 w-5 text-red-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Data Breaches</div>
                      <div className="font-medium">{securityMetrics.dataBreaches}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Compliance Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {complianceFrameworks.map(framework => (
                    <div key={framework.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{framework.name}</span>
                        <Badge variant={
                          framework.status === 'compliant' ? 'default' :
                          framework.status === 'partial' ? 'secondary' : 'destructive'
                        }>
                          {framework.status}
                        </Badge>
                      </div>
                      <Progress value={framework.score} className="h-2" />
                      <div className="text-xs text-muted-foreground">
                        Score: {framework.score}% | Next audit: {framework.nextAudit.toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Security Events</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {auditLogs.slice(0, 5).map(log => (
                      <div key={log.id} className="flex items-center gap-3 p-2 border rounded">
                        {log.outcome === 'success' ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : log.outcome === 'warning' ? (
                          <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                        <div className="flex-1">
                          <div className="text-sm font-medium">{log.action}</div>
                          <div className="text-xs text-muted-foreground">
                            {log.user} • {log.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Security Policies */}
          <TabsContent value="policies" className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Security Policies</h3>
                <Button>
                  <Lock className="h-4 w-4 mr-2" />
                  Add Policy
                </Button>
              </div>

              <div className="grid gap-4">
                {securityPolicies.map(policy => (
                  <Card key={policy.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{policy.name}</h4>
                          <Badge variant={policy.status === 'active' ? 'default' : 'secondary'}>
                            {policy.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          {policy.compliance.map(comp => (
                            <Badge key={comp} variant="outline" className="text-xs">
                              {comp}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">{policy.description}</p>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-muted-foreground">
                          {policy.rules.length} rules • Updated {policy.lastUpdated.toLocaleDateString()}
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">Edit</Button>
                          <Button size="sm" variant="outline">Test</Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Compliance Frameworks */}
          <TabsContent value="compliance" className="p-6">
            <div className="space-y-6">
              {complianceFrameworks.map(framework => (
                <Card key={framework.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        {framework.name} {framework.version}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          framework.status === 'compliant' ? 'default' :
                          framework.status === 'partial' ? 'secondary' : 'destructive'
                        }>
                          {framework.status}
                        </Badge>
                        <Button size="sm" onClick={() => runComplianceCheck(framework.id)}>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Check
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">Overall Score</span>
                          <span className="text-sm">{framework.score}%</span>
                        </div>
                        <Progress value={framework.score} className="h-2" />
                      </div>

                      <div className="grid gap-3">
                        {framework.requirements.map(req => (
                          <div key={req.id} className="flex items-center justify-between p-3 border rounded">
                            <div className="flex-1">
                              <div className="font-medium text-sm">{req.title}</div>
                              <div className="text-xs text-muted-foreground">{req.description}</div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={
                                req.status === 'met' ? 'default' :
                                req.status === 'partial' ? 'secondary' : 'destructive'
                              }>
                                {req.status}
                              </Badge>
                              {req.status === 'met' && <CheckCircle className="h-4 w-4 text-green-600" />}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Audit Logs */}
          <TabsContent value="audit" className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Audit Logs</h3>
                <div className="flex gap-2">
                  <Button variant="outline">
                    <Eye className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" onClick={() => onAuditExport?.(auditLogs)}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              <Card>
                <CardContent className="p-0">
                  <div className="overflow-auto max-h-96">
                    <table className="w-full text-sm">
                      <thead className="border-b bg-gray-50">
                        <tr>
                          <th className="text-left p-3">Timestamp</th>
                          <th className="text-left p-3">User</th>
                          <th className="text-left p-3">Action</th>
                          <th className="text-left p-3">Resource</th>
                          <th className="text-left p-3">Outcome</th>
                          <th className="text-left p-3">IP Address</th>
                        </tr>
                      </thead>
                      <tbody>
                        {auditLogs.map(log => (
                          <tr key={log.id} className="border-b hover:bg-gray-50">
                            <td className="p-3">{log.timestamp.toLocaleString()}</td>
                            <td className="p-3">{log.user}</td>
                            <td className="p-3">{log.action}</td>
                            <td className="p-3">{log.resource}</td>
                            <td className="p-3">
                              <Badge variant={
                                log.outcome === 'success' ? 'default' :
                                log.outcome === 'warning' ? 'secondary' : 'destructive'
                              }>
                                {log.outcome}
                              </Badge>
                            </td>
                            <td className="p-3">{log.ipAddress}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings */}
          <TabsContent value="settings" className="p-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Multi-Factor Authentication</div>
                      <div className="text-sm text-muted-foreground">Require MFA for all users</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Session Timeout</div>
                      <div className="text-sm text-muted-foreground">Automatic logout after inactivity</div>
                    </div>
                    <select className="border rounded px-3 py-1">
                      <option>30 minutes</option>
                      <option>1 hour</option>
                      <option>4 hours</option>
                      <option>8 hours</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Audit Logging</div>
                      <div className="text-sm text-muted-foreground">Log all user actions</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Data Protection</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Data Encryption</div>
                      <div className="text-sm text-muted-foreground">Encrypt all data at rest</div>
                    </div>
                    <Badge variant="default">AES-256</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Data Retention</div>
                      <div className="text-sm text-muted-foreground">Automatic data cleanup</div>
                    </div>
                    <select className="border rounded px-3 py-1">
                      <option>1 year</option>
                      <option>2 years</option>
                      <option>5 years</option>
                      <option>7 years</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Backup Encryption</div>
                      <div className="text-sm text-muted-foreground">Encrypt backup files</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
