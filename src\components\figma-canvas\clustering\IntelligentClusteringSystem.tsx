import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { 
  Brain, 
  Zap, 
  Group, 
  Layers, 
  Target,
  BarChart3,
  Settings,
  Sparkles
} from 'lucide-react';
import { DrawingObject, Point } from '@/types/figma';

export interface ClusterBoundary {
  id: string;
  name: string;
  objectIds: string[];
  boundaryPath: Point[];
  style: ClusterStyle;
  metadata: ClusterMetadata;
  parentClusterId?: string;
  childClusterIds: string[];
}

export interface ClusterStyle {
  fillColor: string;
  strokeColor: string;
  strokeWidth: number;
  opacity: number;
  borderRadius: number;
  dashPattern?: number[];
}

export interface ClusterMetadata {
  theme: string;
  confidence: number;
  tags: string[];
  description: string;
  createdAt: Date;
  updatedAt: Date;
  analytics: ClusterAnalytics;
}

export interface ClusterAnalytics {
  objectCount: number;
  averageDistance: number;
  cohesionScore: number;
  separationScore: number;
  silhouetteScore: number;
}

interface IntelligentClusteringSystemProps {
  objects: Record<string, DrawingObject>;
  clusters: ClusterBoundary[];
  onClusterCreate?: (cluster: Omit<ClusterBoundary, 'id'>) => void;
  onClusterUpdate?: (clusterId: string, updates: Partial<ClusterBoundary>) => void;
  onClusterDelete?: (clusterId: string) => void;
  enableAIClustering?: boolean;
  showAnalytics?: boolean;
}

type ClusteringAlgorithm = 'kmeans' | 'dbscan' | 'hierarchical' | 'semantic';

export const IntelligentClusteringSystem: React.FC<IntelligentClusteringSystemProps> = ({
  objects,
  clusters,
  onClusterCreate,
  onClusterUpdate,
  onClusterDelete,
  enableAIClustering = true,
  showAnalytics = true,
}) => {
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<ClusteringAlgorithm>('semantic');
  const [clusterCount, setClusterCount] = useState(3);
  const [minClusterSize, setMinClusterSize] = useState(2);
  const [similarityThreshold, setSimilarityThreshold] = useState(0.7);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedClusterId, setSelectedClusterId] = useState<string | null>(null);

  // Calculate object positions for clustering
  const objectPositions = useMemo(() => {
    return Object.values(objects).map(obj => ({
      id: obj.id,
      x: obj.transform.x + obj.transform.width / 2,
      y: obj.transform.y + obj.transform.height / 2,
      type: obj.type,
      name: obj.name,
    }));
  }, [objects]);

  // K-means clustering algorithm
  const performKMeansClustering = useCallback((k: number) => {
    if (objectPositions.length < k) return [];

    // Initialize centroids randomly
    let centroids = Array.from({ length: k }, () => ({
      x: Math.random() * 1000,
      y: Math.random() * 600,
    }));

    const assignments: number[] = new Array(objectPositions.length).fill(0);
    let hasChanged = true;
    let iterations = 0;
    const maxIterations = 100;

    while (hasChanged && iterations < maxIterations) {
      hasChanged = false;
      iterations++;

      // Assign points to nearest centroid
      objectPositions.forEach((point, i) => {
        let minDistance = Infinity;
        let nearestCentroid = 0;

        centroids.forEach((centroid, j) => {
          const distance = Math.sqrt(
            Math.pow(point.x - centroid.x, 2) + Math.pow(point.y - centroid.y, 2)
          );
          if (distance < minDistance) {
            minDistance = distance;
            nearestCentroid = j;
          }
        });

        if (assignments[i] !== nearestCentroid) {
          assignments[i] = nearestCentroid;
          hasChanged = true;
        }
      });

      // Update centroids
      centroids = centroids.map((_, j) => {
        const clusterPoints = objectPositions.filter((_, i) => assignments[i] === j);
        if (clusterPoints.length === 0) return centroids[j];

        return {
          x: clusterPoints.reduce((sum, p) => sum + p.x, 0) / clusterPoints.length,
          y: clusterPoints.reduce((sum, p) => sum + p.y, 0) / clusterPoints.length,
        };
      });
    }

    // Group objects by cluster
    const clusterGroups = Array.from({ length: k }, () => [] as typeof objectPositions);
    objectPositions.forEach((point, i) => {
      clusterGroups[assignments[i]].push(point);
    });

    return clusterGroups.filter(group => group.length >= minClusterSize);
  }, [objectPositions, minClusterSize]);

  // DBSCAN clustering algorithm
  const performDBSCANClustering = useCallback((eps: number, minPts: number) => {
    const visited = new Set<number>();
    const clustered = new Set<number>();
    const clusters: typeof objectPositions[] = [];

    const getNeighbors = (pointIndex: number) => {
      const neighbors: number[] = [];
      const point = objectPositions[pointIndex];

      objectPositions.forEach((otherPoint, i) => {
        if (i === pointIndex) return;
        const distance = Math.sqrt(
          Math.pow(point.x - otherPoint.x, 2) + Math.pow(point.y - otherPoint.y, 2)
        );
        if (distance <= eps) {
          neighbors.push(i);
        }
      });

      return neighbors;
    };

    objectPositions.forEach((_, i) => {
      if (visited.has(i)) return;
      visited.add(i);

      const neighbors = getNeighbors(i);
      if (neighbors.length < minPts) return;

      // Start new cluster
      const cluster = [objectPositions[i]];
      clustered.add(i);

      const queue = [...neighbors];
      while (queue.length > 0) {
        const neighborIndex = queue.shift()!;
        
        if (!visited.has(neighborIndex)) {
          visited.add(neighborIndex);
          const neighborNeighbors = getNeighbors(neighborIndex);
          if (neighborNeighbors.length >= minPts) {
            queue.push(...neighborNeighbors);
          }
        }

        if (!clustered.has(neighborIndex)) {
          cluster.push(objectPositions[neighborIndex]);
          clustered.add(neighborIndex);
        }
      }

      clusters.push(cluster);
    });

    return clusters;
  }, [objectPositions]);

  // Semantic clustering based on object types and names
  const performSemanticClustering = useCallback(() => {
    const typeGroups: Record<string, typeof objectPositions> = {};
    
    objectPositions.forEach(obj => {
      if (!typeGroups[obj.type]) {
        typeGroups[obj.type] = [];
      }
      typeGroups[obj.type].push(obj);
    });

    // Further cluster by name similarity within types
    const clusters: typeof objectPositions[] = [];
    
    Object.values(typeGroups).forEach(group => {
      if (group.length < minClusterSize) {
        if (group.length > 0) clusters.push(group);
        return;
      }

      // Simple name-based clustering
      const nameGroups: Record<string, typeof objectPositions> = {};
      group.forEach(obj => {
        const nameKey = obj.name.toLowerCase().split(' ')[0] || obj.type;
        if (!nameGroups[nameKey]) {
          nameGroups[nameKey] = [];
        }
        nameGroups[nameKey].push(obj);
      });

      Object.values(nameGroups).forEach(nameGroup => {
        if (nameGroup.length >= minClusterSize) {
          clusters.push(nameGroup);
        }
      });
    });

    return clusters;
  }, [objectPositions, minClusterSize]);

  // Generate cluster boundary from object positions
  const generateClusterBoundary = useCallback((clusterObjects: typeof objectPositions): Point[] => {
    if (clusterObjects.length === 0) return [];

    // Calculate bounding box with padding
    const padding = 20;
    const minX = Math.min(...clusterObjects.map(obj => obj.x)) - padding;
    const maxX = Math.max(...clusterObjects.map(obj => obj.x)) + padding;
    const minY = Math.min(...clusterObjects.map(obj => obj.y)) - padding;
    const maxY = Math.max(...clusterObjects.map(obj => obj.y)) + padding;

    // Create rounded rectangle boundary
    const cornerRadius = 15;
    return [
      { x: minX + cornerRadius, y: minY },
      { x: maxX - cornerRadius, y: minY },
      { x: maxX, y: minY + cornerRadius },
      { x: maxX, y: maxY - cornerRadius },
      { x: maxX - cornerRadius, y: maxY },
      { x: minX + cornerRadius, y: maxY },
      { x: minX, y: maxY - cornerRadius },
      { x: minX, y: minY + cornerRadius },
    ];
  }, []);

  // Calculate cluster analytics
  const calculateClusterAnalytics = useCallback((clusterObjects: typeof objectPositions): ClusterAnalytics => {
    if (clusterObjects.length === 0) {
      return {
        objectCount: 0,
        averageDistance: 0,
        cohesionScore: 0,
        separationScore: 0,
        silhouetteScore: 0,
      };
    }

    // Calculate centroid
    const centroid = {
      x: clusterObjects.reduce((sum, obj) => sum + obj.x, 0) / clusterObjects.length,
      y: clusterObjects.reduce((sum, obj) => sum + obj.y, 0) / clusterObjects.length,
    };

    // Calculate average distance to centroid (cohesion)
    const distances = clusterObjects.map(obj => 
      Math.sqrt(Math.pow(obj.x - centroid.x, 2) + Math.pow(obj.y - centroid.y, 2))
    );
    const averageDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;

    // Cohesion score (lower is better, normalized to 0-1)
    const cohesionScore = Math.max(0, 1 - averageDistance / 200);

    // Simplified separation and silhouette scores
    const separationScore = 0.8; // Placeholder
    const silhouetteScore = (cohesionScore + separationScore) / 2;

    return {
      objectCount: clusterObjects.length,
      averageDistance,
      cohesionScore,
      separationScore,
      silhouetteScore,
    };
  }, []);

  // Run clustering analysis
  const runClusteringAnalysis = useCallback(async () => {
    if (objectPositions.length < 2) return;

    setIsAnalyzing(true);

    try {
      let clusterGroups: typeof objectPositions[] = [];

      switch (selectedAlgorithm) {
        case 'kmeans':
          clusterGroups = performKMeansClustering(clusterCount);
          break;
        case 'dbscan':
          clusterGroups = performDBSCANClustering(50, minClusterSize);
          break;
        case 'semantic':
          clusterGroups = performSemanticClustering();
          break;
        case 'hierarchical':
          // Simplified hierarchical clustering
          clusterGroups = performKMeansClustering(clusterCount);
          break;
      }

      // Create cluster boundaries
      clusterGroups.forEach((group, index) => {
        const boundary = generateClusterBoundary(group);
        const analytics = calculateClusterAnalytics(group);
        
        const cluster: Omit<ClusterBoundary, 'id'> = {
          name: `${selectedAlgorithm.charAt(0).toUpperCase() + selectedAlgorithm.slice(1)} Cluster ${index + 1}`,
          objectIds: group.map(obj => obj.id),
          boundaryPath: boundary,
          style: {
            fillColor: `hsl(${(index * 137.5) % 360}, 70%, 95%)`,
            strokeColor: `hsl(${(index * 137.5) % 360}, 70%, 60%)`,
            strokeWidth: 2,
            opacity: 0.3,
            borderRadius: 15,
          },
          metadata: {
            theme: group[0]?.type || 'mixed',
            confidence: analytics.silhouetteScore,
            tags: [...new Set(group.map(obj => obj.type))],
            description: `Auto-generated cluster containing ${group.length} objects`,
            createdAt: new Date(),
            updatedAt: new Date(),
            analytics,
          },
          childClusterIds: [],
        };

        onClusterCreate?.(cluster);
      });
    } catch (error) {
      console.error('Clustering analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [
    objectPositions,
    selectedAlgorithm,
    clusterCount,
    minClusterSize,
    performKMeansClustering,
    performDBSCANClustering,
    performSemanticClustering,
    generateClusterBoundary,
    calculateClusterAnalytics,
    onClusterCreate,
  ]);

  // Render cluster boundary
  const renderClusterBoundary = (cluster: ClusterBoundary) => {
    const pathString = cluster.boundaryPath.length > 0 
      ? `M ${cluster.boundaryPath[0].x} ${cluster.boundaryPath[0].y} ` +
        cluster.boundaryPath.slice(1).map(p => `L ${p.x} ${p.y}`).join(' ') + ' Z'
      : '';

    return (
      <g key={cluster.id}>
        <path
          d={pathString}
          fill={cluster.style.fillColor}
          stroke={cluster.style.strokeColor}
          strokeWidth={cluster.style.strokeWidth}
          strokeDasharray={cluster.style.dashPattern?.join(' ')}
          opacity={cluster.style.opacity}
          className="cursor-pointer"
          onClick={() => setSelectedClusterId(
            selectedClusterId === cluster.id ? null : cluster.id
          )}
        />
        
        {/* Cluster label */}
        {cluster.boundaryPath.length > 0 && (
          <text
            x={cluster.boundaryPath.reduce((sum, p) => sum + p.x, 0) / cluster.boundaryPath.length}
            y={cluster.boundaryPath.reduce((sum, p) => sum + p.y, 0) / cluster.boundaryPath.length}
            textAnchor="middle"
            fontSize="12"
            fill={cluster.style.strokeColor}
            className="pointer-events-none font-medium"
          >
            {cluster.name}
          </text>
        )}
      </g>
    );
  };

  return (
    <div className="space-y-4">
      {/* Clustering Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Intelligent Clustering
            {enableAIClustering && <Badge variant="secondary">AI-Powered</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Algorithm</label>
              <select
                value={selectedAlgorithm}
                onChange={(e) => setSelectedAlgorithm(e.target.value as ClusteringAlgorithm)}
                className="w-full mt-1 p-2 border rounded-md"
              >
                <option value="semantic">Semantic (AI)</option>
                <option value="kmeans">K-Means</option>
                <option value="dbscan">DBSCAN</option>
                <option value="hierarchical">Hierarchical</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium">Cluster Count</label>
              <Slider
                value={[clusterCount]}
                onValueChange={([value]) => setClusterCount(value)}
                min={2}
                max={8}
                step={1}
                className="mt-2"
              />
              <div className="text-xs text-muted-foreground mt-1">{clusterCount}</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Min Cluster Size</label>
              <Slider
                value={[minClusterSize]}
                onValueChange={([value]) => setMinClusterSize(value)}
                min={1}
                max={5}
                step={1}
                className="mt-2"
              />
              <div className="text-xs text-muted-foreground mt-1">{minClusterSize}</div>
            </div>

            <div>
              <label className="text-sm font-medium">Similarity Threshold</label>
              <Slider
                value={[similarityThreshold]}
                onValueChange={([value]) => setSimilarityThreshold(value)}
                min={0.1}
                max={1}
                step={0.1}
                className="mt-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {(similarityThreshold * 100).toFixed(0)}%
              </div>
            </div>
          </div>

          <Button 
            onClick={runClusteringAnalysis}
            disabled={isAnalyzing || objectPositions.length < 2}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Sparkles className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Run Clustering Analysis
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Cluster Analytics */}
      {showAnalytics && clusters.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Cluster Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {clusters.map(cluster => (
                <div 
                  key={cluster.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedClusterId === cluster.id ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedClusterId(
                    selectedClusterId === cluster.id ? null : cluster.id
                  )}
                >
                  <div className="font-medium text-sm">{cluster.name}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Objects: {cluster.metadata.analytics.objectCount}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Quality: {(cluster.metadata.analytics.silhouetteScore * 100).toFixed(0)}%
                  </div>
                  <div className="flex gap-1 mt-2">
                    {cluster.metadata.tags.map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* SVG Overlay for rendering cluster boundaries */}
      <div className="absolute inset-0 pointer-events-none">
        <svg className="w-full h-full">
          {clusters.map(renderClusterBoundary)}
        </svg>
      </div>
    </div>
  );
};
