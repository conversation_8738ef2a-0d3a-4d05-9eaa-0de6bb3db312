/**
 * Global Test Setup
 * 
 * Sets up the test environment before running any tests,
 * including database seeding, service mocking, and environment preparation.
 */

import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs/promises';
import path from 'path';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  // Create test directories
  await createTestDirectories();

  // Setup test database/storage
  await setupTestStorage();

  // Start mock services if needed
  await startMockServices();

  // Prepare test data
  await prepareTestData();

  // Setup browser for authentication if needed
  await setupAuthentication(config);

  console.log('✅ Global test setup completed');
}

async function createTestDirectories() {
  const directories = [
    'test-results',
    'test-results/screenshots',
    'test-results/videos',
    'test-results/traces',
    'test-results/coverage',
    'test-results/artifacts',
    'test-results/e2e-artifacts',
  ];

  for (const dir of directories) {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      console.warn(`Failed to create directory ${dir}:`, error);
    }
  }
}

async function setupTestStorage() {
  // Clear any existing test data
  try {
    const testDataPath = path.join(process.cwd(), 'test-data');
    await fs.rm(testDataPath, { recursive: true, force: true });
    await fs.mkdir(testDataPath, { recursive: true });
    
    // Create mock IndexedDB data
    const mockData = {
      analysisResults: [
        {
          id: 'test-analysis-1',
          question: 'What are the benefits of renewable energy?',
          analysis: 'Renewable energy offers environmental benefits, cost savings, and energy independence.',
          style: 'professional',
          model: 'gpt-4',
          timestamp: new Date().toISOString(),
          analysisType: 'pros-cons',
          rating: 8,
          followUpQuestions: [
            'What are the main types of renewable energy?',
            'How cost-effective is renewable energy compared to fossil fuels?'
          ]
        },
        {
          id: 'test-analysis-2',
          question: 'How does artificial intelligence work?',
          analysis: 'AI works through machine learning algorithms that process data to make predictions and decisions.',
          style: 'educational',
          model: 'claude-3',
          timestamp: new Date().toISOString(),
          analysisType: 'deep',
          rating: 9
        }
      ],
      designElements: [
        {
          id: 'test-element-1',
          type: 'text',
          data: { content: 'Sample text element' },
          style: { color: '#000000', fontSize: 16 },
          position: { x: 100, y: 100 },
          size: { width: 200, height: 50 }
        }
      ],
      canvasState: {
        mode: 'design',
        zoom: 1,
        selectedElements: [],
        layers: []
      }
    };

    await fs.writeFile(
      path.join(testDataPath, 'mockData.json'),
      JSON.stringify(mockData, null, 2)
    );
  } catch (error) {
    console.warn('Failed to setup test storage:', error);
  }
}

async function startMockServices() {
  // Start mock API server if needed
  if (process.env.USE_MOCK_API === 'true') {
    console.log('Starting mock API server...');
    
    // This would start a mock server for API endpoints
    // For now, we'll just set environment variables
    process.env.MOCK_API_ENABLED = 'true';
    process.env.MOCK_OPENROUTER_API = 'true';
  }

  // Setup service worker mocks
  process.env.MOCK_SERVICE_WORKER = 'true';
}

async function prepareTestData() {
  // Prepare test fixtures
  const fixtures = {
    users: [
      {
        id: 'test-user-1',
        name: 'Test User',
        email: '<EMAIL>',
        preferences: {
          theme: 'light',
          defaultModel: 'gpt-4',
          autoSave: true
        }
      }
    ],
    templates: [
      {
        id: 'test-template-1',
        name: 'Professional Report',
        category: 'analysis',
        elements: []
      }
    ],
    settings: {
      apiKeys: {
        openrouter: 'test-key-123'
      },
      features: {
        offlineMode: true,
        advancedAnalysis: true,
        designTools: true
      }
    }
  };

  try {
    const fixturesPath = path.join(process.cwd(), 'test-data', 'fixtures.json');
    await fs.writeFile(fixturesPath, JSON.stringify(fixtures, null, 2));
  } catch (error) {
    console.warn('Failed to prepare test data:', error);
  }
}

async function setupAuthentication(config: FullConfig) {
  // Setup authentication state for tests that require it
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Navigate to app and perform any necessary authentication
    const baseURL = config.projects[0].use?.baseURL || 'http://localhost:3000';
    await page.goto(baseURL);

    // Wait for app to load
    await page.waitForLoadState('networkidle');

    // Check if authentication is needed
    const needsAuth = await page.locator('[data-testid="login-required"]').isVisible().catch(() => false);
    
    if (needsAuth) {
      // Perform authentication steps
      await page.fill('[data-testid="username"]', '<EMAIL>');
      await page.fill('[data-testid="password"]', 'testpassword');
      await page.click('[data-testid="login-button"]');
      
      // Wait for authentication to complete
      await page.waitForSelector('[data-testid="user-menu"]');
    }

    // Save authentication state
    await page.context().storageState({ 
      path: path.join(process.cwd(), 'test-data', 'auth.json') 
    });

  } catch (error) {
    console.warn('Authentication setup failed:', error);
  } finally {
    await browser.close();
  }
}

// Environment-specific setup
async function setupEnvironment() {
  const env = process.env.NODE_ENV || 'test';
  switch (env) {
    case 'test':
      // Test environment setup
      process.env.VITE_API_BASE_URL = 'http://localhost:3001';
      process.env.VITE_ENABLE_MOCK_API = 'true';
      break;
    case 'ci':
      // CI environment setup
      process.env.VITE_API_BASE_URL = 'http://localhost:3001';
      process.env.VITE_ENABLE_MOCK_API = 'true';
      process.env.CI = 'true';
      break;
    default:
      console.warn(`Unknown environment: ${env}`);
  }
}

// Performance monitoring setup
async function setupPerformanceMonitoring() {
  // Setup performance monitoring for tests
  if (process.env.MONITOR_PERFORMANCE === 'true') {
    console.log('Performance monitoring enabled');
    
    // This would setup performance monitoring tools
    // For now, just enable timing logs
    process.env.LOG_PERFORMANCE = 'true';
  }
}

// Accessibility testing setup
async function setupAccessibilityTesting() {
  // Setup accessibility testing tools
  if (process.env.TEST_ACCESSIBILITY === 'true') {
    console.log('Accessibility testing enabled');
    
    // This would setup axe-core or other a11y testing tools
    process.env.A11Y_TESTING = 'true';
  }
}

// Visual regression testing setup
async function setupVisualTesting() {
  // Setup visual regression testing
  if (process.env.VISUAL_TESTING === 'true') {
    console.log('Visual regression testing enabled');
    
    // Create baseline screenshots directory
    await fs.mkdir('test-results/visual-baselines', { recursive: true });
    process.env.VISUAL_REGRESSION = 'true';
  }
}

// Cleanup function for graceful shutdown
process.on('SIGINT', async () => {
  console.log('🧹 Cleaning up test environment...');
  
  // Cleanup test data
  try {
    await fs.rm('test-data', { recursive: true, force: true });
  } catch (error) {
    console.warn('Failed to cleanup test data:', error);
  }
  
  process.exit(0);
});

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

export default globalSetup;
