import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  PenTool, 
  MousePointer, 
  <PERSON>pline, 
  RotateCcw, 
  Check, 
  X,
  Settings,
  Zap
} from 'lucide-react';
import { Point, BezierPoint } from '@/types/figma';

interface EnhancedPenToolProps {
  isActive: boolean;
  onComplete?: (points: BezierPoint[]) => void;
  onCancel?: () => void;
}

type PenMode = 'draw' | 'edit' | 'bezier';

export const EnhancedPenTool: React.FC<EnhancedPenToolProps> = ({
  isActive,
  onComplete,
  onCancel,
}) => {
  const [mode, setMode] = useState<PenMode>('draw');
  const [points, setPoints] = useState<BezierPoint[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<Point[]>([]);
  const [smoothing, setSmoothing] = useState(0.5);
  const [showControlPoints, setShowControlPoints] = useState(true);
  const [selectedPointIndex, setSelectedPointIndex] = useState<number | null>(null);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { activeLayerId, addObject } = useFigmaCanvasStore();

  // Reset state when tool becomes inactive
  useEffect(() => {
    if (!isActive) {
      setPoints([]);
      setCurrentPath([]);
      setIsDrawing(false);
      setSelectedPointIndex(null);
    }
  }, [isActive]);

  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isActive) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const point: Point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    if (mode === 'draw') {
      setIsDrawing(true);
      setCurrentPath([point]);
    } else if (mode === 'bezier') {
      // Add Bezier point
      const bezierPoint: BezierPoint = {
        ...point,
        type: points.length === 0 ? 'move' : 'curve',
      };
      setPoints(prev => [...prev, bezierPoint]);
    }
  }, [isActive, mode, points.length]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isActive || !isDrawing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const point: Point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    if (mode === 'draw') {
      setCurrentPath(prev => [...prev, point]);
    }
  }, [isActive, isDrawing, mode]);

  const handleMouseUp = useCallback(() => {
    if (!isActive || !isDrawing) return;

    setIsDrawing(false);

    if (mode === 'draw' && currentPath.length > 1) {
      // Convert path to Bezier points with automatic smoothing
      const bezierPoints = AdvancedVectorTools.pointsToBezierPoints(currentPath, smoothing);
      setPoints(bezierPoints);
      setCurrentPath([]);
      setMode('edit');
    }
  }, [isActive, isDrawing, mode, currentPath, smoothing]);

  const handlePointClick = useCallback((index: number) => {
    setSelectedPointIndex(selectedPointIndex === index ? null : index);
  }, [selectedPointIndex]);

  const updatePointPosition = useCallback((index: number, newPosition: Point) => {
    setPoints(prev => prev.map((point, i) => 
      i === index ? { ...point, ...newPosition } : point
    ));
  }, []);

  const updateControlPoint = useCallback((
    pointIndex: number, 
    controlPointType: 'controlPoint1' | 'controlPoint2', 
    newPosition: Point
  ) => {
    setPoints(prev => prev.map((point, i) => 
      i === pointIndex ? { ...point, [controlPointType]: newPosition } : point
    ));
  }, []);

  const completePath = useCallback(() => {
    if (points.length < 2) return;

    try {
      const newObject = AdvancedVectorTools.createBezierCurve(
        points,
        activeLayerId,
        { strokeWidth: 2, stroke: '#6366f1' }
      );
      
      const objectId = addObject(newObject);
      onComplete?.(points);
      
      // Reset state
      setPoints([]);
      setMode('draw');
      setSelectedPointIndex(null);
    } catch (error) {
      console.error('Failed to create Bezier curve:', error);
    }
  }, [points, activeLayerId, addObject, onComplete]);

  const cancelPath = useCallback(() => {
    setPoints([]);
    setCurrentPath([]);
    setMode('draw');
    setSelectedPointIndex(null);
    onCancel?.();
  }, [onCancel]);

  const simplifyPath = useCallback(() => {
    if (points.length < 3) return;

    const simplePoints = points.map(p => ({ x: p.x, y: p.y }));
    const simplified = AdvancedVectorTools.simplifyPath(simplePoints, 2.0);
    const newBezierPoints = AdvancedVectorTools.pointsToBezierPoints(simplified, smoothing);
    setPoints(newBezierPoints);
  }, [points, smoothing]);

  const renderCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw current path (while drawing)
    if (currentPath.length > 1) {
      ctx.strokeStyle = '#6366f1';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(currentPath[0].x, currentPath[0].y);
      for (let i = 1; i < currentPath.length; i++) {
        ctx.lineTo(currentPath[i].x, currentPath[i].y);
      }
      ctx.stroke();
    }

    // Draw Bezier curve
    if (points.length > 1) {
      ctx.strokeStyle = '#6366f1';
      ctx.lineWidth = 3;
      ctx.beginPath();
      
      ctx.moveTo(points[0].x, points[0].y);
      
      for (let i = 1; i < points.length; i++) {
        const point = points[i];
        const prevPoint = points[i - 1];
        
        if (point.type === 'curve' && prevPoint.controlPoint2 && point.controlPoint1) {
          ctx.bezierCurveTo(
            prevPoint.controlPoint2.x,
            prevPoint.controlPoint2.y,
            point.controlPoint1.x,
            point.controlPoint1.y,
            point.x,
            point.y
          );
        } else {
          ctx.lineTo(point.x, point.y);
        }
      }
      
      ctx.stroke();

      // Draw control points if enabled
      if (showControlPoints) {
        points.forEach((point, index) => {
          // Draw main point
          ctx.fillStyle = selectedPointIndex === index ? '#ef4444' : '#6366f1';
          ctx.beginPath();
          ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);
          ctx.fill();

          // Draw control points
          if (point.controlPoint1) {
            ctx.strokeStyle = '#94a3b8';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(point.controlPoint1.x, point.controlPoint1.y);
            ctx.stroke();

            ctx.fillStyle = '#94a3b8';
            ctx.beginPath();
            ctx.arc(point.controlPoint1.x, point.controlPoint1.y, 3, 0, 2 * Math.PI);
            ctx.fill();
          }

          if (point.controlPoint2) {
            ctx.strokeStyle = '#94a3b8';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(point.controlPoint2.x, point.controlPoint2.y);
            ctx.stroke();

            ctx.fillStyle = '#94a3b8';
            ctx.beginPath();
            ctx.arc(point.controlPoint2.x, point.controlPoint2.y, 3, 0, 2 * Math.PI);
            ctx.fill();
          }
        });
      }
    }
  };

  // Re-render canvas when state changes
  useEffect(() => {
    renderCanvas();
  }, [points, currentPath, showControlPoints, selectedPointIndex]);

  if (!isActive) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <PenTool className="h-5 w-5" />
              Enhanced Pen Tool
              <Badge variant="secondary">{mode}</Badge>
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setMode(mode === 'draw' ? 'bezier' : 'draw')}
              >
                {mode === 'draw' ? <Spline className="h-4 w-4" /> : <PenTool className="h-4 w-4" />}
                {mode === 'draw' ? 'Bezier Mode' : 'Draw Mode'}
              </Button>
              <Button variant="outline" size="sm" onClick={cancelPath}>
                <X className="h-4 w-4" />
                Cancel
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex gap-4">
          {/* Canvas Area */}
          <div className="flex-1 border rounded-lg overflow-hidden">
            <canvas
              ref={canvasRef}
              width={800}
              height={600}
              className="w-full h-full cursor-crosshair"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
            />
          </div>

          {/* Controls Panel */}
          <div className="w-64 space-y-4">
            <div>
              <label className="text-sm font-medium">Smoothing</label>
              <Slider
                value={[smoothing]}
                onValueChange={([value]) => setSmoothing(value)}
                min={0}
                max={1}
                step={0.1}
                className="mt-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {(smoothing * 100).toFixed(0)}%
              </div>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="showControlPoints"
                checked={showControlPoints}
                onChange={(e) => setShowControlPoints(e.target.checked)}
              />
              <label htmlFor="showControlPoints" className="text-sm">
                Show Control Points
              </label>
            </div>

            {points.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium">
                  Points: {points.length}
                </div>
                
                <div className="flex gap-2">
                  <Button size="sm" onClick={simplifyPath} disabled={points.length < 3}>
                    <Zap className="h-4 w-4" />
                    Simplify
                  </Button>
                </div>

                <Button 
                  onClick={completePath} 
                  disabled={points.length < 2}
                  className="w-full"
                >
                  <Check className="h-4 w-4 mr-2" />
                  Complete Path
                </Button>
              </div>
            )}

            <div className="text-xs text-muted-foreground">
              <p><strong>Draw Mode:</strong> Click and drag to draw</p>
              <p><strong>Bezier Mode:</strong> Click to add control points</p>
              <p>Use Shift+Click for straight lines</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
