/**
 * Theme Provider System
 * 
 * Provides theme context and utilities for consistent theming across the application.
 * Integrates with next-themes for dark/light mode support.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useTheme as useNextTheme } from 'next-themes';
import { designTokens, type DesignTokens } from './tokens';

// Theme configuration
export interface ThemeConfig {
  tokens: DesignTokens;
  mode: 'light' | 'dark' | 'system';
  reducedMotion: boolean;
  highContrast: boolean;
}

// Theme context
interface ThemeContextValue {
  config: ThemeConfig;
  tokens: DesignTokens;
  mode: 'light' | 'dark';
  setMode: (mode: 'light' | 'dark' | 'system') => void;
  toggleMode: () => void;
  setReducedMotion: (reduced: boolean) => void;
  setHighContrast: (highContrast: boolean) => void;
  // Utility functions
  getColor: (path: string) => string;
  getSpacing: (size: keyof typeof designTokens.spacing) => string;
  getTypography: (property: string) => string;
  getShadow: (size: keyof typeof designTokens.shadows) => string;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

// Theme provider component
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultMode?: 'light' | 'dark' | 'system';
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultMode = 'system',
  storageKey = 'app-theme',
}) => {
  const { theme, setTheme, resolvedTheme } = useNextTheme();
  const [reducedMotion, setReducedMotion] = useState(false);
  const [highContrast, setHighContrast] = useState(false);

  // Detect user preferences
  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Create theme configuration
  const config: ThemeConfig = {
    tokens: designTokens,
    mode: (resolvedTheme as 'light' | 'dark') || 'light',
    reducedMotion,
    highContrast,
  };

  // Utility functions
  const getColor = (path: string): string => {
    const keys = path.split('.');
    let value: unknown = designTokens.colors;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        console.warn(`Color path "${path}" not found in design tokens`);
        return designTokens.colors.neutral[500];
      }
    }
    
    return typeof value === 'string' ? value : designTokens.colors.neutral[500];
  };

  const getSpacing = (size: keyof typeof designTokens.spacing): string => {
    return designTokens.spacing[size] || designTokens.spacing[4];
  };

  const getTypography = (property: string): string => {
    const keys = property.split('.');
    let value: unknown = designTokens.typography;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        console.warn(`Typography property "${property}" not found in design tokens`);
        return '';
      }
    }
    
    return typeof value === 'string' ? value : '';
  };

  const getShadow = (size: keyof typeof designTokens.shadows): string => {
    return designTokens.shadows[size] || designTokens.shadows.base;
  };

  const toggleMode = () => {
    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark');
  };

  const contextValue: ThemeContextValue = {
    config,
    tokens: designTokens,
    mode: (resolvedTheme as 'light' | 'dark') || 'light',
    setMode: setTheme,
    toggleMode,
    setReducedMotion,
    setHighContrast,
    getColor,
    getSpacing,
    getTypography,
    getShadow,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useAppTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useAppTheme must be used within a ThemeProvider');
  }
  return context;
};

// Utility hook for responsive values
export const useResponsiveValue = <T,>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}): T | undefined => {
  const [currentValue, setCurrentValue] = useState<T | undefined>();

  useEffect(() => {
    const updateValue = () => {
      const width = window.innerWidth;
      
      if (width >= 1536 && values['2xl']) {
        setCurrentValue(values['2xl']);
      } else if (width >= 1280 && values.xl) {
        setCurrentValue(values.xl);
      } else if (width >= 1024 && values.lg) {
        setCurrentValue(values.lg);
      } else if (width >= 768 && values.md) {
        setCurrentValue(values.md);
      } else if (width >= 640 && values.sm) {
        setCurrentValue(values.sm);
      } else if (values.xs) {
        setCurrentValue(values.xs);
      }
    };

    updateValue();
    window.addEventListener('resize', updateValue);
    return () => window.removeEventListener('resize', updateValue);
  }, [values]);

  return currentValue;
};

// CSS-in-JS style generator
export const createStyles = (theme: ThemeContextValue) => ({
  // Common style patterns
  card: {
    backgroundColor: theme.getColor('neutral.0'),
    borderRadius: theme.tokens.borderRadius.lg,
    boxShadow: theme.getShadow('base'),
    border: `1px solid ${theme.getColor('neutral.200')}`,
    padding: theme.getSpacing(6),
  },
  
  button: {
    primary: {
      backgroundColor: theme.getColor('primary.500'),
      color: theme.getColor('neutral.0'),
      borderRadius: theme.tokens.borderRadius.md,
      padding: `${theme.getSpacing(2)} ${theme.getSpacing(4)}`,
      fontSize: theme.getTypography('fontSize.base'),
      fontWeight: theme.getTypography('fontWeight.medium'),
      border: 'none',
      cursor: 'pointer',
      transition: `all ${theme.tokens.animations.duration.normal} ${theme.tokens.animations.easing.easeInOut}`,
      '&:hover': {
        backgroundColor: theme.getColor('primary.600'),
        transform: 'translateY(-1px)',
        boxShadow: theme.getShadow('md'),
      },
      '&:active': {
        transform: 'translateY(0)',
      },
    },
    
    secondary: {
      backgroundColor: theme.getColor('neutral.100'),
      color: theme.getColor('neutral.900'),
      borderRadius: theme.tokens.borderRadius.md,
      padding: `${theme.getSpacing(2)} ${theme.getSpacing(4)}`,
      fontSize: theme.getTypography('fontSize.base'),
      fontWeight: theme.getTypography('fontWeight.medium'),
      border: `1px solid ${theme.getColor('neutral.300')}`,
      cursor: 'pointer',
      transition: `all ${theme.tokens.animations.duration.normal} ${theme.tokens.animations.easing.easeInOut}`,
      '&:hover': {
        backgroundColor: theme.getColor('neutral.200'),
        borderColor: theme.getColor('neutral.400'),
      },
    },
  },
  
  input: {
    backgroundColor: theme.getColor('neutral.0'),
    border: `1px solid ${theme.getColor('neutral.300')}`,
    borderRadius: theme.tokens.borderRadius.md,
    padding: `${theme.getSpacing(2)} ${theme.getSpacing(3)}`,
    fontSize: theme.getTypography('fontSize.base'),
    color: theme.getColor('neutral.900'),
    transition: `all ${theme.tokens.animations.duration.normal} ${theme.tokens.animations.easing.easeInOut}`,
    '&:focus': {
      outline: 'none',
      borderColor: theme.getColor('primary.500'),
      boxShadow: `0 0 0 3px ${theme.getColor('primary.100')}`,
    },
  },
  
  text: {
    heading: {
      fontSize: theme.getTypography('fontSize.2xl'),
      fontWeight: theme.getTypography('fontWeight.bold'),
      color: theme.getColor('neutral.900'),
      lineHeight: theme.getTypography('lineHeight.tight'),
    },
    
    body: {
      fontSize: theme.getTypography('fontSize.base'),
      fontWeight: theme.getTypography('fontWeight.normal'),
      color: theme.getColor('neutral.700'),
      lineHeight: theme.getTypography('lineHeight.normal'),
    },
    
    caption: {
      fontSize: theme.getTypography('fontSize.sm'),
      fontWeight: theme.getTypography('fontWeight.normal'),
      color: theme.getColor('neutral.500'),
      lineHeight: theme.getTypography('lineHeight.normal'),
    },
  },
});

// Export types
export type { ThemeConfig, ThemeContextValue };
