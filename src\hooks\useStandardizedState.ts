/**
 * Standardized State Management Hooks
 * 
 * Provides consistent patterns for state management across the application,
 * including loading states, error handling, data fetching, and form management.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';

// Standard async state interface
export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

// Standard form state interface
export interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isValid: boolean;
  isSubmitting: boolean;
  isDirty: boolean;
}

// Standard pagination state interface
export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Standard filter state interface
export interface FilterState<T> {
  filters: T;
  search: string;
  sortBy: string | null;
  sortOrder: 'asc' | 'desc';
}

// Hook for managing async operations with consistent loading/error states
export const useAsyncState = <T>(
  initialData: T | null = null
): [
  AsyncState<T>,
  {
    setData: (data: T | null) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    reset: () => void;
    execute: (asyncFn: () => Promise<T>) => Promise<T | null>;
  }
] => {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  const { toast } = useToast();

  const setData = useCallback((data: T | null) => {
    setState(prev => ({
      ...prev,
      data,
      error: null,
      lastUpdated: Date.now(),
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, loading: false }));
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
    }
  }, [toast]);

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, [initialData]);

  const execute = useCallback(async (asyncFn: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      const result = await asyncFn();
      setData(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [setData, setError, setLoading]);

  return [state, { setData, setLoading, setError, reset, execute }];
};

// Hook for managing form state with validation
export const useFormState = <T extends Record<string, unknown>>(
  initialValues: T,
  validationRules?: Partial<Record<keyof T, (value: unknown) => string | null>>
): [
  FormState<T>,
  {
    setValue: (field: keyof T, value: unknown) => void;
    setValues: (values: Partial<T>) => void;
    setError: (field: keyof T, error: string | null) => void;
    setTouched: (field: keyof T, touched: boolean) => void;
    validate: (field?: keyof T) => boolean;
    reset: () => void;
    submit: (onSubmit: (values: T) => Promise<void>) => Promise<boolean>;
  }
] => {
  const [state, setState] = useState<FormState<T>>({
    values: initialValues,
    errors: {},
    touched: {},
    isValid: true,
    isSubmitting: false,
    isDirty: false,
  });

  const { toast } = useToast();

  const setValue = useCallback((field: keyof T, value: unknown) => {
    setState(prev => {
      const newValues = { ...prev.values, [field]: value };
      const isDirty = JSON.stringify(newValues) !== JSON.stringify(initialValues);
      
      // Validate the field if it has been touched
      const newErrors = { ...prev.errors };
      if (prev.touched[field] && validationRules?.[field]) {
        const error = validationRules[field]!(value);
        if (error) {
          newErrors[field] = error;
        } else {
          delete newErrors[field];
        }
      }

      const isValid = Object.keys(newErrors).length === 0;

      return {
        ...prev,
        values: newValues,
        errors: newErrors,
        isValid,
        isDirty,
      };
    });
  }, [initialValues, validationRules]);

  const setValues = useCallback((values: Partial<T>) => {
    setState(prev => ({
      ...prev,
      values: { ...prev.values, ...values },
      isDirty: true,
    }));
  }, []);

  const setError = useCallback((field: keyof T, error: string | null) => {
    setState(prev => {
      const newErrors = { ...prev.errors };
      if (error) {
        newErrors[field] = error;
      } else {
        delete newErrors[field];
      }
      return {
        ...prev,
        errors: newErrors,
        isValid: Object.keys(newErrors).length === 0,
      };
    });
  }, []);

  const setTouched = useCallback((field: keyof T, touched: boolean) => {
    setState(prev => ({
      ...prev,
      touched: { ...prev.touched, [field]: touched },
    }));
  }, []);

  const validate = useCallback((field?: keyof T): boolean => {
    if (!validationRules) return true;

    const fieldsToValidate = field ? [field] : Object.keys(validationRules) as (keyof T)[];
    let hasErrors = false;

    setState(prev => {
      const newErrors = { ...prev.errors };

      fieldsToValidate.forEach(fieldName => {
        const rule = validationRules[fieldName];
        if (rule) {
          const error = rule(prev.values[fieldName]);
          if (error) {
            newErrors[fieldName] = error;
            hasErrors = true;
          } else {
            delete newErrors[fieldName];
          }
        }
      });

      return {
        ...prev,
        errors: newErrors,
        isValid: Object.keys(newErrors).length === 0,
      };
    });

    return !hasErrors;
  }, [validationRules]);

  const reset = useCallback(() => {
    setState({
      values: initialValues,
      errors: {},
      touched: {},
      isValid: true,
      isSubmitting: false,
      isDirty: false,
    });
  }, [initialValues]);

  const submit = useCallback(async (onSubmit: (values: T) => Promise<void>): Promise<boolean> => {
    // Validate all fields
    const isValid = validate();
    if (!isValid) {
      toast({
        title: 'Validation Error',
        description: 'Please fix the errors before submitting',
        variant: 'destructive',
      });
      return false;
    }

    setState(prev => ({ ...prev, isSubmitting: true }));

    try {
      await onSubmit(state.values);
      toast({
        title: 'Success',
        description: 'Form submitted successfully',
      });
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Submission failed';
      toast({
        title: 'Submission Error',
        description: errorMessage,
        variant: 'destructive',
      });
      return false;
    } finally {
      setState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [state.values, validate, toast]);

  return [state, { setValue, setValues, setError, setTouched, validate, reset, submit }];
};

// Hook for managing pagination state
export const usePaginationState = (
  initialPageSize: number = 10
): [
  PaginationState,
  {
    setPage: (page: number) => void;
    setPageSize: (pageSize: number) => void;
    setTotal: (total: number) => void;
    nextPage: () => void;
    previousPage: () => void;
    reset: () => void;
  }
] => {
  const [state, setState] = useState<PaginationState>({
    page: 1,
    pageSize: initialPageSize,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrevious: false,
  });

  const updatePaginationState = useCallback((updates: Partial<PaginationState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      newState.totalPages = Math.ceil(newState.total / newState.pageSize);
      newState.hasNext = newState.page < newState.totalPages;
      newState.hasPrevious = newState.page > 1;
      return newState;
    });
  }, []);

  const setPage = useCallback((page: number) => {
    updatePaginationState({ page });
  }, [updatePaginationState]);

  const setPageSize = useCallback((pageSize: number) => {
    updatePaginationState({ pageSize, page: 1 });
  }, [updatePaginationState]);

  const setTotal = useCallback((total: number) => {
    updatePaginationState({ total });
  }, [updatePaginationState]);

  const nextPage = useCallback(() => {
    if (state.hasNext) {
      setPage(state.page + 1);
    }
  }, [state.hasNext, state.page, setPage]);

  const previousPage = useCallback(() => {
    if (state.hasPrevious) {
      setPage(state.page - 1);
    }
  }, [state.hasPrevious, state.page, setPage]);

  const reset = useCallback(() => {
    setState({
      page: 1,
      pageSize: initialPageSize,
      total: 0,
      totalPages: 0,
      hasNext: false,
      hasPrevious: false,
    });
  }, [initialPageSize]);

  return [state, { setPage, setPageSize, setTotal, nextPage, previousPage, reset }];
};

// Hook for managing filter state
export const useFilterState = <T extends Record<string, unknown>>(
  initialFilters: T
): [
  FilterState<T>,
  {
    setFilter: (key: keyof T, value: unknown) => void;
    setFilters: (filters: Partial<T>) => void;
    setSearch: (search: string) => void;
    setSort: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
    clearFilters: () => void;
    reset: () => void;
  }
] => {
  const [state, setState] = useState<FilterState<T>>({
    filters: initialFilters,
    search: '',
    sortBy: null,
    sortOrder: 'asc',
  });

  const setFilter = useCallback((key: keyof T, value: unknown) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, [key]: value },
    }));
  }, []);

  const setFilters = useCallback((filters: Partial<T>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filters },
    }));
  }, []);

  const setSearch = useCallback((search: string) => {
    setState(prev => ({ ...prev, search }));
  }, []);

  const setSort = useCallback((sortBy: string, sortOrder: 'asc' | 'desc') => {
    setState(prev => ({ ...prev, sortBy, sortOrder }));
  }, []);

  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: initialFilters,
      search: '',
    }));
  }, [initialFilters]);

  const reset = useCallback(() => {
    setState({
      filters: initialFilters,
      search: '',
      sortBy: null,
      sortOrder: 'asc',
    });
  }, [initialFilters]);

  return [state, { setFilter, setFilters, setSearch, setSort, clearFilters, reset }];
};

// Hook for debounced values
export const useDebounced = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Hook for managing local storage state
export const useLocalStorageState = <T>(
  key: string,
  initialValue: T
): [T, (value: T | ((prev: T) => T)) => void] => {
  const [state, setState] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(state) : value;
      setState(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, state]);

  return [state, setValue];
};
