/**
 * Design System Components
 * 
 * Centralized export of all design system components.
 * These components use the unified design tokens and variant system.
 */

// Core components (only export what exists)
export { ButtonGroup, IconButton, FAB } from './Button';
export {
  StatsCard,
  FeatureCard,
  TestimonialCard
} from './Card';

// Re-export design system components with distinct names to avoid conflicts
export { Button as DSButton } from './Button';
export { 
  Card as DSCard,
  CardHeader as DSCardHeader,
  CardContent as DSCard<PERSON>ontent,
  Card<PERSON>ooter as DSCardFooter
} from './Card';

// Types
export type {
  ButtonProps,
} from './Button';

export type {
  CardProps as DSCardProps,
  CardHeaderProps as DSCardHeaderProps,
} from './Card';

// Note: Additional components will be added as they are implemented
// This ensures the build doesn't fail on missing components
