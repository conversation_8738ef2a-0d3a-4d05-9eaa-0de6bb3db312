import { ChainConnection, ChainNode } from '@/types/visualization';

interface ChainConnectorProps {
  connection: ChainConnection;
  fromNode: ChainNode;
  toNode: ChainNode;
  nodePositions: Map<string, { x: number; y: number; width: number; height: number }>;
  className?: string;
}

const connectionTypeConfig = {
  flow: {
    color: '#3b82f6', // blue-500
    width: 2,
    dashArray: undefined,
    animated: true
  },
  branch: {
    color: '#f59e0b', // amber-500
    width: 2,
    dashArray: '5,5',
    animated: false
  },
  merge: {
    color: '#8b5cf6', // violet-500
    width: 2,
    dashArray: undefined,
    animated: false
  },
  feedback: {
    color: '#ef4444', // red-500
    width: 1,
    dashArray: '3,3',
    animated: true
  }
};

export const ChainConnector: React.FC<ChainConnectorProps> = ({
  connection,
  fromNode,
  toNode,
  nodePositions,
  className
}) => {
  const fromPos = nodePositions.get(connection.from);
  const toPos = nodePositions.get(connection.to);

  if (!fromPos || !toPos) {
    return null;
  }

  const typeConfig = connectionTypeConfig[connection.type];
  const color = connection.style?.color || typeConfig.color;
  const width = connection.style?.width || typeConfig.width;
  const dashArray = connection.style?.dashArray || typeConfig.dashArray;
  const animated = connection.animated ?? typeConfig.animated;

  // Calculate connection points
  const startX = fromPos.x + fromPos.width;
  const startY = fromPos.y + fromPos.height / 2;
  const endX = toPos.x;
  const endY = toPos.y + toPos.height / 2;

  // Calculate control points for curved path
  const controlPointOffset = Math.abs(endX - startX) * 0.5;
  const controlPoint1X = startX + controlPointOffset;
  const controlPoint1Y = startY;
  const controlPoint2X = endX - controlPointOffset;
  const controlPoint2Y = endY;

  // Create SVG path
  const pathData = `M ${startX} ${startY} C ${controlPoint1X} ${controlPoint1Y}, ${controlPoint2X} ${controlPoint2Y}, ${endX} ${endY}`;

  // Calculate arrow position and rotation
  const arrowX = endX - 8;
  const arrowY = endY;
  const angle = Math.atan2(endY - controlPoint2Y, endX - controlPoint2X) * (180 / Math.PI);

  return (
    <svg
      className={cn("absolute inset-0 pointer-events-none", className)}
      style={{ zIndex: 1 }}
    >
      <defs>
        <marker
          id={`arrowhead-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={color}
          />
        </marker>
        
        {animated && (
          <linearGradient id={`gradient-${connection.id}`} x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={color} stopOpacity="0.3" />
            <stop offset="50%" stopColor={color} stopOpacity="1" />
            <stop offset="100%" stopColor={color} stopOpacity="0.3" />
          </linearGradient>
        )}
      </defs>
      
      {/* Main connection path */}
      <motion.path
        d={pathData}
        stroke={animated ? `url(#gradient-${connection.id})` : color}
        strokeWidth={width}
        strokeDasharray={dashArray}
        fill="none"
        markerEnd={`url(#arrowhead-${connection.id})`}
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ 
          duration: 0.8,
          delay: 0.2,
          ease: "easeInOut"
        }}
      />
      
      {/* Animated flow indicator */}
      {animated && (
        <motion.circle
          r="3"
          fill={color}
          initial={{ offsetDistance: "0%" }}
          animate={{ offsetDistance: "100%" }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{
            offsetPath: `path('${pathData}')`,
            offsetRotate: "0deg"
          }}
        />
      )}
      
      {/* Connection label (if needed) */}
      {connection.type === 'branch' && (
        <text
          x={(startX + endX) / 2}
          y={(startY + endY) / 2 - 10}
          textAnchor="middle"
          className="text-xs fill-gray-500 font-medium"
        >
          Branch
        </text>
      )}
      
      {connection.type === 'feedback' && (
        <text
          x={(startX + endX) / 2}
          y={(startY + endY) / 2 - 10}
          textAnchor="middle"
          className="text-xs fill-red-500 font-medium"
        >
          Feedback
        </text>
      )}
    </svg>
  );
};

// Helper component for straight line connections (simpler alternative)
export const StraightConnector: React.FC<ChainConnectorProps> = ({
  connection,
  nodePositions,
  className
}) => {
  const fromPos = nodePositions.get(connection.from);
  const toPos = nodePositions.get(connection.to);

  if (!fromPos || !toPos) {
    return null;
  }

  const typeConfig = connectionTypeConfig[connection.type];
  const color = connection.style?.color || typeConfig.color;
  const width = connection.style?.width || typeConfig.width;
  const dashArray = connection.style?.dashArray || typeConfig.dashArray;

  const startX = fromPos.x + fromPos.width;
  const startY = fromPos.y + fromPos.height / 2;
  const endX = toPos.x;
  const endY = toPos.y + toPos.height / 2;

  return (
    <svg
      className={cn("absolute inset-0 pointer-events-none", className)}
      style={{ zIndex: 1 }}
    >
      <defs>
        <marker
          id={`arrowhead-straight-${connection.id}`}
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={color}
          />
        </marker>
      </defs>
      
      <motion.line
        x1={startX}
        y1={startY}
        x2={endX}
        y2={endY}
        stroke={color}
        strokeWidth={width}
        strokeDasharray={dashArray}
        markerEnd={`url(#arrowhead-straight-${connection.id})`}
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ 
          duration: 0.5,
          delay: 0.1,
          ease: "easeInOut"
        }}
      />
    </svg>
  );
};
