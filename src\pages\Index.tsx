
import { Suspense, lazy, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useNavigationStore } from "@/stores/useNavigationStore";
import { TourManager } from "@/components/TourManager";
import { TabLoader } from "@/components/ui/loading-fallback";
import { useSmartPreloader } from "@/hooks/useChunkPreloader";
import { CanvasNavigationHelper, CanvasQuickSwitch } from "@/components/navigation/CanvasNavigationHelper";
import { useCanvasDataProvider } from "@/hooks/useCanvasDataBridge";
import { useLocation, useNavigate } from "react-router-dom";
import { ErrorBoundaryEnhanced } from "@/components/ErrorBoundaryEnhanced";
import { useComponentErrorTracking } from "@/hooks/useErrorBoundary";
import { ErrorTestComponent } from "@/components/ErrorTestComponent";

// Lazy load heavy components
const ConversationPlanner = lazy(() =>
  import("@/components/ConversationPlanner").then(module => ({
    default: module.ConversationPlanner
  }))
);
const Library = lazy(() =>
  import("@/components/Library").then(module => ({
    default: module.Library
  }))
);
const IntegratedCanvasContainer = lazy(() =>
  import("@/components/visual-analysis/IntegratedCanvasContainer").then(module => ({
    default: module.IntegratedCanvasContainer
  }))
);

export default function Index() {
  console.log('📄 Index component rendering...');

  const location = useLocation();
  const navigate = useNavigate();
  const { mainTab, setMainTab } = useNavigationStore();
  const { preloadLibrary } = useSmartPreloader();
  const { trackLifecycleError, trackRenderError } = useComponentErrorTracking('Index');

  console.log('📊 Index state:', { mainTab });
  console.log('🧭 Index location:', location);
  console.log('🧭 Index navigate function:', typeof navigate);

  // Initialize canvas data bridge
  useCanvasDataProvider();

  // Preload components when user switches tabs
  useEffect(() => {
    console.log('⚡ Index useEffect - mainTab changed:', mainTab);
    if (mainTab === 'insights') {
      console.log('📚 Preloading library...');
      preloadLibrary();
    } else if (mainTab === 'canvas') {
      console.log('🧠 Preloading canvas components...');
      // Preload canvas components
      import("@/components/visual-analysis/IntegratedCanvasContainer");
    }
  }, [mainTab, preloadLibrary]);
  return (
    <div className="min-h-screen bg-background text-foreground">
      <ErrorBoundaryEnhanced context="Tour Manager" level="component">
        <TourManager />
      </ErrorBoundaryEnhanced>

      {/* Header Section - Fixed positioning */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary-hover to-purple-600">
              AI Question Analyzer
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
              Get detailed analysis and historical context for your questions with AI-powered insights
            </p>
          </div>
        </div>
      </header>

      {/* Canvas Navigation Helper */}
      <ErrorBoundaryEnhanced context="Canvas Navigation" level="component">
        <CanvasNavigationHelper />
      </ErrorBoundaryEnhanced>

      {/* Navigation Test Panel - Development Only */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          padding: '10px',
          borderRadius: '8px',
          fontSize: '12px',
          zIndex: 9999
        }}>
          <div>🧭 Navigation Test</div>
          <div>Current: {location.pathname}</div>
          <div>Tab: {mainTab}</div>
          <button
            onClick={() => navigate('/canvas')}
            style={{ margin: '2px', padding: '4px 8px', fontSize: '10px' }}
          >
            Go to Canvas
          </button>
          <button
            onClick={() => navigate('/canvas-safe')}
            style={{ margin: '2px', padding: '4px 8px', fontSize: '10px' }}
          >
            Go to Safe Canvas
          </button>
          <button
            onClick={() => navigate('/')}
            style={{ margin: '2px', padding: '4px 8px', fontSize: '10px' }}
          >
            Go to Home
          </button>
        </div>
      )}

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as 'analyze' | 'insights' | 'canvas')} className="w-full">
            <div className="flex justify-center mb-8 px-2">
              <TabsList className="grid w-full max-w-4xl grid-cols-3 bg-muted/60 backdrop-blur-sm p-1 rounded-xl border border-border shadow-lg">
                <TabsTrigger
                  value="analyze"
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md rounded-lg py-4 px-3 sm:px-6 text-muted-foreground hover:text-foreground transition-all duration-200 font-semibold text-sm sm:text-base whitespace-nowrap overflow-hidden text-ellipsis"
                >
                  <span className="hidden sm:inline">🔍 Conversation Suite</span>
                  <span className="sm:hidden">🔍 Suite</span>
                </TabsTrigger>
                <TabsTrigger
                  value="insights"
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-md rounded-lg py-4 px-3 sm:px-6 text-muted-foreground hover:text-foreground transition-all duration-200 font-semibold text-sm sm:text-base whitespace-nowrap overflow-hidden text-ellipsis"
                >
                  <span className="hidden sm:inline">📚 Historical Analysis</span>
                  <span className="sm:hidden">📚 Analysis</span>
                </TabsTrigger>
                <TabsTrigger
                  value="canvas"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-md rounded-lg py-4 px-3 sm:px-6 text-muted-foreground hover:text-foreground transition-all duration-200 font-semibold text-sm sm:text-base whitespace-nowrap overflow-hidden text-ellipsis"
                >
                  <span className="hidden sm:inline">🧠 Visual Canvas</span>
                  <span className="sm:hidden">🧠 Canvas</span>
                </TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="analyze" className="mt-0 animate-in fade-in-50 duration-300">
              <ErrorBoundaryEnhanced context="Conversation Planner Tab" level="feature">
                <Suspense fallback={<TabLoader />}>
                  <ConversationPlanner />
                </Suspense>
              </ErrorBoundaryEnhanced>
            </TabsContent>

            <TabsContent value="insights" className="mt-0 animate-in fade-in-50 duration-300">
              <ErrorBoundaryEnhanced context="Library Tab" level="feature">
                <Suspense fallback={<TabLoader />}>
                  <Library showHistoryOnly={true} />
                </Suspense>
              </ErrorBoundaryEnhanced>
            </TabsContent>

            <TabsContent value="canvas" className="mt-0 animate-in fade-in-50 duration-300">
              <ErrorBoundaryEnhanced context="Canvas Tab" level="feature">
                <Suspense fallback={<TabLoader />}>
                  <IntegratedCanvasContainer />
                </Suspense>
              </ErrorBoundaryEnhanced>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {/* Canvas Quick Switch */}
      <ErrorBoundaryEnhanced context="Canvas Quick Switch" level="component">
        <CanvasQuickSwitch />
      </ErrorBoundaryEnhanced>

      {/* Error Test Component - Development Only */}
      <ErrorBoundaryEnhanced context="Error Test Component" level="component">
        <ErrorTestComponent />
      </ErrorBoundaryEnhanced>
    </div>
  );
}
