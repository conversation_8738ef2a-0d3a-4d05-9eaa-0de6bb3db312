import React from 'react';
import { ParsedProsConsAnalysis } from '@/services/openRouter/types/parsedOutput';

interface ProsConsDisplayProps {
  analysis: ParsedProsConsAnalysis;
}

export const ProsConsDisplay: React.FC<ProsConsDisplayProps> = ({ analysis }) => {
  return (
    <div>
      <h3>Pros & Cons Analysis</h3>
      {analysis.summary && <p><strong>Summary:</strong> {analysis.summary}</p>}
      <h4>Pros:</h4>
      <ul>
        {analysis.pros.map((pro, index) => <li key={`pro-${index}`}>{pro}</li>)}
      </ul>
      <h4>Cons:</h4>
      <ul>
        {analysis.cons.map((con, index) => <li key={`con-${index}`}>{con}</li>)}
      </ul>
      {analysis.warning && <p style={{ color: 'orange' }}>Warning: {analysis.warning}</p>}
    </div>
  );
};
