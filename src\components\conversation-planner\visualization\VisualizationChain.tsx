import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { VisualizationChain as VisualizationChainType, ChainNode as ChainNodeType } from '@/types/visualization';
import { ChainConnector } from './ChainConnector';
import { ChainNode } from './ChainNode';
import { InteractiveOverlay } from './InteractiveOverlay';
import { cn } from '@/lib/utils';

interface VisualizationChainProps {
  chain: VisualizationChainType;
  onNodeClick?: (nodeId: string) => void;
  onNodeSelect?: (nodeId: string) => void;
  selectedNodeId?: string;
  className?: string;
  compact?: boolean;
  interactive?: boolean;
  showInteractiveOverlay?: boolean;
  enableAnimations?: boolean;
}

interface NodePosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const VisualizationChain: React.FC<VisualizationChainProps> = ({
  chain,
  onNodeClick,
  onNodeSelect,
  selectedNodeId,
  className,
  compact = false,
  interactive = true,
  showInteractiveOverlay = true,
  enableAnimations = true
}) => {
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [nodePositions, setNodePositions] = useState<Map<string, NodePosition>>(new Map());
  const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const nodeRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // Calculate layout positions based on chain layout type
  const calculateLayout = (nodes: ChainNodeType[]) => {
    const positions = new Map<string, { x: number; y: number }>();
    const nodeSpacing = compact ? 250 : 320;
    const levelSpacing = compact ? 120 : 150;

    switch (chain.layout) {
      case 'horizontal':
        nodes.forEach((node, index) => {
          const level = node.position.x;
          const offset = node.position.y;
          positions.set(node.id, {
            x: level * nodeSpacing,
            y: offset * levelSpacing + 200 // Center vertically
          });
        });
        break;
      case 'vertical':
        nodes.forEach((node, index) => {
          const level = node.position.y;
          const offset = node.position.x;
          positions.set(node.id, {
            x: offset * nodeSpacing + 200,
            y: level * levelSpacing
          });
        });
        break;
      case 'tree': {
        // Simple tree layout - can be enhanced
        const levels = new Map<number, ChainNodeType[]>();
        nodes.forEach(node => {
          const level = node.position.x;
          if (!levels.has(level)) levels.set(level, []);
          levels.get(level)!.push(node);
        });
        levels.forEach((levelNodes, level) => {
          levelNodes.forEach((node, index) => {
            const totalNodes = levelNodes.length;
            const startY = -(totalNodes - 1) * levelSpacing / 2;
            positions.set(node.id, {
              x: level * nodeSpacing,
              y: startY + index * levelSpacing + 200
            });
          });
        });
        break;
      }
      case 'circular': {
        const centerX = 300;
        const centerY = 200;
        const radius = 150;
        nodes.forEach((node, index) => {
          const angle = (index / nodes.length) * 2 * Math.PI;
          positions.set(node.id, {
            x: centerX + Math.cos(angle) * radius,
            y: centerY + Math.sin(angle) * radius
          });
        });
        break;
      }
      default:
        // Default to horizontal
        nodes.forEach((node, index) => {
          positions.set(node.id, {
            x: index * nodeSpacing,
            y: 200
          });
        });
    }

    return positions;
  };

  const layoutPositions = calculateLayout(chain.nodes);

  // Update node positions when refs change
  useEffect(() => {
    const updatePositions = () => {
      const newPositions = new Map<string, NodePosition>();
      
      nodeRefs.current.forEach((element, nodeId) => {
        if (element) {
          const rect = element.getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();
          
          if (containerRect) {
            const layoutPos = layoutPositions.get(nodeId);
            if (layoutPos) {
              newPositions.set(nodeId, {
                x: layoutPos.x,
                y: layoutPos.y,
                width: rect.width,
                height: rect.height
              });
            }
          }
        }
      });
      
      setNodePositions(newPositions);
    };

    // Update positions after a short delay to ensure DOM is ready
    const timer = setTimeout(updatePositions, 100);
    return () => clearTimeout(timer);
  }, [chain.nodes, layoutPositions]);

  const handleNodeHover = (nodeId: string | null) => {
    if (interactive) {
      setHoveredNodeId(nodeId);
    }
  };

  const handleNodeClick = (nodeId: string) => {
    if (interactive) {
      onNodeClick?.(nodeId);
      onNodeSelect?.(nodeId);
    }
  };

  // Calculate container dimensions
  const containerWidth = Math.max(
    ...Array.from(layoutPositions.values()).map(pos => pos.x)
  ) + (compact ? 250 : 320);
  
  const containerHeight = Math.max(
    ...Array.from(layoutPositions.values()).map(pos => pos.y)
  ) + (compact ? 150 : 200);

  return (
    <div className={cn("relative overflow-auto", className)}>
      <motion.div
        ref={containerRef}
        className="relative"
        style={{
          width: Math.max(containerWidth, 800),
          height: Math.max(containerHeight, 400),
          minHeight: '400px'
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Render connections first (behind nodes) */}
        <AnimatePresence>
          {chain.connections.map((connection) => {
            const fromNode = chain.nodes.find(n => n.id === connection.from);
            const toNode = chain.nodes.find(n => n.id === connection.to);
            
            if (!fromNode || !toNode) return null;
            
            return (
              <ChainConnector
                key={connection.id}
                connection={connection}
                fromNode={fromNode}
                toNode={toNode}
                nodePositions={nodePositions}
              />
            );
          })}
        </AnimatePresence>

        {/* Render nodes */}
        <AnimatePresence>
          {chain.nodes.map((node, index) => {
            const position = layoutPositions.get(node.id);
            if (!position) return null;

            return (
              <motion.div
                key={node.id}
                ref={(el) => {
                  if (el) {
                    nodeRefs.current.set(node.id, el);
                  } else {
                    nodeRefs.current.delete(node.id);
                  }
                }}
                className="absolute"
                style={{
                  left: position.x,
                  top: position.y,
                  zIndex: 10
                }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ 
                  duration: 0.4,
                  delay: index * 0.1,
                  ease: "easeOut"
                }}
              >
                <ChainNode
                  node={node}
                  isSelected={selectedNodeId === node.id}
                  isHovered={hoveredNodeId === node.id}
                  onHover={handleNodeHover}
                  onClick={handleNodeClick}
                  compact={compact}
                />
              </motion.div>
            );
          })}
        </AnimatePresence>

        {/* Chain title and metadata */}
        <motion.div
          className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm border"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <h3 className="font-semibold text-sm text-gray-800">{chain.title}</h3>
          {chain.description && (
            <p className="text-xs text-gray-600 mt-1">{chain.description}</p>
          )}
          <div className="flex items-center gap-2 mt-2">
            <span className="text-xs text-gray-500">
              {chain.nodes.length} nodes
            </span>
            <span className="text-xs text-gray-500">•</span>
            <span className="text-xs text-gray-500">
              {chain.connections.length} connections
            </span>
          </div>
        </motion.div>

        {/* Interactive Overlay */}
        {showInteractiveOverlay && interactive && (
          <InteractiveOverlay
            chain={chain}
            hoveredNodeId={hoveredNodeId}
            selectedNodeId={selectedNodeId}
            onNodeClick={handleNodeClick}
            onPlayAnimation={() => setIsAnimationPlaying(true)}
            onPauseAnimation={() => setIsAnimationPlaying(false)}
            isAnimationPlaying={isAnimationPlaying}
          />
        )}
      </motion.div>
    </div>
  );
};
