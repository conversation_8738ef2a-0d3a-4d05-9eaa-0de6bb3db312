/**
 * End-to-End Tests for Complete User Workflows
 * 
 * Tests complete user journeys from analysis creation through design
 * and export, ensuring all integrated systems work together seamlessly.
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

test.describe('Complete User Workflows', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
  });

  test.describe('Analysis to Design Workflow', () => {
    test('should complete full analysis-to-design workflow', async ({ page }) => {
      // Step 1: Create an analysis
      await test.step('Create analysis', async () => {
        await page.click('[data-testid="question-input"]');
        await page.fill('[data-testid="question-input"]', 'What are the benefits of renewable energy?');
        
        await page.selectOption('[data-testid="analysis-type-select"]', 'pros-cons');
        await page.selectOption('[data-testid="model-select"]', 'gpt-4');
        
        await page.click('[data-testid="analyze-button"]');
        
        // Wait for analysis to complete
        await page.waitForSelector('[data-testid="analysis-result"]', { timeout: 30000 });
        
        // Verify analysis appears
        await expect(page.locator('[data-testid="analysis-result"]')).toContainText('renewable energy');
      });

      // Step 2: Open Visual Canvas
      await test.step('Open Visual Canvas', async () => {
        await page.click('[data-testid="open-canvas-button"]');
        
        // Wait for canvas to load
        await page.waitForSelector('[data-testid="visual-canvas"]', { timeout: 10000 });
        
        // Verify canvas is open
        await expect(page.locator('[data-testid="visual-canvas"]')).toBeVisible();
      });

      // Step 3: Switch to Design Studio mode
      await test.step('Switch to Design Studio', async () => {
        await page.click('[data-testid="design-studio-tab"]');
        
        // Wait for Figma tools to load
        await page.waitForSelector('[data-testid="figma-tools"]', { timeout: 5000 });
        
        // Verify design tools are available
        await expect(page.locator('[title="Rectangle"]')).toBeVisible();
        await expect(page.locator('[title="Text"]')).toBeVisible();
        await expect(page.locator('[title="Pen"]')).toBeVisible();
      });

      // Step 4: Convert analysis to design elements
      await test.step('Convert analysis to design', async () => {
        // Open analysis bridge panel
        await page.click('[data-testid="analysis-panel-toggle"]');
        
        // Select analysis result
        await page.click('[data-testid="analysis-result"]:first-child');
        
        // Convert to design element
        await page.click('[data-testid="convert-to-text-button"]');
        
        // Verify design element was created
        await expect(page.locator('[data-testid="design-element"]')).toBeVisible();
      });

      // Step 5: Use design tools
      await test.step('Use design tools', async () => {
        // Select rectangle tool
        await page.click('[title="Rectangle"]');
        
        // Draw rectangle on canvas
        const canvas = page.locator('[data-testid="figma-canvas"]');
        await canvas.click({ position: { x: 100, y: 100 } });
        await canvas.dragTo(canvas, { 
          sourcePosition: { x: 100, y: 100 },
          targetPosition: { x: 200, y: 150 }
        });
        
        // Verify rectangle was created
        await expect(page.locator('[data-testid="canvas-object"]')).toHaveCount(2); // Text + Rectangle
      });

      // Step 6: Export design
      await test.step('Export design', async () => {
        await page.click('[data-testid="export-button"]');
        
        // Configure export options
        await page.selectOption('[data-testid="export-format-select"]', 'pdf');
        await page.fill('[data-testid="export-title-input"]', 'Renewable Energy Analysis');
        
        // Start export
        const downloadPromise = page.waitForEvent('download');
        await page.click('[data-testid="start-export-button"]');
        
        // Verify download
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toContain('Renewable_Energy_Analysis.pdf');
      });
    });

    test('should handle offline workflow', async ({ page }) => {
      // Simulate offline mode
      await page.context().setOffline(true);
      
      await test.step('Create offline analysis', async () => {
        await page.click('[data-testid="question-input"]');
        await page.fill('[data-testid="question-input"]', 'How does solar power work?');
        
        await page.click('[data-testid="analyze-button"]');
        
        // Should use offline AI service
        await page.waitForSelector('[data-testid="offline-analysis-indicator"]');
        await page.waitForSelector('[data-testid="analysis-result"]', { timeout: 15000 });
        
        // Verify offline analysis
        await expect(page.locator('[data-testid="analysis-result"]')).toContainText('Local Rule-Based Analysis');
      });

      await test.step('Use offline design tools', async () => {
        await page.click('[data-testid="open-canvas-button"]');
        await page.click('[data-testid="design-studio-tab"]');
        
        // Design tools should work offline
        await page.click('[title="Text"]');
        await page.click('[data-testid="figma-canvas"]', { position: { x: 150, y: 100 } });
        
        // Verify text element created
        await expect(page.locator('[data-testid="canvas-object"]')).toBeVisible();
      });

      // Restore online mode
      await page.context().setOffline(false);
    });
  });

  test.describe('Presentation Creation Workflow', () => {
    test('should create and export presentation from multiple analyses', async ({ page }) => {
      // Create multiple analyses
      await test.step('Create multiple analyses', async () => {
        const questions = [
          'What are the advantages of electric vehicles?',
          'How do electric vehicles impact the environment?',
          'What are the challenges facing electric vehicle adoption?'
        ];

        for (const question of questions) {
          await page.fill('[data-testid="question-input"]', question);
          await page.click('[data-testid="analyze-button"]');
          await page.waitForSelector('[data-testid="analysis-result"]', { timeout: 30000 });
        }

        // Verify all analyses are created
        await expect(page.locator('[data-testid="analysis-result"]')).toHaveCount(3);
      });

      // Open presentation generator
      await test.step('Open presentation generator', async () => {
        await page.click('[data-testid="create-presentation-button"]');
        
        // Wait for presentation dialog
        await page.waitForSelector('[data-testid="presentation-dialog"]');
        
        // Select all analyses
        await page.click('[data-testid="select-all-analyses"]');
        
        // Configure presentation
        await page.fill('[data-testid="presentation-title"]', 'Electric Vehicle Analysis');
        await page.selectOption('[data-testid="presentation-template"]', 'professional');
        
        // Generate presentation
        await page.click('[data-testid="generate-presentation-button"]');
        
        // Wait for generation
        await page.waitForSelector('[data-testid="presentation-preview"]', { timeout: 15000 });
      });

      // Export presentation
      await test.step('Export presentation', async () => {
        await page.selectOption('[data-testid="export-format"]', 'pptx');
        
        const downloadPromise = page.waitForEvent('download');
        await page.click('[data-testid="export-presentation-button"]');
        
        const download = await downloadPromise;
        expect(download.suggestedFilename()).toContain('Electric_Vehicle_Analysis.pptx');
      });
    });
  });

  test.describe('Node-Based Workflow', () => {
    test('should create and connect analysis nodes', async ({ page }) => {
      // Open node editor
      await test.step('Open node editor', async () => {
        await page.click('[data-testid="node-editor-button"]');
        await page.waitForSelector('[data-testid="node-canvas"]');
      });

      // Create analysis input node
      await test.step('Create input node', async () => {
        await page.click('[data-testid="add-node-button"]');
        await page.click('[data-testid="analysis-input-node"]');
        
        // Configure input node
        await page.fill('[data-testid="node-question-input"]', 'What is artificial intelligence?');
        await page.click('[data-testid="confirm-node-button"]');
        
        // Verify node created
        await expect(page.locator('[data-testid="node-analysis-input"]')).toBeVisible();
      });

      // Create process node
      await test.step('Create process node', async () => {
        await page.click('[data-testid="add-node-button"]');
        await page.click('[data-testid="analysis-process-node"]');
        
        await page.selectOption('[data-testid="node-model-select"]', 'gpt-4');
        await page.selectOption('[data-testid="node-analysis-type"]', 'deep');
        await page.click('[data-testid="confirm-node-button"]');
        
        await expect(page.locator('[data-testid="node-analysis-process"]')).toBeVisible();
      });

      // Connect nodes
      await test.step('Connect nodes', async () => {
        // Enter connection mode
        await page.click('[data-testid="connection-mode-button"]');
        
        // Click output port of input node
        await page.click('[data-testid="node-analysis-input"] [data-testid="output-port"]');
        
        // Click input port of process node
        await page.click('[data-testid="node-analysis-process"] [data-testid="input-port"]');
        
        // Verify connection created
        await expect(page.locator('[data-testid="node-connection"]')).toBeVisible();
      });

      // Execute node workflow
      await test.step('Execute workflow', async () => {
        await page.click('[data-testid="execute-workflow-button"]');
        
        // Wait for execution
        await page.waitForSelector('[data-testid="workflow-complete"]', { timeout: 30000 });
        
        // Verify results
        await expect(page.locator('[data-testid="node-output"]')).toContainText('artificial intelligence');
      });
    });
  });

  test.describe('Responsive Design Workflow', () => {
    test('should work on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await test.step('Mobile analysis creation', async () => {
        // Mobile interface should be accessible
        await page.click('[data-testid="mobile-menu-button"]');
        await page.click('[data-testid="analysis-tab"]');
        
        await page.fill('[data-testid="question-input"]', 'Mobile test question');
        await page.click('[data-testid="analyze-button"]');
        
        await page.waitForSelector('[data-testid="analysis-result"]');
        await expect(page.locator('[data-testid="analysis-result"]')).toBeVisible();
      });

      await test.step('Mobile canvas interaction', async () => {
        await page.click('[data-testid="open-canvas-button"]');
        
        // Should show mobile-optimized canvas
        await expect(page.locator('[data-testid="mobile-canvas"]')).toBeVisible();
        
        // Touch interactions should work
        await page.tap('[data-testid="mobile-tool-select"]');
        await page.tap('[data-testid="canvas-area"]');
      });
    });

    test('should adapt to tablet viewport', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Tablet should show hybrid interface
      await expect(page.locator('[data-testid="tablet-interface"]')).toBeVisible();
      
      // Should have collapsible panels
      await page.click('[data-testid="collapse-panel-button"]');
      await expect(page.locator('[data-testid="side-panel"]')).toBeHidden();
    });
  });

  test.describe('Error Recovery Workflow', () => {
    test('should recover from network errors', async ({ page }) => {
      // Start analysis
      await page.fill('[data-testid="question-input"]', 'Test question for error recovery');
      await page.click('[data-testid="analyze-button"]');
      
      // Simulate network error during analysis
      await page.route('**/api/analyze', route => route.abort());
      
      // Should show error and retry option
      await page.waitForSelector('[data-testid="analysis-error"]');
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
      
      // Restore network and retry
      await page.unroute('**/api/analyze');
      await page.click('[data-testid="retry-button"]');
      
      // Should complete successfully
      await page.waitForSelector('[data-testid="analysis-result"]');
      await expect(page.locator('[data-testid="analysis-result"]')).toBeVisible();
    });

    test('should handle canvas crashes gracefully', async ({ page }) => {
      await page.click('[data-testid="open-canvas-button"]');
      
      // Simulate canvas error
      await page.evaluate(() => {
        window.dispatchEvent(new Error('Canvas rendering error'));
      });
      
      // Should show error boundary
      await expect(page.locator('[data-testid="error-boundary"]')).toBeVisible();
      
      // Should offer recovery options
      await page.click('[data-testid="reload-canvas-button"]');
      await expect(page.locator('[data-testid="visual-canvas"]')).toBeVisible();
    });
  });

  test.describe('Performance Workflow', () => {
    test('should handle large datasets efficiently', async ({ page }) => {
      // Create many analyses quickly
      for (let i = 0; i < 20; i++) {
        await page.fill('[data-testid="question-input"]', `Performance test question ${i}`);
        await page.click('[data-testid="analyze-button"]');
        
        // Don't wait for each to complete - test concurrent handling
        if (i < 19) {
          await page.waitForTimeout(100); // Small delay between requests
        }
      }
      
      // Wait for all to complete
      await page.waitForFunction(() => {
        const results = document.querySelectorAll('[data-testid="analysis-result"]');
        return results.length >= 20;
      }, { timeout: 60000 });
      
      // Interface should remain responsive
      await page.click('[data-testid="open-canvas-button"]');
      await expect(page.locator('[data-testid="visual-canvas"]')).toBeVisible();
    });

    test('should maintain performance with complex designs', async ({ page }) => {
      await page.click('[data-testid="open-canvas-button"]');
      await page.click('[data-testid="design-studio-tab"]');
      
      // Create many design elements
      for (let i = 0; i < 50; i++) {
        await page.click('[title="Rectangle"]');
        await page.click('[data-testid="figma-canvas"]', { 
          position: { x: 50 + (i % 10) * 30, y: 50 + Math.floor(i / 10) * 30 }
        });
      }
      
      // Canvas should remain responsive
      await page.click('[title="Select"]');
      await page.click('[data-testid="figma-canvas"]', { position: { x: 100, y: 100 } });
      
      // Selection should work
      await expect(page.locator('[data-testid="selected-element"]')).toBeVisible();
    });
  });
});
