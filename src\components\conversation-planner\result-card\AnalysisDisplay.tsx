import React from "react";
import { FormattedContentDisplay } from "./analysis-display/ContentFormatter";
import { MultipleAnswersDisplay } from "./analysis-display/MultipleAnswersDisplay";
import { AnalysisResult } from "@/types/conversation";
import { DeepAnalysisDisplay } from "./analysis-display/DeepAnalysisDisplay";
import { CharacterAnalysisDisplay } from "./analysis-display/CharacterAnalysisDisplay";
import { ProsConsDisplay } from './analysis-display/ProsConsDisplay';
import { SixThinkingHatsDisplay } from './analysis-display/SixThinkingHatsDisplay';
import { EmotionalAnglesDisplay } from './analysis-display/EmotionalAnglesDisplay';

interface AnalysisDisplayProps {
  result: AnalysisResult;
  onAnswerSelect?: (answer: string, answerNumber: number) => void;
  onFollowUpQuestion?: (question: string) => void;
  onItemSelectForChat?: (item: string) => void;
  selectedItems?: string[];
  onItemToggleSelect?: (item: string) => void;
}

export const AnalysisDisplay: React.FC<AnalysisDisplayProps> = ({
  result,
  onAnswerSelect,
  onFollowUpQuestion,
  onItemSelectForChat,
  selectedItems,
  onItemToggleSelect,
}) => {
  const { analysis: content, analysisType, questionContext, parsedOutput } = result;
  const qc = questionContext || "asking";

  const handleFollowUpSelect = onItemSelectForChat || onFollowUpQuestion;

  if (parsedOutput && onFollowUpQuestion) {
    switch (parsedOutput.type) {
      case 'multiple':
        if (onAnswerSelect && handleFollowUpSelect) {
          return (
            <MultipleAnswersDisplay
              content={content}
              questionContext={qc}
              onAnswerSelect={onAnswerSelect}
              onFollowUpQuestion={handleFollowUpSelect}
              selectedItems={selectedItems}
              onItemToggleSelect={onItemToggleSelect}
            />
          );
        }
        break; 
      case 'deep':
        return <DeepAnalysisDisplay analysis={parsedOutput} onFollowUpQuestion={onFollowUpQuestion} onItemSelectForChat={onItemSelectForChat} selectedItems={selectedItems} onItemToggleSelect={onItemToggleSelect} />;
      case 'character':
        return <CharacterAnalysisDisplay analysis={parsedOutput} onFollowUpQuestion={onFollowUpQuestion} characterName={result.characterPersona?.name} onItemSelectForChat={onItemSelectForChat} selectedItems={selectedItems} onItemToggleSelect={onItemToggleSelect} />;
      case 'pros-cons':
        return <ProsConsDisplay analysis={parsedOutput} />;
      case 'six-hats':
        return <SixThinkingHatsDisplay analysis={parsedOutput} />;
      case 'emotional-angles':
        return <EmotionalAnglesDisplay analysis={parsedOutput} />;
      case 'fallback':
        return <FormattedContentDisplay content={parsedOutput.content} />;
    }
  }

  // Fallback for non-parsed or legacy results
  if (analysisType === "character") {
    return <FormattedContentDisplay content={content} />;
  }

  if (analysisType === "multiple" && onAnswerSelect && handleFollowUpSelect) {
    return (
      <MultipleAnswersDisplay
        content={content}
        questionContext={qc}
        onAnswerSelect={onAnswerSelect}
        onFollowUpQuestion={handleFollowUpSelect}
        selectedItems={selectedItems}
        onItemToggleSelect={onItemToggleSelect}
      />
    );
  }

  // Default display for deep analysis or other types
  return <FormattedContentDisplay content={content} />;
};
