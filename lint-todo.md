# Lint Error & Warning Tracker

This file lists all current lint errors and warnings. Check off each item as it is fixed.

---

## Errors

- [x] Unexpected any. Specify a different type (`@typescript-eslint/no-explicit-any`)
- [ ] React Hook useCallback/useEffect has a missing dependency (`react-hooks/exhaustive-deps`)
- [ ] Fast refresh only works when a file only exports components (`react-refresh/only-export-components`)
- [ ] Unexpected lexical declaration in case block (`no-case-declarations`)
- [ ] Unnecessary escape character (`no-useless-escape`)
- [ ] An interface declaring no members is equivalent to its supertype (`@typescript-eslint/no-empty-object-type`)
- [ ] Do not access Object.prototype method 'hasOwnProperty' from target object (`no-prototype-builtins`)
- [ ] Parsing error: Declaration or statement expected
- [ ] A `require()` style import is forbidden (`@typescript-eslint/no-require-imports`)

## Warnings

- [ ] The ref value 'xxx' will likely have changed by the time this effect cleanup function runs (`react-hooks/exhaustive-deps`)
- [ ] Unused eslint-disable directive
- [ ] Missing key prop in list rendering (`react/missing-key`)
- [ ] Avoid inline functions in JSX for better performance (`react/jsx-no-bind`)
- [ ] Consider using React.memo for performance optimization (`performance/react-memo`)
- [ ] Expensive operations in render, consider memoization (`performance/expensive-render`)
- [ ] Wildcard imports can increase bundle size (`performance/bundle-size`)

---

## File/Line Examples (partial)

- All critical any usages are now resolved. Remaining any usages are in test/mocks or are justified and do not cause errors.
- `src/components/visual-analysis/LivingDataCanvas.tsx:946:6`  useEffect missing dependencies
- `src/components/ui/textarea.tsx:6:18`  An interface declaring no members is equivalent to its supertype
- ...

---

> Update this file as you fix each issue. When all are checked off, your codebase will be lint-clean!
