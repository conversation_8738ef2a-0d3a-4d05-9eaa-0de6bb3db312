import { CanvasState, DrawingObject, Layer } from '@/types/figma';

interface StoredCanvas {
  id: string;
  name: string;
  canvasState: CanvasState;
  thumbnail?: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
}

interface StoredProject {
  id: string;
  name: string;
  description?: string;
  canvasIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

export class IndexedDBManager {
  private dbName = 'ChatCraftTrainerPro';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Canvas store
        if (!db.objectStoreNames.contains('canvases')) {
          const canvasStore = db.createObjectStore('canvases', { keyPath: 'id' });
          canvasStore.createIndex('name', 'name', { unique: false });
          canvasStore.createIndex('updatedAt', 'updatedAt', { unique: false });
        }

        // Projects store
        if (!db.objectStoreNames.contains('projects')) {
          const projectStore = db.createObjectStore('projects', { keyPath: 'id' });
          projectStore.createIndex('name', 'name', { unique: false });
          projectStore.createIndex('updatedAt', 'updatedAt', { unique: false });
        }

        // Settings store
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' });
        }

        // Auto-save store for temporary data
        if (!db.objectStoreNames.contains('autosave')) {
          const autosaveStore = db.createObjectStore('autosave', { keyPath: 'id' });
          autosaveStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  private ensureDB(): IDBDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call init() first.');
    }
    return this.db;
  }

  // Canvas operations
  async saveCanvas(canvas: Omit<StoredCanvas, 'id' | 'createdAt' | 'updatedAt'> & { id?: string }): Promise<string> {
    const db = this.ensureDB();
    const now = new Date();
    const id = canvas.id || this.generateId();

    const storedCanvas: StoredCanvas = {
      ...canvas,
      id,
      createdAt: canvas.id ? (await this.getCanvas(canvas.id))?.createdAt || now : now,
      updatedAt: now,
      version: '1.0',
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['canvases'], 'readwrite');
      const store = transaction.objectStore('canvases');
      const request = store.put(storedCanvas);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(new Error('Failed to save canvas'));
    });
  }

  async getCanvas(id: string): Promise<StoredCanvas | null> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['canvases'], 'readonly');
      const store = transaction.objectStore('canvases');
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          // Convert date strings back to Date objects
          result.createdAt = new Date(result.createdAt);
          result.updatedAt = new Date(result.updatedAt);
        }
        resolve(result || null);
      };
      request.onerror = () => reject(new Error('Failed to get canvas'));
    });
  }

  async getAllCanvases(): Promise<StoredCanvas[]> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['canvases'], 'readonly');
      const store = transaction.objectStore('canvases');
      const index = store.index('updatedAt');
      const request = index.getAll();

      request.onsuccess = () => {
        const results = request.result.map(canvas => ({
          ...canvas,
          createdAt: new Date(canvas.createdAt),
          updatedAt: new Date(canvas.updatedAt),
        }));
        // Sort by most recently updated
        results.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
        resolve(results);
      };
      request.onerror = () => reject(new Error('Failed to get canvases'));
    });
  }

  async deleteCanvas(id: string): Promise<void> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['canvases'], 'readwrite');
      const store = transaction.objectStore('canvases');
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to delete canvas'));
    });
  }

  // Auto-save operations
  async autoSave(canvasState: CanvasState, canvasId?: string): Promise<void> {
    const db = this.ensureDB();
    const id = canvasId || 'current';

    const autosaveData = {
      id,
      canvasState,
      timestamp: new Date(),
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['autosave'], 'readwrite');
      const store = transaction.objectStore('autosave');
      const request = store.put(autosaveData);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to auto-save'));
    });
  }

  async getAutoSave(canvasId?: string): Promise<{ canvasState: CanvasState; timestamp: Date } | null> {
    const db = this.ensureDB();
    const id = canvasId || 'current';

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['autosave'], 'readonly');
      const store = transaction.objectStore('autosave');
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          result.timestamp = new Date(result.timestamp);
        }
        resolve(result || null);
      };
      request.onerror = () => reject(new Error('Failed to get auto-save'));
    });
  }

  async clearAutoSave(canvasId?: string): Promise<void> {
    const db = this.ensureDB();
    const id = canvasId || 'current';

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['autosave'], 'readwrite');
      const store = transaction.objectStore('autosave');
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to clear auto-save'));
    });
  }

  // Project operations
  async saveProject(project: Omit<StoredProject, 'id' | 'createdAt' | 'updatedAt'> & { id?: string }): Promise<string> {
    const db = this.ensureDB();
    const now = new Date();
    const id = project.id || this.generateId();

    const storedProject: StoredProject = {
      ...project,
      id,
      createdAt: project.id ? (await this.getProject(project.id))?.createdAt || now : now,
      updatedAt: now,
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['projects'], 'readwrite');
      const store = transaction.objectStore('projects');
      const request = store.put(storedProject);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(new Error('Failed to save project'));
    });
  }

  async getProject(id: string): Promise<StoredProject | null> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['projects'], 'readonly');
      const store = transaction.objectStore('projects');
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          result.createdAt = new Date(result.createdAt);
          result.updatedAt = new Date(result.updatedAt);
        }
        resolve(result || null);
      };
      request.onerror = () => reject(new Error('Failed to get project'));
    });
  }

  async getAllProjects(): Promise<StoredProject[]> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['projects'], 'readonly');
      const store = transaction.objectStore('projects');
      const request = store.getAll();

      request.onsuccess = () => {
        const results = request.result.map(project => ({
          ...project,
          createdAt: new Date(project.createdAt),
          updatedAt: new Date(project.updatedAt),
        }));
        results.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
        resolve(results);
      };
      request.onerror = () => reject(new Error('Failed to get projects'));
    });
  }

  // Settings operations
  async saveSetting(key: string, value: unknown): Promise<void> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readwrite');
      const store = transaction.objectStore('settings');
      const request = store.put({ key, value });

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to save setting'));
    });
  }

  async getSetting(key: string): Promise<unknown> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readonly');
      const store = transaction.objectStore('settings');
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : null);
      };
      request.onerror = () => reject(new Error('Failed to get setting'));
    });
  }

  // Utility methods
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  async getStorageUsage(): Promise<{ used: number; quota: number }> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        quota: estimate.quota || 0,
      };
    }
    return { used: 0, quota: 0 };
  }

  async clearAllData(): Promise<void> {
    const db = this.ensureDB();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['canvases', 'projects', 'settings', 'autosave'], 'readwrite');
      
      const clearPromises = [
        this.clearStore(transaction, 'canvases'),
        this.clearStore(transaction, 'projects'),
        this.clearStore(transaction, 'settings'),
        this.clearStore(transaction, 'autosave'),
      ];

      Promise.all(clearPromises)
        .then(() => resolve())
        .catch(() => reject(new Error('Failed to clear data')));
    });
  }

  private clearStore(transaction: IDBTransaction, storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const store = transaction.objectStore(storeName);
      const request = store.clear();
      
      request.onsuccess = () => resolve();
      request.onerror = () => reject();
    });
  }

  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Singleton instance
export const indexedDBManager = new IndexedDBManager();
