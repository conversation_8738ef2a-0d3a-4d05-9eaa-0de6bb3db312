
import React, { useState, Suspense, lazy, useEffect } from "react";
import { AnalyticsDashboardEnhanced } from "./historical-analysis/AnalyticsDashboardEnhanced";
import { HistoricalAnalysisList } from "./historical-analysis/HistoricalAnalysisList";
import { HistoricalAnalysisControls } from "./historical-analysis/HistoricalAnalysisControls";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Brain, Settings } from "lucide-react";
import { HistoricalAnalysisApiManager } from "./historical-analysis/HistoricalAnalysisApiManager";
import { useHistoricalAnalysis } from "@/hooks/useHistoricalAnalysis";
import { ComponentLoader, ModalLoader } from "@/components/ui/loading-fallback";
import { useChunkPreloader } from "@/hooks/useChunkPreloader";

// Lazy load heavy components
const VisualHistoricalAnalysisCanvas = lazy(() => 
  import("./insights-canvas/VisualHistoricalAnalysisCanvas").then(module => ({ default: module.VisualHistoricalAnalysisCanvas }))
);
const AISettingsModal = lazy(() => 
  import("./historical-analysis/AISettingsModal").then(module => ({ default: module.AISettingsModal }))
);

interface HistoricalAnalysisProps {
  // No props needed currently
  className?: string;
}

export const HistoricalAnalysis: React.FC<HistoricalAnalysisProps> = () => {
  const [activeTab, setActiveTab] = useState("analytics");
  const [showAISettings, setShowAISettings] = useState(false);
  const { preloadCanvas, preloadAISettings } = useChunkPreloader();
  
  const {
    notes,
    searchQuery,
    setSearchQuery,
    filterBy,
    setFilterBy,
    sortBy,
    setSortBy,
    sortedNotes,
    handleViewNote,
    handleDiscoverNote,
    handleDeleteNote,
  } = useHistoricalAnalysis();

  // Preload canvas when user is likely to use it
  useEffect(() => {
    if (activeTab === 'canvas') {
      preloadCanvas();
    }
  }, [activeTab, preloadCanvas]);

  // Preload AI settings when user is about to open it
  useEffect(() => {
    if (showAISettings) {
      preloadAISettings();
    }
  }, [showAISettings, preloadAISettings]);

  return (
    <div className="space-y-6">
      <Card id="historical-analysis-card" className="bg-transparent border-none shadow-none">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-600" />
                Historical Analysis
              </CardTitle>
              <CardDescription>
                Explore your conversation analysis history with AI-powered insights
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <HistoricalAnalysisApiManager />
              <Button 
                variant="outline" 
                onClick={() => setShowAISettings(true)}
                size="sm"
              >
                <Settings className="h-4 w-4 mr-2" />
                AI Settings
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <HistoricalAnalysisControls
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            filterBy={filterBy}
            setFilterBy={setFilterBy}
            sortBy={sortBy}
            setSortBy={setSortBy}
            onShowAISettings={() => setShowAISettings(true)}
          />          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-slate-800 p-1 rounded-lg h-auto border border-slate-700">
              <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg py-3 px-4 text-slate-300 hover:text-white hover:bg-slate-700 transition-all duration-200 font-medium rounded-md">Analytics</TabsTrigger>
              <TabsTrigger value="canvas" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg py-3 px-4 text-slate-300 hover:text-white hover:bg-slate-700 transition-all duration-200 font-medium rounded-md">Visual Canvas</TabsTrigger>
              <TabsTrigger value="list" className="data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg py-3 px-4 text-slate-300 hover:text-white hover:bg-slate-700 transition-all duration-200 font-medium rounded-md">List View</TabsTrigger>
            </TabsList>

            <TabsContent value="analytics" className="mt-6">
              {/* Still use all notes for dashboard */}
              <AnalyticsDashboardEnhanced notes={notes} />
            </TabsContent>            <TabsContent value="canvas" className="mt-6">
              <Suspense fallback={<ComponentLoader message="Loading visual canvas..." />}>
                <VisualHistoricalAnalysisCanvas />
              </Suspense>
            </TabsContent>

            <TabsContent value="list" className="mt-6">
              <HistoricalAnalysisList
                notes={sortedNotes}
                onView={handleViewNote}
                onDiscover={handleDiscoverNote}
                onDelete={handleDeleteNote}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Suspense fallback={<ModalLoader />}>
        <AISettingsModal 
          isOpen={showAISettings}
          onClose={() => setShowAISettings(false)}
        />
      </Suspense>
    </div>
  );
};
