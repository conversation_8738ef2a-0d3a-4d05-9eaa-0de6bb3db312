
import React from "react";
import { CanvasCluster } from "@/services/aiCanvasService";

type SmartClusterWithCenter = CanvasCluster & {
  center: { x: number; y: number };
};

interface SmartClusterOverlayProps {
  clusters: SmartClusterWithCenter[];
  zoom: number;
  pan: { x: number; y: number };
  isVisible: boolean;
}

export const SmartClusterOverlay: React.FC<SmartClusterOverlayProps> = ({
  clusters,
  zoom,
  pan,
  isVisible
}) => {
  if (!isVisible || clusters.length === 0) return null;

  // Safety checks for transform values
  const safeZoom = isFinite(zoom) && zoom > 0 ? zoom : 1;
  const safePanX = isFinite(pan.x) ? pan.x : 0;
  const safePanY = isFinite(pan.y) ? pan.y : 0;

  return (
    <div
      className="absolute inset-0 pointer-events-none"
      style={{
        transform: `translate(${safePanX}px, ${safePanY}px) scale(${safeZoom})`,
        transformOrigin: '0 0',
        zIndex: 10, // Lower z-index to stay behind interactive elements
      }}
    >
      {clusters.map((cluster) => (        <div
          key={cluster.id}
          className="absolute rounded-full animate-pulse-glow"
          style={{
            left: isFinite(cluster.center.x) ? cluster.center.x - 150 : 0,
            top: isFinite(cluster.center.y) ? cluster.center.y - 150 : 0,
            width: 300,
            height: 300,
            backgroundColor: `${cluster.color}20`,
            boxShadow: `0 0 ${40 / safeZoom}px 0px ${cluster.color}BF`,
            pointerEvents: 'none', // Ensure it doesn't interfere with interactions
          }}
        >
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-3 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap text-center"
            style={{
              backgroundColor: cluster.color,
              color: 'white',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transform: `scale(${1 / safeZoom})`,
              transformOrigin: 'center center',
            }}
          >
            {cluster.name}
            <br />
            <span className="text-xs opacity-80">({Math.round(cluster.confidence * 100)}% confidence)</span>
          </div>
        </div>
      ))}
    </div>
  );
};
