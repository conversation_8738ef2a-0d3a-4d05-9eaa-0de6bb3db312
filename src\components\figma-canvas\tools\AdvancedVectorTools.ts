import { 
  DrawingObject, 
  Point, 
  StyleProperties,
  BezierCurveObject,
  PathObject,
  BooleanOperationObject
} from '@/types/figma';

/**
 * Advanced Vector Tools for Professional Design Capabilities
 * Implements Bezier curves, path operations, and advanced vector manipulation
 */

export interface BezierPoint extends Point {
  controlPoint1?: Point;
  controlPoint2?: Point;
  type: 'move' | 'line' | 'curve' | 'close';
}

export interface PathCommand {
  type: 'M' | 'L' | 'C' | 'Q' | 'A' | 'Z';
  points: Point[];
  controlPoints?: Point[];
}

export type BooleanOperation = 'union' | 'subtract' | 'intersect' | 'exclude';

export class AdvancedVectorTools {
  /**
   * Create a Bezier curve with control points
   */
  static createBezierCurve(
    points: BezierPoint[],
    layerId: string,
    style: StyleProperties = {}
  ): Omit<BezierCurveObject, 'id' | 'createdAt' | 'updatedAt'> {
    if (points.length < 2) {
      throw new Error('Bezier curve requires at least 2 points');
    }

    // Calculate bounding box
    const allPoints = points.flatMap(p => [
      p,
      ...(p.controlPoint1 ? [p.controlPoint1] : []),
      ...(p.controlPoint2 ? [p.controlPoint2] : [])
    ]);
    
    const minX = Math.min(...allPoints.map(p => p.x));
    const minY = Math.min(...allPoints.map(p => p.y));
    const maxX = Math.max(...allPoints.map(p => p.x));
    const maxY = Math.max(...allPoints.map(p => p.y));

    return {
      type: 'bezier-curve',
      layerId,
      transform: {
        x: minX,
        y: minY,
        width: maxX - minX || 1,
        height: maxY - minY || 1,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      style: {
        stroke: '#6366f1',
        strokeWidth: 2,
        fill: 'transparent',
        opacity: 1,
        ...style,
      },
      visible: true,
      locked: false,
      name: 'Bezier Curve',
      points: points.map(p => ({
        ...p,
        x: p.x - minX,
        y: p.y - minY,
        controlPoint1: p.controlPoint1 ? { x: p.controlPoint1.x - minX, y: p.controlPoint1.y - minY } : undefined,
        controlPoint2: p.controlPoint2 ? { x: p.controlPoint2.x - minX, y: p.controlPoint2.y - minY } : undefined,
      })),
      closed: false,
      smoothing: 0.5,
    };
  }

  /**
   * Create a complex path with multiple commands
   */
  static createPath(
    commands: PathCommand[],
    layerId: string,
    style: StyleProperties = {}
  ): Omit<PathObject, 'id' | 'createdAt' | 'updatedAt'> {
    if (commands.length === 0) {
      throw new Error('Path requires at least one command');
    }

    // Calculate bounding box from all points
    const allPoints = commands.flatMap(cmd => [
      ...cmd.points,
      ...(cmd.controlPoints || [])
    ]);
    
    const minX = Math.min(...allPoints.map(p => p.x));
    const minY = Math.min(...allPoints.map(p => p.y));
    const maxX = Math.max(...allPoints.map(p => p.x));
    const maxY = Math.max(...allPoints.map(p => p.y));

    // Generate SVG path string
    const pathString = this.generateSVGPath(commands, minX, minY);

    return {
      type: 'path',
      layerId,
      transform: {
        x: minX,
        y: minY,
        width: maxX - minX || 1,
        height: maxY - minY || 1,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      style: {
        stroke: '#8b5cf6',
        strokeWidth: 2,
        fill: 'transparent',
        opacity: 1,
        ...style,
      },
      visible: true,
      locked: false,
      name: 'Path',
      pathData: pathString,
      commands: commands.map(cmd => ({
        ...cmd,
        points: cmd.points.map(p => ({ x: p.x - minX, y: p.y - minY })),
        controlPoints: cmd.controlPoints?.map(p => ({ x: p.x - minX, y: p.y - minY })),
      })),
    };
  }

  /**
   * Perform boolean operations on two objects
   */
  static createBooleanOperation(
    object1Id: string,
    object2Id: string,
    operation: BooleanOperation,
    layerId: string,
    style: StyleProperties = {}
  ): Omit<BooleanOperationObject, 'id' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'boolean-operation',
      layerId,
      transform: {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
      },
      style: {
        fill: '#10b981',
        stroke: '#059669',
        strokeWidth: 2,
        opacity: 1,
        ...style,
      },
      visible: true,
      locked: false,
      name: `${operation.charAt(0).toUpperCase() + operation.slice(1)} Operation`,
      operation,
      sourceObject1Id: object1Id,
      sourceObject2Id: object2Id,
      preserveOriginals: false,
    };
  }

  /**
   * Generate SVG path string from commands
   */
  private static generateSVGPath(commands: PathCommand[], offsetX: number, offsetY: number): string {
    return commands.map(cmd => {
      const points = cmd.points.map(p => `${p.x - offsetX},${p.y - offsetY}`);
      
      switch (cmd.type) {
        case 'M':
          return `M ${points[0]}`;
        case 'L':
          return `L ${points.join(' ')}`;
        case 'C':
          if (cmd.controlPoints && cmd.controlPoints.length >= 2) {
            const cp1 = `${cmd.controlPoints[0].x - offsetX},${cmd.controlPoints[0].y - offsetY}`;
            const cp2 = `${cmd.controlPoints[1].x - offsetX},${cmd.controlPoints[1].y - offsetY}`;
            return `C ${cp1} ${cp2} ${points[0]}`;
          }
          return `L ${points[0]}`;
        case 'Q':
          if (cmd.controlPoints && cmd.controlPoints.length >= 1) {
            const cp = `${cmd.controlPoints[0].x - offsetX},${cmd.controlPoints[0].y - offsetY}`;
            return `Q ${cp} ${points[0]}`;
          }
          return `L ${points[0]}`;
        case 'A':
          // Arc command: A rx ry x-axis-rotation large-arc-flag sweep-flag x y
          return `A ${points.join(' ')}`;
        case 'Z':
          return 'Z';
        default:
          return '';
      }
    }).join(' ');
  }

  /**
   * Convert simple points to Bezier points with automatic control point generation
   */
  static pointsToBezierPoints(points: Point[], tension: number = 0.3): BezierPoint[] {
    if (points.length < 2) return [];

    const bezierPoints: BezierPoint[] = [];

    for (let i = 0; i < points.length; i++) {
      const current = points[i];
      const prev = points[i - 1];
      const next = points[i + 1];

      let controlPoint1: Point | undefined;
      let controlPoint2: Point | undefined;

      if (i > 0 && i < points.length - 1) {
        // Calculate control points for smooth curves
        const dx1 = current.x - prev.x;
        const dy1 = current.y - prev.y;
        const dx2 = next.x - current.x;
        const dy2 = next.y - current.y;

        const len1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
        const len2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);

        const fa = tension * len1 / (len1 + len2);
        const fb = tension * len2 / (len1 + len2);

        const p1x = current.x - fa * (next.x - prev.x);
        const p1y = current.y - fa * (next.y - prev.y);
        const p2x = current.x + fb * (next.x - prev.x);
        const p2y = current.y + fb * (next.y - prev.y);

        controlPoint1 = { x: p1x, y: p1y };
        controlPoint2 = { x: p2x, y: p2y };
      }

      bezierPoints.push({
        ...current,
        controlPoint1,
        controlPoint2,
        type: i === 0 ? 'move' : 'curve',
      });
    }

    return bezierPoints;
  }

  /**
   * Simplify a path by removing redundant points
   */
  static simplifyPath(points: Point[], tolerance: number = 1.0): Point[] {
    if (points.length <= 2) return points;

    const simplified: Point[] = [points[0]];

    for (let i = 1; i < points.length - 1; i++) {
      const prev = simplified[simplified.length - 1];
      const current = points[i];
      const next = points[i + 1];

      // Calculate distance from current point to line between prev and next
      const distance = this.pointToLineDistance(current, prev, next);

      if (distance > tolerance) {
        simplified.push(current);
      }
    }

    simplified.push(points[points.length - 1]);
    return simplified;
  }

  /**
   * Calculate distance from point to line
   */
  private static pointToLineDistance(point: Point, lineStart: Point, lineEnd: Point): number {
    const A = point.x - lineStart.x;
    const B = point.y - lineStart.y;
    const C = lineEnd.x - lineStart.x;
    const D = lineEnd.y - lineStart.y;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);

    const param = dot / lenSq;
    
    let xx: number, yy: number;
    
    if (param < 0) {
      xx = lineStart.x;
      yy = lineStart.y;
    } else if (param > 1) {
      xx = lineEnd.x;
      yy = lineEnd.y;
    } else {
      xx = lineStart.x + param * C;
      yy = lineStart.y + param * D;
    }

    const dx = point.x - xx;
    const dy = point.y - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Get default styles for advanced vector tools
   */
  static getDefaultStyle(toolType: string): StyleProperties {
    const styles: Record<string, StyleProperties> = {
      'bezier-curve': { stroke: '#6366f1', strokeWidth: 2, fill: 'transparent' },
      'path': { stroke: '#8b5cf6', strokeWidth: 2, fill: 'transparent' },
      'boolean-operation': { fill: '#10b981', stroke: '#059669', strokeWidth: 2 },
    };

    return styles[toolType] || {};
  }
}
