import React, { useRef, useEffect, useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Box, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Play, 
  Pause,
  Settings,
  Download,
  Maximize,
  Eye,
  Layers,
  Palette
} from 'lucide-react';
import * as THREE from 'three';
import { OrbitControls } from 'three-stdlib';
import { SavedAnalysis } from '@/types/conversation';

export interface Node3D {
  id: string;
  position: THREE.Vector3;
  data: {
    title: string;
    content: string;
    type: 'analysis' | 'question' | 'response' | 'cluster';
    connections: string[];
    metadata: Record<string, unknown>;
  };
  visual: {
    color: string;
    size: number;
    shape: 'sphere' | 'cube' | 'pyramid' | 'cylinder';
    opacity: number;
  };
}

export interface Connection3D {
  id: string;
  sourceId: string;
  targetId: string;
  strength: number;
  type: 'semantic' | 'temporal' | 'causal' | 'similarity';
  animated: boolean;
}

interface ThreeDVisualMapProps {
  analyses: SavedAnalysis[];
  nodes?: Node3D[];
  connections?: Connection3D[];
  onNodeClick?: (node: Node3D) => void;
  onNodeHover?: (node: Node3D | null) => void;
  className?: string;
  autoRotate?: boolean;
  enablePhysics?: boolean;
}

export const ThreeDVisualMap: React.FC<ThreeDVisualMapProps> = ({
  analyses,
  nodes = [],
  connections = [],
  onNodeClick,
  onNodeHover,
  className,
  autoRotate = false,
  enablePhysics = true,
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const controlsRef = useRef<OrbitControls>();
  const animationIdRef = useRef<number>();
  const nodeObjectsRef = useRef<Map<string, THREE.Object3D>>(new Map());
  const connectionObjectsRef = useRef<Map<string, THREE.Line>>(new Map());

  const [isPlaying, setIsPlaying] = useState(true);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [cameraDistance, setCameraDistance] = useState(50);
  const [nodeSize, setNodeSize] = useState(1);
  const [showConnections, setShowConnections] = useState(true);
  const [colorScheme, setColorScheme] = useState<'default' | 'semantic' | 'temporal'>('default');
  const [renderQuality, setRenderQuality] = useState<'low' | 'medium' | 'high'>('medium');

  // Initialize Three.js scene
  const initializeScene = useCallback(() => {
    if (!mountRef.current) return;

    // Scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf8fafc);
    scene.fog = new THREE.Fog(0xf8fafc, 50, 200);
    sceneRef.current = scene;

    // Camera
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(30, 30, 30);
    cameraRef.current = camera;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ 
      antialias: renderQuality !== 'low',
      alpha: true,
      powerPreference: 'high-performance'
    });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.setPixelRatio(renderQuality === 'high' ? window.devicePixelRatio : 1);
    renderer.shadowMap.enabled = renderQuality !== 'low';
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    // renderer.outputEncoding = THREE.sRGBEncoding; // deprecated
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.autoRotate = autoRotate;
    controls.autoRotateSpeed = 0.5;
    controls.maxDistance = 200;
    controls.minDistance = 10;
    controlsRef.current = controls;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = renderQuality !== 'low';
    directionalLight.shadow.mapSize.width = renderQuality === 'high' ? 2048 : 1024;
    directionalLight.shadow.mapSize.height = renderQuality === 'high' ? 2048 : 1024;
    scene.add(directionalLight);

    // Grid helper
    const gridHelper = new THREE.GridHelper(100, 20, 0xe2e8f0, 0xf1f5f9);
    gridHelper.position.y = -20;
    scene.add(gridHelper);

    // Raycaster for mouse interaction
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    const handleMouseMove = (event: MouseEvent) => {
      const rect = renderer.domElement.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycaster.setFromCamera(mouse, camera);
      const intersects = raycaster.intersectObjects(Array.from(nodeObjectsRef.current.values()));

      if (intersects.length > 0) {
        const intersectedObject = intersects[0].object;
        const nodeId = intersectedObject.userData.nodeId;
        if (nodeId !== hoveredNodeId) {
          setHoveredNodeId(nodeId);
          const node = nodes.find(n => n.id === nodeId);
          onNodeHover?.(node || null);
        }
        renderer.domElement.style.cursor = 'pointer';
      } else {
        if (hoveredNodeId) {
          setHoveredNodeId(null);
          onNodeHover?.(null);
        }
        renderer.domElement.style.cursor = 'default';
      }
    };

    const handleClick = (event: MouseEvent) => {
      const rect = renderer.domElement.getBoundingClientRect();
      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycaster.setFromCamera(mouse, camera);
      const intersects = raycaster.intersectObjects(Array.from(nodeObjectsRef.current.values()));

      if (intersects.length > 0) {
        const intersectedObject = intersects[0].object;
        const nodeId = intersectedObject.userData.nodeId;
        setSelectedNodeId(nodeId);
        const node = nodes.find(n => n.id === nodeId);
        onNodeClick?.(node!);
      }
    };

    renderer.domElement.addEventListener('mousemove', handleMouseMove);
    renderer.domElement.addEventListener('click', handleClick);

    return () => {
      renderer.domElement.removeEventListener('mousemove', handleMouseMove);
      renderer.domElement.removeEventListener('click', handleClick);
    };
  }, [autoRotate, renderQuality, nodes, hoveredNodeId, onNodeHover, onNodeClick]);

  // Generate nodes from analyses
  const generateNodesFromAnalyses = useCallback((): Node3D[] => {
    const generatedNodes: Node3D[] = [];
    const radius = 30;

    analyses.forEach((analysis, index) => {
      // Main analysis node
      const angle = (index / analyses.length) * Math.PI * 2;
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      const y = Math.random() * 10 - 5;

      const mainNode: Node3D = {
        id: `analysis_${analysis.id}`,
        position: new THREE.Vector3(x, y, z),
        data: {
          title: analysis.title,
          content: analysis.results.map(r => r.analysis).join(' ').substring(0, 200),
          type: 'analysis',
          connections: [],
          metadata: { analysisId: analysis.id, resultCount: analysis.results.length },
        },
        visual: {
          color: getColorByType('analysis', index),
          size: Math.min(2 + analysis.results.length * 0.5, 5),
          shape: 'sphere',
          opacity: 0.8,
        },
      };

      generatedNodes.push(mainNode);

      // Question nodes
      analysis.results.forEach((result, resultIndex) => {
        const questionAngle = angle + (resultIndex - analysis.results.length / 2) * 0.3;
        const questionRadius = radius * 0.7;
        const qx = Math.cos(questionAngle) * questionRadius;
        const qz = Math.sin(questionAngle) * questionRadius;
        const qy = y + (resultIndex - analysis.results.length / 2) * 3;

        const questionNode: Node3D = {
          id: `question_${analysis.id}_${resultIndex}`,
          position: new THREE.Vector3(qx, qy, qz),
          data: {
            title: result.question,
            content: result.analysis,
            type: 'question',
            connections: [mainNode.id],
            metadata: { 
              analysisType: result.analysisType, 
              style: result.style,
              rating: result.rating 
            },
          },
          visual: {
            color: getColorByAnalysisType(result.analysisType),
            size: 1 + (result.rating || 0) * 0.3,
            shape: 'cube',
            opacity: 0.7,
          },
        };

        generatedNodes.push(questionNode);
        mainNode.data.connections.push(questionNode.id);
      });
    });

    return generatedNodes;
  }, [analyses]);

  // Get color by type
  const getColorByType = (type: string, index: number): string => {
    const colors = {
      analysis: ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444'],
      question: ['#6366f1', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'],
      response: ['#64748b', '#6b7280', '#9ca3af', '#d1d5db', '#e5e7eb'],
      cluster: ['#ec4899', '#f43f5e', '#ef4444', '#f97316', '#eab308'],
    };
    return colors[type as keyof typeof colors]?.[index % 5] || '#6b7280';
  };

  const getColorByAnalysisType = (analysisType: string): string => {
    const typeColors = {
      multiple: '#3b82f6',
      deep: '#8b5cf6',
      character: '#10b981',
      'pros-cons': '#f59e0b',
      'six-hats': '#ef4444',
      'emotional-angles': '#ec4899',
    };
    return typeColors[analysisType as keyof typeof typeColors] || '#6b7280';
  };

  // Create node geometry
  const createNodeGeometry = (node: Node3D): THREE.BufferGeometry => {
    switch (node.visual.shape) {
      case 'cube':
        return new THREE.BoxGeometry(node.visual.size, node.visual.size, node.visual.size);
      case 'pyramid':
        return new THREE.ConeGeometry(node.visual.size * 0.7, node.visual.size * 1.5, 4);
      case 'cylinder':
        return new THREE.CylinderGeometry(node.visual.size * 0.7, node.visual.size * 0.7, node.visual.size * 1.2);
      case 'sphere':
      default:
        return new THREE.SphereGeometry(node.visual.size, 16, 12);
    }
  };

  // Create node material
  const createNodeMaterial = (node: Node3D): THREE.Material => {
    const isSelected = selectedNodeId === node.id;
    const isHovered = hoveredNodeId === node.id;
    
    const color = new THREE.Color(node.visual.color);
    if (isSelected) color.multiplyScalar(1.3);
    if (isHovered) color.multiplyScalar(1.1);

    return new THREE.MeshPhongMaterial({
      color,
      transparent: true,
      opacity: node.visual.opacity,
      shininess: isSelected ? 100 : 30,
      emissive: isSelected ? new THREE.Color(node.visual.color).multiplyScalar(0.1) : new THREE.Color(0x000000),
    });
  };

  // Render nodes
  const renderNodes = useCallback(() => {
    if (!sceneRef.current) return;

    // Clear existing nodes
    nodeObjectsRef.current.forEach(obj => {
      sceneRef.current!.remove(obj);
    });
    nodeObjectsRef.current.clear();

    const nodesToRender = nodes.length > 0 ? nodes : generateNodesFromAnalyses();

    nodesToRender.forEach(node => {
      const geometry = createNodeGeometry(node);
      const material = createNodeMaterial(node);
      const mesh = new THREE.Mesh(geometry, material);

      mesh.position.copy(node.position);
      mesh.userData = { nodeId: node.id, nodeData: node.data };
      mesh.castShadow = renderQuality !== 'low';
      mesh.receiveShadow = renderQuality !== 'low';

      // Add text label
      if (renderQuality !== 'low') {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d')!;
        canvas.width = 256;
        canvas.height = 64;
        context.fillStyle = '#ffffff';
        context.fillRect(0, 0, canvas.width, canvas.height);
        context.fillStyle = '#000000';
        context.font = '16px Arial';
        context.textAlign = 'center';
        context.fillText(node.data.title.substring(0, 20), canvas.width / 2, 40);

        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        sprite.position.set(0, node.visual.size + 2, 0);
        sprite.scale.set(8, 2, 1);
        mesh.add(sprite);
      }

      sceneRef.current.add(mesh);
      nodeObjectsRef.current.set(node.id, mesh);
    });
  }, [nodes, generateNodesFromAnalyses, selectedNodeId, hoveredNodeId, renderQuality, nodeSize]);

  // Render connections
  const renderConnections = useCallback(() => {
    if (!sceneRef.current || !showConnections) return;

    // Clear existing connections
    connectionObjectsRef.current.forEach(line => {
      sceneRef.current!.remove(line);
    });
    connectionObjectsRef.current.clear();

    const nodesToRender = nodes.length > 0 ? nodes : generateNodesFromAnalyses();

    nodesToRender.forEach(node => {
      node.data.connections.forEach(targetId => {
        const targetNode = nodesToRender.find(n => n.id === targetId);
        if (!targetNode) return;

        const points = [node.position, targetNode.position];
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({ 
          color: 0x94a3b8, 
          transparent: true, 
          opacity: 0.6 
        });
        const line = new THREE.Line(geometry, material);

        sceneRef.current!.add(line);
        connectionObjectsRef.current.set(`${node.id}-${targetId}`, line);
      });
    });
  }, [nodes, generateNodesFromAnalyses, showConnections]);

  // Animation loop
  const animate = useCallback(() => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    controlsRef.current?.update();

    // Animate connections
    if (showConnections) {
      connectionObjectsRef.current.forEach(line => {
        const material = line.material as THREE.LineBasicMaterial;
        material.opacity = 0.3 + Math.sin(Date.now() * 0.001) * 0.3;
      });
    }

    rendererRef.current.render(sceneRef.current, cameraRef.current);
    
    if (isPlaying) {
      animationIdRef.current = requestAnimationFrame(animate);
    }
  }, [isPlaying, showConnections]);

  // Initialize scene
  useEffect(() => {
    const cleanup = initializeScene();
    return cleanup;
  }, [initializeScene]);

  // Render nodes and connections
  useEffect(() => {
    renderNodes();
    renderConnections();
  }, [renderNodes, renderConnections]);

  // Start animation
  useEffect(() => {
    if (isPlaying) {
      animate();
    }
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
    };
  }, [animate, isPlaying]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (!mountRef.current || !rendererRef.current || !cameraRef.current) return;

      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;

      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Control functions
  const resetCamera = () => {
    if (controlsRef.current && cameraRef.current) {
      cameraRef.current.position.set(30, 30, 30);
      controlsRef.current.reset();
    }
  };

  const toggleAutoRotate = () => {
    if (controlsRef.current) {
      controlsRef.current.autoRotate = !controlsRef.current.autoRotate;
    }
  };

  const exportScene = () => {
    if (rendererRef.current) {
      const link = document.createElement('a');
      link.download = '3d-visualization.png';
      link.href = rendererRef.current.domElement.toDataURL();
      link.click();
    }
  };

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Controls */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant={isPlaying ? "default" : "outline"}
                  onClick={() => setIsPlaying(!isPlaying)}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                <Button size="sm" variant="outline" onClick={resetCamera}>
                  <RotateCcw className="h-4 w-4" />
                </Button>
                
                <Button size="sm" variant="outline" onClick={toggleAutoRotate}>
                  <Box className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm">Node Size:</span>
                <Slider
                  value={[nodeSize]}
                  onValueChange={([value]) => setNodeSize(value)}
                  min={0.5}
                  max={3}
                  step={0.1}
                  className="w-20"
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="showConnections"
                  checked={showConnections}
                  onChange={(e) => setShowConnections(e.target.checked)}
                />
                <label htmlFor="showConnections" className="text-sm">Connections</label>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <select
                value={colorScheme}
                onChange={(e) => setColorScheme(e.target.value as 'default' | 'semantic' | 'temporal')}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="default">Default Colors</option>
                <option value="semantic">Semantic</option>
                <option value="temporal">Temporal</option>
              </select>

              <select
                value={renderQuality}
                onChange={(e) => setRenderQuality(e.target.value as 'low' | 'medium' | 'high')}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="low">Low Quality</option>
                <option value="medium">Medium Quality</option>
                <option value="high">High Quality</option>
              </select>

              <Button size="sm" variant="outline" onClick={exportScene}>
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 3D Viewport */}
      <div className="flex-1 relative">
        <div ref={mountRef} className="w-full h-full" />
        
        {/* Info Overlay */}
        <div className="absolute top-4 left-4 space-y-2">
          <Badge variant="secondary">
            Nodes: {nodes.length || generateNodesFromAnalyses().length}
          </Badge>
          <Badge variant="secondary">
            Connections: {connections.length}
          </Badge>
          {selectedNodeId && (
            <Badge variant="default">
              Selected: {nodes.find(n => n.id === selectedNodeId)?.data.title || 'Node'}
            </Badge>
          )}
        </div>

        {/* Performance Info */}
        <div className="absolute bottom-4 right-4 text-xs text-muted-foreground">
          Quality: {renderQuality} | FPS: ~60
        </div>
      </div>
    </div>
  );
};
