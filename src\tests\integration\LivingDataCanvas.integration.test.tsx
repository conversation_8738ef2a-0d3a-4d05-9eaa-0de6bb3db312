import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Move all jest.mock calls above all imports for proper hoisting
vi.mock('@/hooks/useAnalysisStore', () => ({
  useAnalysisStore: () => ({
    analysisResults: mockAnalysisResults,
    addAnalysisResult: vi.fn(),
    updateAnalysisResult: vi.fn(),
    deleteAnalysisResult: vi.fn()
  })
}));

vi.mock('@/hooks/useLibraryStore', () => ({
  useLibraryStore: () => ({
    savedAnalyses: [],
    saveAnalysis: vi.fn(),
    loadAnalysis: vi.fn()
  })
}));

vi.mock('@/services/api/chatAnalysisApi', () => ({
  ChatAnalysisApi: {
    getAllAnalysisResults: vi.fn().mockResolvedValue([]),
    createAnalysisResult: vi.fn(),
    updateAnalysisResult: vi.fn(),
    deleteAnalysisResult: vi.fn()
  }
}));

vi.mock('@/services/api/clusterApi', () => ({
  ClusterApi: {
    getAllClusters: vi.fn().mockResolvedValue([]),
    createCluster: vi.fn(),
    updateCluster: vi.fn(),
    deleteCluster: vi.fn()
  }
}));

vi.mock('@/services/api/simulationApi', () => ({
  SimulationApi: {
    getAllSimulations: vi.fn().mockResolvedValue([]),
    createSimulation: vi.fn(),
    runSimulation: vi.fn(),
    getSimulationResults: vi.fn()
  }
}));

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@/utils/test-utils';
import userEvent from '@testing-library/user-event';
import { LivingDataCanvas } from '@/components/visual-analysis/LivingDataCanvas';
import { AnalysisResult } from '@/types/conversation';
import { performanceProfiler } from '@/services/performance/performanceProfiler';

// Mock analysis results
const mockAnalysisResults: AnalysisResult[] = [
  {
    id: '1',
    question: 'What is the main topic?',
    analysis: 'The main topic is artificial intelligence.',
    analysisType: 'multiple',
    model: 'gpt-4',
    style: 'professional',
    timestamp: new Date(),
    rating: 5
  },
  {
    id: '2',
    question: 'How does AI work?',
    analysis: 'AI works through machine learning algorithms.',
    analysisType: 'deep',
    model: 'gpt-4',
    style: 'professional',
    timestamp: new Date(),
    rating: 4
  },
  {
    id: '3',
    question: 'What are the benefits?',
    analysis: 'Benefits include automation and efficiency.',
    analysisType: 'pros-cons',
    model: 'gpt-3.5-turbo',
    style: 'professional',
    timestamp: new Date(),
    rating: 3
  }
];

describe('LivingDataCanvas Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    // Clear performance metrics before each test
    performanceProfiler.clearMetrics();
    
    // Mock canvas and WebGL context
    const mock2dContext = {
      canvas: document.createElement('canvas'),
      fillStyle: '',
      strokeStyle: '',
      fillRect: vi.fn(),
      clearRect: vi.fn(),
      getImageData: vi.fn(() => ({ data: new Array(4) })),
      putImageData: vi.fn(),
      createImageData: vi.fn(() => ({ data: new Array(4) })),
      setTransform: vi.fn(),
      drawImage: vi.fn(),
      save: vi.fn(),
      fillText: vi.fn(),
      restore: vi.fn(),
      beginPath: vi.fn(),
      moveTo: vi.fn(),
      lineTo: vi.fn(),
      closePath: vi.fn(),
      stroke: vi.fn(),
      translate: vi.fn(),
      scale: vi.fn(),
      rotate: vi.fn(),
      arc: vi.fn(),
      measureText: vi.fn(() => ({ width: 42 })),
      transform: vi.fn(),
      rect: vi.fn(),
      clip: vi.fn(),
      bezierCurveTo: vi.fn(),
      quadraticCurveTo: vi.fn(),
      isPointInPath: vi.fn(),
      isPointInStroke: vi.fn(),
      strokeRect: vi.fn(),
      fill: vi.fn(),
      strokeText: vi.fn(),
      createLinearGradient: vi.fn(() => ({ addColorStop: vi.fn() })),
      createPattern: vi.fn(),
      createRadialGradient: vi.fn(() => ({ addColorStop: vi.fn() })),
      drawFocusIfNeeded: vi.fn(),
      getLineDash: vi.fn(),
      setLineDash: vi.fn(),
      lineDashOffset: 0,
      lineWidth: 1,
      lineCap: 'butt',
      lineJoin: 'miter',
      miterLimit: 10,
      shadowBlur: 0,
      shadowColor: '',
      shadowOffsetX: 0,
      shadowOffsetY: 0,
      globalAlpha: 1,
      globalCompositeOperation: 'source-over',
      font: '',
      textAlign: 'start',
      textBaseline: 'alphabetic',
      direction: 'inherit',
      createConicGradient: vi.fn(() => ({ addColorStop: vi.fn() })),
      filter: '',
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'low',
      arcTo: vi.fn(),
      ellipse: vi.fn(),
      roundRect: vi.fn(),
      getContextAttributes: vi.fn(),
      getTransform: vi.fn(),
      resetTransform: vi.fn(),
      isContextLost: vi.fn(),
    } as unknown as CanvasRenderingContext2D;

    HTMLCanvasElement.prototype.getContext = vi.fn((contextId: string) => {
      if (contextId === '2d') return mock2dContext;
      return null;
    }) as unknown as HTMLCanvasElement['getContext'];
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Canvas Initialization', () => {
    it('should render canvas with analysis results', async () => {
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
    });

    it('should initialize with proper performance tracking', async () => {
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        const metrics = performanceProfiler.getPerformanceSummary();
        expect(metrics).toBeDefined();
      });
    });

    it('should handle empty analysis results gracefully', async () => {
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
    });
  });

  describe('Node Interactions', () => {
    it('should handle node selection', async () => {
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        const canvas = screen.getByTestId('living-data-canvas');
        expect(canvas).toBeInTheDocument();
      });

      // Simulate node click
      const canvas = screen.getByRole('img'); // Canvas element
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      
      // Should show node information panel
      await waitFor(() => {
        expect(screen.getByTestId('node-info-panel')).toBeInTheDocument();
      });
    });

    it('should support multi-node selection with Ctrl+click', async () => {
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        const canvas = screen.getByRole('img');
        expect(canvas).toBeInTheDocument();
      });

      const canvas = screen.getByRole('img');
      
      // First node selection
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      
      // Second node selection with Ctrl
      fireEvent.click(canvas, { 
        clientX: 200, 
        clientY: 200, 
        ctrlKey: true 
      });
      
      await waitFor(() => {
        expect(screen.getByTestId('multi-selection-info')).toBeInTheDocument();
      });
    });
  });

  describe('Advanced Controls', () => {
    it('should toggle advanced control panel', async () => {
      render(<LivingDataCanvas />);
      
      // Find and click the advanced controls toggle
      const toggleButton = screen.getByLabelText('Toggle Advanced Controls');
      await user.click(toggleButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('advanced-control-panel')).toBeInTheDocument();
      });
    });

    it('should handle theme changes', async () => {
      render(<LivingDataCanvas />);
      
      // Open advanced controls
      const toggleButton = screen.getByLabelText('Toggle Advanced Controls');
      await user.click(toggleButton);
      
      // Switch to visual tab
      const visualTab = screen.getByRole('tab', { name: /visual/i });
      await user.click(visualTab);
      
      // Change theme
      const darkThemeButton = screen.getByRole('button', { name: /dark/i });
      await user.click(darkThemeButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toHaveAttribute('data-theme', 'dark');
      });
    });

    it('should handle layout algorithm changes', async () => {
      render(<LivingDataCanvas />);
      
      // Open advanced controls
      const toggleButton = screen.getByLabelText('Toggle Advanced Controls');
      await user.click(toggleButton);
      
      // Change layout algorithm
      const layoutSelect = screen.getByLabelText('Layout Algorithm');
      await user.selectOptions(layoutSelect, 'circular');
      
      await waitFor(() => {
        expect(layoutSelect).toHaveValue('circular');
      });
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should respond to keyboard shortcuts', async () => {
      render(<LivingDataCanvas />);
      
      // Press Ctrl+Shift+? to open shortcuts panel
      fireEvent.keyDown(document, {
        key: '?',
        ctrlKey: true,
        shiftKey: true
      });
      
      await waitFor(() => {
        expect(screen.getByTestId('keyboard-shortcuts-panel')).toBeInTheDocument();
      });
    });

    it('should handle escape key to clear selections', async () => {
      render(<LivingDataCanvas />);
      
      // Select a node first
      const canvas = screen.getByRole('img');
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      
      // Press escape
      fireEvent.keyDown(document, { key: 'Escape' });
      
      await waitFor(() => {
        expect(screen.queryByTestId('node-info-panel')).not.toBeInTheDocument();
      });
    });
  });

  describe('Cluster Management', () => {
    it('should create clusters from selected nodes', async () => {
      render(<LivingDataCanvas />);
      
      const canvas = screen.getByRole('img');
      
      // Select multiple nodes
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      fireEvent.click(canvas, { clientX: 200, clientY: 200, ctrlKey: true });
      
      // Press 'C' to create cluster
      fireEvent.keyDown(document, { key: 'c' });
      
      await waitFor(() => {
        expect(screen.getByTestId('cluster-manager')).toBeInTheDocument();
      });
    });

    it('should handle cluster creation form', async () => {
      render(<LivingDataCanvas />);
      
      const canvas = screen.getByRole('img');
      
      // Select multiple nodes and create cluster
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      fireEvent.click(canvas, { clientX: 200, clientY: 200, ctrlKey: true });
      fireEvent.keyDown(document, { key: 'c' });
      
      await waitFor(() => {
        expect(screen.getByTestId('cluster-manager')).toBeInTheDocument();
      });
      
      // Fill cluster form
      const nameInput = screen.getByLabelText('Cluster Name');
      const descriptionInput = screen.getByLabelText('Description');
      
      await user.type(nameInput, 'AI Analysis Cluster');
      await user.type(descriptionInput, 'Cluster for AI-related analyses');
      
      // Submit cluster
      const createButton = screen.getByRole('button', { name: /create cluster/i });
      await user.click(createButton);
      
      await waitFor(() => {
        expect(screen.queryByTestId('cluster-manager')).not.toBeInTheDocument();
      });
    });
  });

  describe('Simulation Framework', () => {
    it('should create simulations from selected nodes', async () => {
      render(<LivingDataCanvas />);
      
      const canvas = screen.getByRole('img');
      
      // Select nodes
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      
      // Press 'S' to create simulation
      fireEvent.keyDown(document, { key: 's' });
      
      await waitFor(() => {
        expect(screen.getByTestId('simulation-manager')).toBeInTheDocument();
      });
    });

    it('should handle simulation configuration', async () => {
      render(<LivingDataCanvas />);
      
      const canvas = screen.getByRole('img');
      
      // Create simulation
      fireEvent.click(canvas, { clientX: 100, clientY: 100 });
      fireEvent.keyDown(document, { key: 's' });
      
      await waitFor(() => {
        expect(screen.getByTestId('simulation-manager')).toBeInTheDocument();
      });
      
      // Configure simulation
      const nameInput = screen.getByLabelText('Simulation Name');
      const typeSelect = screen.getByLabelText('Simulation Type');
      
      await user.type(nameInput, 'AI Exploration Simulation');
      await user.selectOptions(typeSelect, 'exploration');
      
      // Start simulation
      const startButton = screen.getByRole('button', { name: /start simulation/i });
      await user.click(startButton);
      
      await waitFor(() => {
        expect(screen.getByTestId('simulation-runner')).toBeInTheDocument();
      });
    });
  });

  describe('Performance Optimization', () => {
    it('should handle large datasets efficiently', async () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        ...mockAnalysisResults[0],
        id: `large-${i}`,
        question: `Question ${i}`,
        analysis: `Analysis ${i}`
      }));
      
      const startTime = performance.now();
      
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render within reasonable time (5 seconds)
      expect(renderTime).toBeLessThan(5000);
    });

    it('should use web workers for heavy processing', async () => {
      const largeDataset = Array.from({ length: 500 }, (_, i) => ({
        ...mockAnalysisResults[0],
        id: `worker-${i}`,
        question: `Worker Question ${i}`,
        analysis: `Worker Analysis ${i}`
      }));
      
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        const metrics = performanceProfiler.getPerformanceSummary();
        // Should have worker processing metrics if workers are available
        expect(metrics.timingMetrics).toBeDefined();
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should handle real-time analysis result updates', async () => {
      const { rerender } = render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
      
      // Add new analysis result
      const newResult: AnalysisResult = {
        id: '4',
        question: 'New question',
        analysis: 'New analysis',
        analysisType: 'character',
        model: 'gpt-4',
        style: 'professional',
        timestamp: new Date(),
        rating: 5
      };
      
      const updatedResults = [...mockAnalysisResults, newResult];
      rerender(<LivingDataCanvas />);
      
      await waitFor(() => {
        // Should show updated node count or new node
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Mock API error
      const mockError = new Error('API Error');
      vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
      
      // Should not crash and should show error handling
      expect(console.error).not.toHaveBeenCalledWith(
        expect.stringContaining('Uncaught')
      );
    });

    it('should handle invalid analysis results', async () => {
      const invalidResults = [
        { id: '1' }, // Missing required fields
        null,
        undefined
      ] as Array<Record<string, unknown> | null | undefined>;
      
      render(<LivingDataCanvas />);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
      
      // Should handle gracefully without crashing
    });
  });
});
