import React, { useState, useCallback, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  FileText, 
  Image, 
  Presentation, 
  Share2,
  Settings,
  Palette,
  Layout,
  Zap,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import type { SavedAnalysis } from '../../types/conversation';

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'json' | 'csv' | 'png' | 'svg';
  quality: 'draft' | 'standard' | 'high' | 'print';
  includeMetadata: boolean;
  includeCharts: boolean;
  includeImages: boolean;
  template: 'minimal' | 'professional' | 'academic' | 'presentation';
  colorScheme: 'default' | 'monochrome' | 'corporate' | 'vibrant';
  pageSize: 'A4' | 'Letter' | 'Legal' | 'A3';
  orientation: 'portrait' | 'landscape';
  fontSize: number;
  margins: number;
  watermark?: string;
  branding?: {
    logo?: string;
    companyName?: string;
    colors?: {
      primary: string;
      secondary: string;
    };
  };
}

export interface ExportProgress {
  stage: 'preparing' | 'processing' | 'generating' | 'finalizing' | 'complete' | 'error';
  progress: number;
  message: string;
  estimatedTime?: number;
}

interface ProfessionalExportSystemProps {
  analyses: SavedAnalysis[];
  selectedAnalyses?: string[];
  onExport?: (options: ExportOptions, analyses: SavedAnalysis[]) => Promise<void>;
  onShare?: (shareUrl: string, options: ExportOptions) => void;
  enableBranding?: boolean;
  enableTemplates?: boolean;
}

export const ProfessionalExportSystem: React.FC<ProfessionalExportSystemProps> = ({
  analyses,
  selectedAnalyses = [],
  onExport,
  onShare,
  enableBranding = true,
  enableTemplates = true,
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    quality: 'standard',
    includeMetadata: true,
    includeCharts: true,
    includeImages: true,
    template: 'professional',
    colorScheme: 'default',
    pageSize: 'A4',
    orientation: 'portrait',
    fontSize: 12,
    margins: 20,
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);

  const previewRef = useRef<HTMLDivElement>(null);

  // Get analyses to export
  const analysesToExport = selectedAnalyses.length > 0 
    ? analyses.filter(a => selectedAnalyses.includes(a.id))
    : analyses;

  // Format options for different export types
  const formatOptions = [
    { 
      value: 'pdf', 
      label: 'PDF Document', 
      icon: FileText, 
      description: 'Professional PDF with formatting',
      features: ['High quality', 'Print ready', 'Searchable text']
    },
    { 
      value: 'docx', 
      label: 'Word Document', 
      icon: FileText, 
      description: 'Editable Microsoft Word format',
      features: ['Editable', 'Comments', 'Track changes']
    },
    { 
      value: 'html', 
      label: 'Web Page', 
      icon: Layout, 
      description: 'Interactive HTML with styling',
      features: ['Interactive', 'Responsive', 'Shareable']
    },
    { 
      value: 'png', 
      label: 'Image Export', 
      icon: Image, 
      description: 'High-resolution image format',
      features: ['Visual', 'Social media', 'Presentations']
    },
    { 
      value: 'json', 
      label: 'Data Export', 
      icon: Settings, 
      description: 'Structured data format',
      features: ['Machine readable', 'API integration', 'Backup']
    },
  ];

  // Template options
  const templateOptions = [
    {
      value: 'minimal',
      label: 'Minimal',
      description: 'Clean and simple design',
      preview: '/templates/minimal-preview.png'
    },
    {
      value: 'professional',
      label: 'Professional',
      description: 'Business-ready formatting',
      preview: '/templates/professional-preview.png'
    },
    {
      value: 'academic',
      label: 'Academic',
      description: 'Research paper style',
      preview: '/templates/academic-preview.png'
    },
    {
      value: 'presentation',
      label: 'Presentation',
      description: 'Slide-ready format',
      preview: '/templates/presentation-preview.png'
    },
  ];

  // Color scheme options
  const colorSchemes = [
    { value: 'default', label: 'Default', colors: ['#3b82f6', '#10b981', '#f59e0b'] },
    { value: 'monochrome', label: 'Monochrome', colors: ['#374151', '#6b7280', '#9ca3af'] },
    { value: 'corporate', label: 'Corporate', colors: ['#1e40af', '#dc2626', '#059669'] },
    { value: 'vibrant', label: 'Vibrant', colors: ['#8b5cf6', '#ec4899', '#f97316'] },
  ];

  // Handle export
  const handleExport = useCallback(async () => {
    if (analysesToExport.length === 0) return;

    setIsExporting(true);
    setExportProgress({
      stage: 'preparing',
      progress: 0,
      message: 'Preparing export...',
      estimatedTime: 30,
    });

    try {
      // Simulate export progress
      const stages = [
        { stage: 'preparing' as const, message: 'Preparing data...', duration: 1000 },
        { stage: 'processing' as const, message: 'Processing analyses...', duration: 2000 },
        { stage: 'generating' as const, message: 'Generating document...', duration: 3000 },
        { stage: 'finalizing' as const, message: 'Finalizing export...', duration: 1000 },
      ];

      for (let i = 0; i < stages.length; i++) {
        const stage = stages[i];
        setExportProgress({
          stage: stage.stage,
          progress: (i / stages.length) * 100,
          message: stage.message,
          estimatedTime: Math.max(0, 30 - (i * 7)),
        });

        await new Promise(resolve => setTimeout(resolve, stage.duration));
      }

      // Call the actual export function
      await onExport?.(exportOptions, analysesToExport);

      setExportProgress({
        stage: 'complete',
        progress: 100,
        message: 'Export completed successfully!',
      });

      // Generate share URL if applicable
      if (exportOptions.format === 'html') {
        const mockShareUrl = `https://share.example.com/${Date.now()}`;
        setShareUrl(mockShareUrl);
      }

    } catch (error) {
      setExportProgress({
        stage: 'error',
        progress: 0,
        message: error instanceof Error ? error.message : 'Export failed',
      });
    } finally {
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(null);
      }, 2000);
    }
  }, [analysesToExport, exportOptions, onExport]);

  // Handle share
  const handleShare = useCallback(() => {
    if (shareUrl) {
      onShare?.(shareUrl, exportOptions);
    }
  }, [shareUrl, exportOptions, onShare]);

  // Update export options
  const updateOptions = useCallback((updates: Partial<ExportOptions>) => {
    setExportOptions(prev => ({ ...prev, ...updates }));
  }, []);

  // Generate preview
  const generatePreview = useCallback(() => {
    setPreviewMode(true);
    // In a real implementation, this would generate a preview of the export
  }, []);

  return (
    <div className="space-y-6">
      {/* Export Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Professional Export
            <Badge variant="secondary">{analysesToExport.length} analyses</Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Export Configuration */}
        <div className="lg:col-span-2 space-y-6">
          {/* Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Export Format</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {formatOptions.map(format => (
                  <div
                    key={format.value}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      exportOptions.format === format.value 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => updateOptions({ format: format.value as ExportOptions['format'] })}
                  >
                    <div className="flex items-center gap-3">
                      <format.icon className="h-6 w-6 text-blue-600" />
                      <div className="flex-1">
                        <div className="font-medium">{format.label}</div>
                        <div className="text-sm text-muted-foreground">{format.description}</div>
                        <div className="flex gap-1 mt-2">
                          {format.features.map(feature => (
                            <Badge key={feature} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Template and Styling */}
          {enableTemplates && (
            <Card>
              <CardHeader>
                <CardTitle>Template & Styling</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Tabs value={exportOptions.template} onValueChange={(value) => updateOptions({ template: value as ExportOptions['template'] })}>
                  <TabsList className="grid w-full grid-cols-4">
                    {templateOptions.map(template => (
                      <TabsTrigger key={template.value} value={template.value}>
                        {template.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {templateOptions.map(template => (
                    <TabsContent key={template.value} value={template.value}>
                      <div className="text-sm text-muted-foreground">
                        {template.description}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>

                <div>
                  <label className="text-sm font-medium">Color Scheme</label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    {colorSchemes.map(scheme => (
                      <div
                        key={scheme.value}
                        className={`p-3 border rounded-lg cursor-pointer ${
                          exportOptions.colorScheme === scheme.value 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => updateOptions({ colorScheme: scheme.value as ExportOptions['colorScheme'] })}
                      >
                        <div className="text-xs font-medium mb-2">{scheme.label}</div>
                        <div className="flex gap-1">
                          {scheme.colors.map((color, index) => (
                            <div
                              key={index}
                              className="w-4 h-4 rounded"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Advanced Options */}
          <Card>
            <CardHeader>
              <CardTitle>Advanced Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Quality</label>
                  <select
                    value={exportOptions.quality}
                    onChange={(e) => updateOptions({ quality: e.target.value as ExportOptions['quality'] })}
                    className="w-full mt-1 p-2 border rounded-md"
                  >
                    <option value="draft">Draft</option>
                    <option value="standard">Standard</option>
                    <option value="high">High Quality</option>
                    <option value="print">Print Ready</option>
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Page Size</label>
                  <select
                    value={exportOptions.pageSize}
                    onChange={(e) => updateOptions({ pageSize: e.target.value as ExportOptions['pageSize'] })}
                    className="w-full mt-1 p-2 border rounded-md"
                  >
                    <option value="A4">A4</option>
                    <option value="Letter">Letter</option>
                    <option value="Legal">Legal</option>
                    <option value="A3">A3</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Font Size</label>
                  <Slider
                    value={[exportOptions.fontSize]}
                    onValueChange={([value]) => updateOptions({ fontSize: value })}
                    min={8}
                    max={18}
                    step={1}
                    className="mt-2"
                  />
                  <div className="text-xs text-muted-foreground mt-1">{exportOptions.fontSize}pt</div>
                </div>

                <div>
                  <label className="text-sm font-medium">Margins</label>
                  <Slider
                    value={[exportOptions.margins]}
                    onValueChange={([value]) => updateOptions({ margins: value })}
                    min={10}
                    max={50}
                    step={5}
                    className="mt-2"
                  />
                  <div className="text-xs text-muted-foreground mt-1">{exportOptions.margins}mm</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="includeMetadata"
                    checked={exportOptions.includeMetadata}
                    onChange={(e) => updateOptions({ includeMetadata: e.target.checked })}
                  />
                  <label htmlFor="includeMetadata" className="text-sm">Include metadata</label>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="includeCharts"
                    checked={exportOptions.includeCharts}
                    onChange={(e) => updateOptions({ includeCharts: e.target.checked })}
                  />
                  <label htmlFor="includeCharts" className="text-sm">Include charts and visualizations</label>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="includeImages"
                    checked={exportOptions.includeImages}
                    onChange={(e) => updateOptions({ includeImages: e.target.checked })}
                  />
                  <label htmlFor="includeImages" className="text-sm">Include images</label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Export Actions */}
        <div className="space-y-6">
          {/* Export Progress */}
          {exportProgress && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    {exportProgress.stage === 'complete' ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : exportProgress.stage === 'error' ? (
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    ) : (
                      <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                    )}
                    <span className="font-medium">{exportProgress.message}</span>
                  </div>

                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${exportProgress.progress}%` }}
                    />
                  </div>

                  {exportProgress.estimatedTime && (
                    <div className="text-xs text-muted-foreground">
                      Estimated time remaining: {exportProgress.estimatedTime}s
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Export Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Export Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                onClick={handleExport}
                disabled={isExporting || analysesToExport.length === 0}
                className="w-full"
              >
                {isExporting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export {exportOptions.format.toUpperCase()}
                  </>
                )}
              </Button>

              <Button 
                variant="outline" 
                onClick={generatePreview}
                disabled={isExporting}
                className="w-full"
              >
                <Presentation className="h-4 w-4 mr-2" />
                Preview
              </Button>

              {shareUrl && (
                <Button 
                  variant="outline" 
                  onClick={handleShare}
                  className="w-full"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Export
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Export Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Export Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Format:</span>
                <span className="font-medium">{exportOptions.format.toUpperCase()}</span>
              </div>
              <div className="flex justify-between">
                <span>Template:</span>
                <span className="font-medium">{exportOptions.template}</span>
              </div>
              <div className="flex justify-between">
                <span>Quality:</span>
                <span className="font-medium">{exportOptions.quality}</span>
              </div>
              <div className="flex justify-between">
                <span>Analyses:</span>
                <span className="font-medium">{analysesToExport.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Est. Size:</span>
                <span className="font-medium">
                  {Math.round(analysesToExport.length * 0.5)}MB
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
