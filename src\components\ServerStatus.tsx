/**
 * Server Status Component
 * Displays server health and handles crash recovery UI
 */

import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  useServerState, 
  checkDefaultPortOnly, 
  isRunningOnPreferredPort,
  getCurrentPort,
  logPortStatus 
} from '@/utils/portManager';
import { AlertTriangle, Server, RefreshCw, CheckCircle, XCircle } from 'lucide-react';

interface ServerStatusProps {
  className?: string;
  showDetails?: boolean;
}

export function ServerStatus({ className = '', showDetails = false }: ServerStatusProps) {
  const serverState = useServerState();
  const [isCheckingPort, setIsCheckingPort] = React.useState(false);
  const [portCheckResult, setPortCheckResult] = React.useState<string>('');

  const currentPort = getCurrentPort();
  const isPreferredPort = isRunningOnPreferredPort();

  const handlePortCheck = async () => {
    setIsCheckingPort(true);
    try {
      const status = await checkDefaultPortOnly();
      if (status.available) {
        setPortCheckResult(`✅ Port 8085 is available and ready`);
      } else {
        setPortCheckResult(`⚠️ Port 8085 is in use: ${status.error || 'Unknown reason'}`);
      }
      
      // Also log detailed status to console
      await logPortStatus();
    } catch (error) {
      setPortCheckResult(`❌ Error checking port: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsCheckingPort(false);
    }
  };

  const getStatusColor = () => {
    if (serverState.crashed) return 'destructive';
    if (!isPreferredPort) return 'secondary';
    if (serverState.isRunning) return 'default';
    return 'outline';
  };

  const getStatusIcon = () => {
    if (serverState.crashed) return <XCircle className="h-4 w-4" />;
    if (!isPreferredPort) return <AlertTriangle className="h-4 w-4" />;
    if (serverState.isRunning) return <CheckCircle className="h-4 w-4" />;
    return <Server className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (serverState.crashed) return 'Server Crashed';
    if (!isPreferredPort) return 'Non-preferred Port';
    if (serverState.isRunning) return 'Server Running';
    return 'Server Status Unknown';
  };

  // Show alert for crashes or port issues
  const shouldShowAlert = serverState.crashed || !isPreferredPort;

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Status Badge */}
      <div className="flex items-center gap-2">
        <Badge variant={getStatusColor()} className="flex items-center gap-1">
          {getStatusIcon()}
          {getStatusText()}
        </Badge>
        <span className="text-sm text-muted-foreground">
          Port {currentPort}
        </span>
      </div>

      {/* Alert for issues */}
      {shouldShowAlert && (
        <Alert variant={serverState.crashed ? 'destructive' : 'default'}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {serverState.crashed ? (
              <div className="space-y-2">
                <p>
                  Development server has crashed {serverState.retryCount} time(s). 
                  {serverState.retryCount >= 3 ? ' Manual intervention required.' : ''}
                </p>
                {serverState.retryCount >= 3 ? (
                  <p className="text-sm">
                    Please check for port conflicts and restart the server manually with: 
                    <code className="bg-muted px-1 rounded ml-1">npm run dev</code>
                  </p>
                ) : (
                  <p className="text-sm">
                    Suggested restart command: 
                    <code className="bg-muted px-1 rounded ml-1">npm run dev -- --port {serverState.port}</code>
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <p>
                  Server is running on port {currentPort}, but the preferred port is 8085.
                </p>
                <p className="text-sm">
                  For optimal performance, consider restarting on port 8085 if available.
                </p>
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Detailed status (optional) */}
      {showDetails && (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePortCheck}
              disabled={isCheckingPort}
              className="flex items-center gap-1"
            >
              <RefreshCw className={`h-3 w-3 ${isCheckingPort ? 'animate-spin' : ''}`} />
              Check Port 8085
            </Button>
          </div>

          {portCheckResult && (
            <div className="text-sm p-2 bg-muted rounded">
              {portCheckResult}
            </div>
          )}

          <div className="text-xs text-muted-foreground space-y-1">
            <div>Last checked: {serverState.lastCheck.toLocaleTimeString()}</div>
            <div>Retry count: {serverState.retryCount}</div>
            <div>Preferred port: {isPreferredPort ? 'Yes' : 'No'}</div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Server Status Indicator - Minimal version for header/status bar
 */
export function ServerStatusIndicator() {
  const serverState = useServerState();
  const isPreferredPort = isRunningOnPreferredPort();
  const currentPort = getCurrentPort();

  const getIndicatorColor = () => {
    if (serverState.crashed) return 'bg-red-500';
    if (!isPreferredPort) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getTooltipText = () => {
    if (serverState.crashed) return `Server crashed (${serverState.retryCount} times)`;
    if (!isPreferredPort) return `Running on port ${currentPort} (preferred: 8085)`;
    return `Server healthy on port ${currentPort}`;
  };

  return (
    <div 
      className="flex items-center gap-2 text-sm"
      title={getTooltipText()}
    >
      <div className={`w-2 h-2 rounded-full ${getIndicatorColor()}`} />
      <span className="text-muted-foreground">
        Port {currentPort}
      </span>
    </div>
  );
}

/**
 * Development Tools Panel - For debugging server issues
 */
export function ServerDevelopmentTools() {
  const [isOpen, setIsOpen] = React.useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  return (
    <div className="fixed bottom-4 right-20 z-50">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="mb-2"
      >
        <Server className="h-4 w-4 mr-1" />
        Server Tools
      </Button>

      {isOpen && (
        <div className="bg-background border rounded-lg p-4 shadow-lg max-w-sm">
          <h3 className="font-semibold mb-3">Development Server Tools</h3>
          <ServerStatus showDetails={true} />
        </div>
      )}
    </div>
  );
}
