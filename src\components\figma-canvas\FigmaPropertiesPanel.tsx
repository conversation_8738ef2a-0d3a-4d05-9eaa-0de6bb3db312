import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { DrawingObject } from '@/types/figma';
import { cn } from '@/lib/utils';

interface FigmaPropertiesPanelProps {
  className?: string;
}

export const FigmaPropertiesPanel: React.FC<FigmaPropertiesPanelProps> = ({ className }) => {
  const { 
    selectedObjectIds, 
    getSelectedObjects, 
    updateObject,
    deleteObject,
    duplicateObject 
  } = useFigmaCanvasStore();

  const selectedObjects = getSelectedObjects();
  const hasSelection = selectedObjects.length > 0;
  const singleSelection = selectedObjects.length === 1 ? selectedObjects[0] : null;

  const handlePropertyChange = (property: string, value: unknown) => {
    selectedObjectIds.forEach(id => {
      if (property.startsWith('transform.')) {
        const transformProp = property.split('.')[1];
        updateObject(id, {
          transform: {
            ...selectedObjects.find(obj => obj.id === id)?.transform,
            [transformProp]: value,
          }
        });
      } else if (property.startsWith('style.')) {
        const styleProp = property.split('.')[1];
        updateObject(id, {
          style: {
            ...selectedObjects.find(obj => obj.id === id)?.style,
            [styleProp]: value,
          }
        });
      } else {
        updateObject(id, { [property]: value });
      }
    });
  };

  const handleDelete = () => {
    selectedObjectIds.forEach(id => deleteObject(id));
  };

  const handleDuplicate = () => {
    selectedObjectIds.forEach(id => duplicateObject(id));
  };

  if (!hasSelection) {
    return (
      <div className={cn("w-64 bg-white border-l border-gray-200 flex flex-col", className)}>
        <div className="p-4 text-center text-gray-500 text-sm">
          Select an object to edit properties
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-64 bg-white border-l border-gray-200 flex flex-col", className)}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-900">Properties</h3>
        {selectedObjects.length > 1 && (
          <p className="text-xs text-gray-500 mt-1">
            {selectedObjects.length} objects selected
          </p>
        )}
      </div>

      <ScrollArea className="flex-1">
        <div className="p-3 space-y-4">
          {/* Object Actions */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDuplicate}
                className="flex-1"
              >
                Duplicate
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="flex-1 text-red-600 hover:text-red-700"
              >
                Delete
              </Button>
            </div>
          </div>

          <Separator />

          {/* Transform Properties */}
          {singleSelection && (
            <TransformProperties 
              object={singleSelection}
              onChange={handlePropertyChange}
            />
          )}

          <Separator />

          {/* Style Properties */}
          <StyleProperties 
            objects={selectedObjects}
            onChange={handlePropertyChange}
          />

          {/* Type-specific Properties */}
          {singleSelection && (
            <>
              <Separator />
              <TypeSpecificProperties 
                object={singleSelection}
                onChange={handlePropertyChange}
              />
            </>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

interface TransformPropertiesProps {
  object: DrawingObject;
  onChange: (property: string, value: unknown) => void;
}

const TransformProperties: React.FC<TransformPropertiesProps> = ({ object, onChange }) => {
  const { transform } = object;

  return (
    <div className="space-y-3">
      <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Transform</h4>
      
      {/* Position */}
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label htmlFor="x" className="text-xs">X</Label>
          <Input
            id="x"
            type="number"
            value={Math.round(transform.x)}
            onChange={(e) => onChange('transform.x', parseFloat(e.target.value) || 0)}
            className="h-7 text-xs"
          />
        </div>
        <div>
          <Label htmlFor="y" className="text-xs">Y</Label>
          <Input
            id="y"
            type="number"
            value={Math.round(transform.y)}
            onChange={(e) => onChange('transform.y', parseFloat(e.target.value) || 0)}
            className="h-7 text-xs"
          />
        </div>
      </div>

      {/* Size */}
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label htmlFor="width" className="text-xs">Width</Label>
          <Input
            id="width"
            type="number"
            value={Math.round(transform.width)}
            onChange={(e) => onChange('transform.width', parseFloat(e.target.value) || 1)}
            className="h-7 text-xs"
          />
        </div>
        <div>
          <Label htmlFor="height" className="text-xs">Height</Label>
          <Input
            id="height"
            type="number"
            value={Math.round(transform.height)}
            onChange={(e) => onChange('transform.height', parseFloat(e.target.value) || 1)}
            className="h-7 text-xs"
          />
        </div>
      </div>

      {/* Rotation */}
      <div>
        <Label htmlFor="rotation" className="text-xs">Rotation</Label>
        <Input
          id="rotation"
          type="number"
          value={Math.round(transform.rotation)}
          onChange={(e) => onChange('transform.rotation', parseFloat(e.target.value) || 0)}
          className="h-7 text-xs"
        />
      </div>
    </div>
  );
};

interface StylePropertiesProps {
  objects: DrawingObject[];
  onChange: (property: string, value: unknown) => void;
}

const StyleProperties: React.FC<StylePropertiesProps> = ({ objects, onChange }) => {
  // Get common style values
  const getCommonValue = (property: string) => {
    const values = objects.map(obj => {
      const keys = property.split('.');
      let value: unknown = obj;
      for (const key of keys) {
        if (typeof value === 'object' && value !== null && key in value) {
          value = (value as Record<string, unknown>)[key];
        } else {
          value = undefined;
          break;
        }
      }
      return value;
    });
    
    const firstValue = values[0];
    const allSame = values.every(v => v === firstValue);
    return allSame ? firstValue : undefined;
  };

  const fill = getCommonValue('style.fill');
  const stroke = getCommonValue('style.stroke');
  const strokeWidth = getCommonValue('style.strokeWidth');
  const opacity = getCommonValue('style.opacity');

  return (
    <div className="space-y-3">
      <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Style</h4>
      
      {/* Fill */}
      <div>
        <Label htmlFor="fill" className="text-xs">Fill</Label>
        <div className="flex gap-2">
          <Input
            id="fill"
            type="color"
            value={typeof fill === 'string' ? fill : '#000000'}
            onChange={(e) => onChange('style.fill', e.target.value)}
            className="h-7 w-12 p-1"
          />
          <Input
            type="text"
            value={typeof fill === 'string' ? fill : ''}
            onChange={(e) => onChange('style.fill', e.target.value)}
            placeholder="Color"
            className="h-7 text-xs flex-1"
          />
        </div>
      </div>

      {/* Stroke */}
      <div>
        <Label htmlFor="stroke" className="text-xs">Stroke</Label>
        <div className="flex gap-2">
          <Input
            id="stroke"
            type="color"
            value={typeof stroke === 'string' ? stroke : '#000000'}
            onChange={(e) => onChange('style.stroke', e.target.value)}
            className="h-7 w-12 p-1"
          />
          <Input
            type="text"
            value={typeof stroke === 'string' ? stroke : ''}
            onChange={(e) => onChange('style.stroke', e.target.value)}
            placeholder="Color"
            className="h-7 text-xs flex-1"
          />
        </div>
      </div>

      {/* Stroke Width */}
      <div>
        <Label htmlFor="strokeWidth" className="text-xs">Stroke Width</Label>
        <Input
          id="strokeWidth"
          type="number"
          min="0"
          value={typeof strokeWidth === 'number' ? strokeWidth : 0}
          onChange={(e) => onChange('style.strokeWidth', parseFloat(e.target.value) || 0)}
          className="h-7 text-xs"
        />
      </div>

      {/* Opacity */}
      <div>
        <Label htmlFor="opacity" className="text-xs">Opacity</Label>
        <div className="space-y-2">
          <Slider
            value={typeof opacity === 'number' ? [opacity] : [1]}
            onValueChange={([value]) => onChange('style.opacity', value)}
            max={1}
            min={0}
            step={0.01}
            className="w-full"
          />
          <Input
            type="number"
            min="0"
            max="1"
            step="0.01"
            value={typeof opacity === 'number' ? opacity : 1}
            onChange={(e) => onChange('style.opacity', parseFloat(e.target.value) || 1)}
            className="h-7 text-xs"
          />
        </div>
      </div>
    </div>
  );
};

interface TypeSpecificPropertiesProps {
  object: DrawingObject;
  onChange: (property: string, value: unknown) => void;
}

const TypeSpecificProperties: React.FC<TypeSpecificPropertiesProps> = ({ object, onChange }) => {
  switch (object.type) {
    case 'rectangle':
      return (
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Rectangle</h4>
          <div>
            <Label htmlFor="cornerRadius" className="text-xs">Corner Radius</Label>
            <Input
              id="cornerRadius"
              type="number"
              min="0"
              value={object.cornerRadius || 0}
              onChange={(e) => onChange('cornerRadius', parseFloat(e.target.value) || 0)}
              className="h-7 text-xs"
            />
          </div>
        </div>
      );

    case 'text':
      return (
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Text</h4>
          <div>
            <Label htmlFor="content" className="text-xs">Content</Label>
            <Input
              id="content"
              value={object.content}
              onChange={(e) => onChange('content', e.target.value)}
              className="h-7 text-xs"
            />
          </div>
          <div>
            <Label htmlFor="fontSize" className="text-xs">Font Size</Label>
            <Input
              id="fontSize"
              type="number"
              min="1"
              value={object.textStyle.fontSize}
              onChange={(e) => onChange('textStyle.fontSize', parseFloat(e.target.value) || 12)}
              className="h-7 text-xs"
            />
          </div>
        </div>
      );

    case 'polygon':
      return (
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Polygon</h4>
          <div>
            <Label htmlFor="sides" className="text-xs">Sides</Label>
            <Input
              id="sides"
              type="number"
              min="3"
              max="20"
              value={object.sides}
              onChange={(e) => onChange('sides', parseInt(e.target.value) || 3)}
              className="h-7 text-xs"
            />
          </div>
        </div>
      );

    case 'star':
      return (
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700 uppercase tracking-wide">Star</h4>
          <div>
            <Label htmlFor="points" className="text-xs">Points</Label>
            <Input
              id="points"
              type="number"
              min="3"
              max="20"
              value={object.points}
              onChange={(e) => onChange('points', parseInt(e.target.value) || 5)}
              className="h-7 text-xs"
            />
          </div>
        </div>
      );

    default:
      return null;
  }
};
