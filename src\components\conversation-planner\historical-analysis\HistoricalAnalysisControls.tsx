import React from 'react';
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, Filter } from "lucide-react";
import { useHistoricalAnalysisSettingsStore } from "@/stores/useHistoricalAnalysisSettingsStore";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { useDebouncedState } from "@/components/conversation-planner/visualization/PerformanceOptimizer";

export type SortOption = 'newest' | 'oldest' | 'rating' | 'type';
export type FilterOption = 'all' | 'multiple' | 'deep' | 'character';

interface HistoricalAnalysisControlsProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterBy: FilterOption;
  setFilterBy: (value: FilterOption) => void;
  sortBy: SortOption;
  setSortBy: (value: SortOption) => void;
  onShowAISettings?: () => void;
}

export const HistoricalAnalysisControls: React.FC<HistoricalAnalysisControlsProps> = ({
  searchQuery,
  setSearchQuery,
  filterBy,
  setFilterBy,
  sortBy,
  setSortBy,
  onShowAISettings,
}) => {
  const { settings: historicalSettings } = useHistoricalAnalysisSettingsStore();
  const { settings: mainSettings } = useSettingsStore();
  const aiConfigured = !!mainSettings.openRouterApiKey;
  const tokenUsage = historicalSettings.currentMonthUsage;
  const tokenLimit = historicalSettings.monthlyTokenLimit;
  const usagePercentage = tokenLimit > 0 ? (tokenUsage / tokenLimit) * 100 : 0;

  const [inputValue, debouncedValue, setInputValue] = useDebouncedState(searchQuery, 300);

  React.useEffect(() => {
    setSearchQuery(debouncedValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedValue]);

  React.useEffect(() => {
    setInputValue(searchQuery);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search analyses, tags, or content..."
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          className="pl-10"
        />
      </div>
      <div className="flex gap-2">
        <Select value={filterBy} onValueChange={(value: FilterOption) => setFilterBy(value)}>
          <SelectTrigger className="w-40">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="multiple">Multiple Answers</SelectItem>
            <SelectItem value="deep">Deep Analysis</SelectItem>
            <SelectItem value="character">Character Response</SelectItem>
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Newest</SelectItem>
            <SelectItem value="oldest">Oldest</SelectItem>
            <SelectItem value="type">Type</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
