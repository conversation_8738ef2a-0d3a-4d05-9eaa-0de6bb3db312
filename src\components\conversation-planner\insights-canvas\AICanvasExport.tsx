import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Download, Share2, FileText, Image, Database } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { UserNote } from "@/types/conversation";
import { InsightSummary } from "@/services/aiHistoricalService/types";

// Placeholder types for missing CanvasCluster and PatternOverlay
type CanvasCluster = unknown;
type PatternOverlay = unknown;

interface AICanvasExportProps {
  notes: UserNote[];
  connections: Array<{ fromId: string; toId: string }>;
  clusters: CanvasCluster[];
  patterns: PatternOverlay[];
  insights: InsightSummary | null;
  isVisible: boolean;
  onClose: () => void;
}

export const AICanvasExport: React.FC<AICanvasExportProps> = ({
  notes,
  connections,
  clusters,
  patterns,
  insights,
  isVisible,
  onClose
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  if (!isVisible) return null;

  const handleExportJSON = async () => {
    setIsExporting(true);
    try {
      const exportData = {
        timestamp: new Date().toISOString(),
        metadata: {
          notesCount: notes.length,
          connectionsCount: connections.length,
          clustersCount: clusters.length,
          patternsCount: patterns.length
        },
        notes,
        connections,
        aiAnalysis: {
          clusters,
          patterns,
          insights
        }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `historical-analysis-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export Complete",
        description: "Historical analysis data exported successfully."
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting the data.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleExportReport = async () => {
    setIsExporting(true);
    try {
      const reportContent = `
# Historical Analysis Report
Generated on: ${new Date().toLocaleDateString()}

## Summary
- Total Analyses: ${notes.length}
- Connections: ${connections.length}
- AI Clusters: ${clusters.length}
- Patterns Detected: ${patterns.length}

## Key Insights
${insights?.overallInsights?.map((insight: string) => `- ${insight}`).join('\n') || 'No insights available'}

## Strength Areas
${insights?.strengthAreas?.map((strength: string) => `- ${strength}`).join('\n') || 'No strengths identified'}

## Conversation Gaps
${insights?.conversationGaps?.map((gap: string) => `- ${gap}`).join('\n') || 'No gaps identified'}

## Next Steps
${insights?.nextSteps?.map((step: string) => `- ${step}`).join('\n') || 'No next steps available'}

## Analysis Details
${notes.map((note, index) => `
### Analysis ${index + 1}
- Type: ${note.analysisType}
- Content: ${note.noteText.substring(0, 200)}...
- Tags: ${note.tags?.join(', ') || 'None'}
- Created: ${note.createdAt.toLocaleDateString()}
`).join('\n')}
`;

      const blob = new Blob([reportContent], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `historical-analysis-report-${new Date().toISOString().split('T')[0]}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Report Generated",
        description: "Historical analysis report exported successfully."
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error generating the report.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="absolute top-4 right-4 z-20">
      <Card className="w-80">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Share2 className="h-5 w-5" />
              Export Analysis
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
          <CardDescription>
            Export your historical analysis and AI insights
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button
            onClick={handleExportJSON}
            disabled={isExporting}
            className="w-full justify-start"
            variant="outline"
          >
            <Database className="h-4 w-4 mr-2" />
            Export Raw Data (JSON)
          </Button>
          
          <Button
            onClick={handleExportReport}
            disabled={isExporting}
            className="w-full justify-start"
            variant="outline"
          >
            <FileText className="h-4 w-4 mr-2" />
            Export Insights Report (MD)
          </Button>
          
          <div className="text-xs text-gray-500 mt-3">
            <p>• JSON includes all data and AI analysis</p>
            <p>• Report includes formatted insights and summary</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
