import { Convers<PERSON><PERSON><PERSON><PERSON>, AnalysisR<PERSON>ult, Character<PERSON>ersona } from "@/types/conversation";
import { QuestionContext } from "@/components/conversation-planner/QuestionContextSelector";
import { AnalysisHandlers, AnalysisConfig } from "./openRouter/analysisHandlers";
import { AnalysisOutputParser, ParsedOutput } from "./openRouter/analysisOutputParser";
import { ConversationSectionsParser } from "./openRouter/parsers/conversationSectionsParser";
import { useAnalysisStore } from "@/stores/useAnalysisStore";
import { offlineServiceManager } from "./offline/offlineServiceManager";

// Enhanced error types for better error handling
export class OpenRouterAPIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errorBody?: string,
    public isRateLimited: boolean = false,
    public isAuthError: boolean = false
  ) {
    super(message);
    this.name = 'OpenRouterAPIError';
  }
}

export class OpenRouterService {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Enhanced error handling for API responses
   */
  private async handleAPIResponse(response: Response): Promise<unknown> {
    if (!response.ok) {
      let errorBody = '';
      try {
        errorBody = await response.text();
      } catch (e) {
        errorBody = 'Unable to read error response';
      }

      const isRateLimited = response.status === 429;
      const isAuthError = response.status === 401 || response.status === 403;
      
      let errorMessage = `OpenRouter API error (${response.status}): ${response.statusText}`;
      
      // Provide more specific error messages based on status codes
      switch (response.status) {
        case 400:
          errorMessage = 'Invalid request. Please check your parameters.';
          break;
        case 401:
          errorMessage = 'Invalid API key. Please verify your OpenRouter API key.';
          break;
        case 403:
          errorMessage = 'Access forbidden. Your API key may not have sufficient permissions.';
          break;
        case 429:
          errorMessage = 'Rate limit exceeded. Please wait before making another request.';
          break;
        case 500:
          errorMessage = 'OpenRouter server error. Please try again later.';
          break;
        case 502:
        case 503:
        case 504:
          errorMessage = 'OpenRouter service temporarily unavailable. Please try again later.';
          break;
      }

      console.error("OpenRouter API Error Details:", {
        status: response.status,
        statusText: response.statusText,
        errorBody,
        isRateLimited,
        isAuthError
      });

      throw new OpenRouterAPIError(
        errorMessage,
        response.status,
        errorBody,
        isRateLimited,
        isAuthError
      );
    }

    try {
      return await response.json();
    } catch (error) {
      throw new OpenRouterAPIError('Invalid JSON response from OpenRouter API');
    }
  }

  /**
   * Validate API response data structure
   */
  private validateResponseData(data: unknown): void {
    if (!data) {
      throw new OpenRouterAPIError('No data received from OpenRouter API');
    }

    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      throw new OpenRouterAPIError('Invalid response structure: no choices available');
    }

    const content = data.choices[0]?.message?.content;
    if (!content || typeof content !== 'string') {
      throw new OpenRouterAPIError('No valid content received from OpenRouter API');
    }
  }

  async analyzeQuestion(
    question: string,
    style: ConversationStyle,
    model: string,
    numberOfAnswers: number,
    analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles' = 'multiple',
    characterPersona?: CharacterPersona,
    seedContext?: string,
    questionContext?: QuestionContext,
    selectedEmotions?: string[]
  ): Promise<AnalysisResult> {
    // Check if we should use offline mode
    const networkState = offlineServiceManager.getNetworkState();
    const shouldUseOffline = !networkState.isOnline || offlineServiceManager.shouldUseOfflineMode();

    if (shouldUseOffline) {
      console.log('Using offline analysis due to network conditions');
      return await offlineServiceManager.analyzeQuestion(
        question,
        style,
        model,
        numberOfAnswers,
        analysisType,
        characterPersona,
        seedContext,
        questionContext,
        selectedEmotions
      );
    }

    if (!this.apiKey) {
      throw new OpenRouterAPIError("OpenRouter API key is required", undefined, undefined, false, true);
    }

    if (!question?.trim()) {
      throw new Error("Question is required for analysis");
    }

    if (!model?.trim()) {
      throw new Error("Model selection is required for analysis");
    }

    const { datingContext } = useAnalysisStore.getState();

    const config: AnalysisConfig = {
      question,
      style,
      numberOfAnswers,
      analysisType,
      characterPersona,
      seedContext,
      questionContext,
      selectedEmotions
    };

    const { systemPrompt, userPrompt, temperature, maxTokens } = AnalysisHandlers.buildPrompts(config);

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.href, // Optional: helps with rate limiting
          'X-Title': 'ChatCraft Trainer Pro' // Optional: helps with tracking
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: userPrompt
            }
          ],
          temperature,
          max_tokens: maxTokens,
        }),
      });

      const data = await this.handleAPIResponse(response);
      this.validateResponseData(data);

      const content = data.choices[0].message.content;

      // Parse the raw AI response into structured output
      let parsedOutput: ParsedOutput;
      try {
        parsedOutput = AnalysisOutputParser.parseAnalysisOutput(content, analysisType);
      } catch (parseError) {
        console.warn('Failed to parse analysis output, using fallback:', parseError);
        // Create a fallback parsed output
        parsedOutput = {
          type: 'fallback',
          isFallback: true,
          content: content,
          warning: 'Analysis parsing failed, displaying raw content'
        };
      }
      
      // Use the consolidated parser for follow-up questions (only for non-character analysis)
      let followUpQuestions: string[] = [];
      if (analysisType !== 'character') {
        try {
          const conversationSections = ConversationSectionsParser.parse(content);
          followUpQuestions = conversationSections.followUpQuestions;
        } catch (parseError) {
          console.warn('Failed to parse follow-up questions:', parseError);
          followUpQuestions = [];
        }
      }

      return {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
        question,
        style,
        model,
        analysis: content, // Keep original content for backward compatibility
        parsedOutput, // Add structured parsed output
        timestamp: new Date(),
        followUpQuestions,
        analysisType,
        characterPersona: analysisType === 'character' ? characterPersona : undefined,
        seedContext, // Store seed context for Visual Analysis Function
        questionContext, // Store question context for proper display
        datingContext: style === 'dating' ? datingContext : undefined,
        selectedEmotions: analysisType === 'emotional-angles' ? selectedEmotions : undefined,
      };
    } catch (error) {
      if (error instanceof OpenRouterAPIError) {
        console.error('OpenRouter API call failed:', {
          message: error.message,
          statusCode: error.statusCode,
          isRateLimited: error.isRateLimited,
          isAuthError: error.isAuthError
        });

        // Fall back to offline analysis if online analysis fails
        if (error.statusCode && error.statusCode >= 500) {
          console.log('Server error detected, falling back to offline analysis');
          try {
            return await offlineServiceManager.analyzeQuestion(
              question,
              style,
              model,
              numberOfAnswers,
              analysisType,
              characterPersona,
              seedContext,
              questionContext,
              selectedEmotions
            );
          } catch (offlineError) {
            console.error('Offline fallback also failed:', offlineError);
          }
        }

        throw error;
      } else {
        console.error('Unexpected error during OpenRouter API call:', error);

        // Try offline fallback for unexpected errors too
        try {
          console.log('Attempting offline fallback for unexpected error');
          return await offlineServiceManager.analyzeQuestion(
            question,
            style,
            model,
            numberOfAnswers,
            analysisType,
            characterPersona,
            seedContext,
            questionContext,
            selectedEmotions
          );
        } catch (offlineError) {
          console.error('Offline fallback failed:', offlineError);
          throw new Error(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    }
  }
}
