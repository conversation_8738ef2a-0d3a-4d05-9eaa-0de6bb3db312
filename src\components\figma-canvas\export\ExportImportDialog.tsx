import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Download, 
  Upload, 
  Image, 
  FileText, 
  File,
  Settings,
  Loader2
} from 'lucide-react';
import { ExportOptions, ImportOptions } from '@/types/figma';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';

interface ExportImportDialogProps {
  onExport: (format: string, options: ExportOptions, filename?: string) => Promise<void>;
  onImport: (files: FileList, options: ImportOptions) => Promise<void>;
  className?: string;
}

export const ExportImportDialog: React.FC<ExportImportDialogProps> = ({
  onExport,
  onImport,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export');
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  // Export settings
  const [exportFormat, setExportFormat] = useState<'png' | 'jpg' | 'svg' | 'pdf'>('png');
  const [exportFilename, setExportFilename] = useState('');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'png',
    scale: 1,
    quality: 0.9,
    backgroundColor: '#ffffff',
    includeBackground: true,
    selectedOnly: false,
  });

  // Import settings
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    preserveAspectRatio: true,
    fitToCanvas: false,
    createNewLayer: true,
  });

  const exportFormats = [
    { value: 'png', label: 'PNG Image', icon: Image },
    { value: 'jpg', label: 'JPEG Image', icon: Image },
    { value: 'svg', label: 'SVG Vector', icon: FileText },
    { value: 'pdf', label: 'PDF Document', icon: File },
  ];

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    try {
      const filename = exportFilename.trim() || undefined;
      await onExport(exportFormat, { ...exportOptions, format: exportFormat }, filename);
      setIsOpen(false);
    } catch (error) {
      console.error('Export failed:', error);
      // Show error notification
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0 || isImporting) return;

    setIsImporting(true);
    try {
      await onImport(files, importOptions);
      setIsOpen(false);
    } catch (error) {
      console.error('Import failed:', error);
      // Show error notification
    } finally {
      setIsImporting(false);
    }
  };

  const updateExportOption = (key: keyof ExportOptions, value: unknown) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateImportOption = (key: keyof ImportOptions, value: unknown) => {
    setImportOptions(prev => ({ ...prev, [key]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <Download className="w-4 h-4 mr-2" />
          Export/Import
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Export & Import</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(tab: 'export' | 'import') => setActiveTab(tab)}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </TabsTrigger>
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Import
            </TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-4 mt-4">
            {/* Export Format */}
            <div>
              <Label className="text-sm">Format</Label>
              <Select
                value={exportFormat}
                onValueChange={(value) => setExportFormat(value as ExportOptions['format'])}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {exportFormats.map(format => {
                    const Icon = format.icon;
                    return (
                      <SelectItem key={format.value} value={format.value}>
                        <div className="flex items-center gap-2">
                          <Icon className="w-4 h-4" />
                          {format.label}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Filename */}
            <div>
              <Label className="text-sm">Filename (optional)</Label>
              <Input
                value={exportFilename}
                onChange={(e) => setExportFilename(e.target.value)}
                placeholder={`canvas-export.${exportFormat}`}
                className="mt-1"
              />
            </div>

            {/* Export Options */}
            {['png', 'jpg', 'svg', 'pdf'].includes(exportFormat) && (
              <>
                <Separator />
                
                {/* Scale */}
                <div>
                  <Label className="text-sm">Scale: {exportOptions.scale}x</Label>
                  <Slider
                    value={[exportOptions.scale || 1]}
                    onValueChange={([scale]) => updateExportOption('scale', scale)}
                    min={0.1}
                    max={5}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                {/* Quality (for JPG) */}
                {exportFormat === 'jpg' && (
                  <div>
                    <Label className="text-sm">Quality: {Math.round((exportOptions.quality || 0.8) * 100)}%</Label>
                    <Slider
                      value={[exportOptions.quality || 0.8]}
                      onValueChange={([quality]) => updateExportOption('quality', quality)}
                      min={0.1}
                      max={1}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                )}

                {/* Background */}
                {exportFormat !== 'svg' && (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeBackground"
                        checked={exportOptions.includeBackground}
                        onCheckedChange={(checked) => updateExportOption('includeBackground', checked)}
                      />
                      <Label htmlFor="includeBackground" className="text-sm">Include background</Label>
                    </div>
                    
                    {exportOptions.includeBackground && (
                      <div className="flex items-center gap-2">
                        <Label className="text-sm">Color:</Label>
                        <Input
                          type="color"
                          value={exportOptions.backgroundColor || '#ffffff'}
                          onChange={(e) => updateExportOption('backgroundColor', e.target.value)}
                          className="w-12 h-8 p-1"
                        />
                        <Input
                          value={exportOptions.backgroundColor || '#ffffff'}
                          onChange={(e) => updateExportOption('backgroundColor', e.target.value)}
                          className="flex-1 h-8 text-xs font-mono"
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Selected Only */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="selectedOnly"
                    checked={exportOptions.selectedOnly}
                    onCheckedChange={(checked) => updateExportOption('selectedOnly', checked)}
                  />
                  <Label htmlFor="selectedOnly" className="text-sm">Export selected objects only</Label>
                </div>
              </>
            )}

            {/* Export Button */}
            <Button 
              onClick={handleExport} 
              disabled={isExporting}
              className="w-full"
            >
              {isExporting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export {exportFormat.toUpperCase()}
                </>
              )}
            </Button>
          </TabsContent>

          <TabsContent value="import" className="space-y-4 mt-4">
            {/* Import Options */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="preserveAspectRatio"
                  checked={importOptions.preserveAspectRatio}
                  onCheckedChange={(checked) => updateImportOption('preserveAspectRatio', checked)}
                />
                <Label htmlFor="preserveAspectRatio" className="text-sm">Preserve aspect ratio</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="fitToCanvas"
                  checked={importOptions.fitToCanvas}
                  onCheckedChange={(checked) => updateImportOption('fitToCanvas', checked)}
                />
                <Label htmlFor="fitToCanvas" className="text-sm">Fit to canvas</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="createNewLayer"
                  checked={importOptions.createNewLayer}
                  onCheckedChange={(checked) => updateImportOption('createNewLayer', checked)}
                />
                <Label htmlFor="createNewLayer" className="text-sm">Create new layer</Label>
              </div>
            </div>

            <Separator />

            {/* File Input */}
            <div>
              <Label className="text-sm">Select files to import</Label>
              <Input
                type="file"
                accept="image/*,.json,.svg"
                multiple
                onChange={handleImport}
                disabled={isImporting}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                Supported: Images (PNG, JPG, SVG), Canvas data (JSON)
              </p>
            </div>

            {isImporting && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-6 h-6 animate-spin mr-2" />
                <span className="text-sm">Importing files...</span>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
