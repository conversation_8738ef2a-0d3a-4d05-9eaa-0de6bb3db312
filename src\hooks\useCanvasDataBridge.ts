/**
 * Canvas Data Bridge Hook
 * 
 * Provides seamless data transfer between the main application and the Visual Canvas.
 * Handles analysis results, user interactions, and state synchronization.
 */

import { useState, useEffect, useCallback } from 'react';

interface CanvasDataBridgeState {
  analysisData: AnalysisResult[];
  selectedAnalysis: AnalysisResult | null;
  canvasReady: boolean;
  lastSync: number;
}

interface CanvasDataBridge {
  // State
  state: CanvasDataBridgeState;
  
  // Data management
  sendAnalysisToCanvas: (analysis: AnalysisResult | AnalysisResult[]) => void;
  clearCanvasData: () => void;
  syncWithStores: () => void;
  
  // Navigation integration
  openCanvasWithAnalysis: (analysis: AnalysisResult, mode?: 'standard' | 'safe' | 'chat-analysis') => void;
  returnFromCanvas: () => void;
  
  // Canvas state management
  setCanvasReady: (ready: boolean) => void;
  selectAnalysis: (analysis: AnalysisResult | null) => void;
  
  // Data retrieval
  getCanvasData: () => any;
  getAnalysisById: (id: string) => AnalysisResult | null;
}

export const useCanvasDataBridge = (): CanvasDataBridge => {
  const [state, setState] = useState<CanvasDataBridgeState>({
    analysisData: [],
    selectedAnalysis: null,
    canvasReady: false,
    lastSync: Date.now(),
  });

  const analysisStore = useAnalysisStore();
  const libraryStore = useLibraryStore();
  const navigationStore = useNavigationStore();

  // Sync with analysis store
  const syncWithStores = useCallback(() => {
    const allAnalysis = [
      ...analysisStore.analysisResults,
      ...libraryStore.savedAnalyses,
    ];

    setState(prev => ({
      ...prev,
      analysisData: allAnalysis,
      lastSync: Date.now(),
    }));
  }, [analysisStore.analysisResults, libraryStore.savedAnalyses]);

  // Auto-sync when stores change
  useEffect(() => {
    syncWithStores();
  }, [syncWithStores]);

  // Send analysis data to canvas
  const sendAnalysisToCanvas = useCallback((analysis: AnalysisResult | AnalysisResult[]) => {
    const analysisArray = Array.isArray(analysis) ? analysis : [analysis];
    
    // Store in sessionStorage for canvas consumption
    sessionStorage.setItem('canvasAnalysisData', JSON.stringify(analysisArray));
    
    setState(prev => ({
      ...prev,
      analysisData: [...prev.analysisData, ...analysisArray],
      selectedAnalysis: analysisArray[0] || prev.selectedAnalysis,
    }));
  }, []);

  // Clear canvas data
  const clearCanvasData = useCallback(() => {
    sessionStorage.removeItem('canvasAnalysisData');
    sessionStorage.removeItem('canvasData');
    
    setState(prev => ({
      ...prev,
      analysisData: [],
      selectedAnalysis: null,
    }));
  }, []);

  // Open canvas with specific analysis
  const openCanvasWithAnalysis = useCallback((
    analysis: AnalysisResult,
    mode: 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated' = 'figma-integrated'
  ) => {
    console.log('🧭 Canvas Data Bridge - opening canvas with analysis:', {
      analysisId: analysis.id,
      mode,
      currentTab: navigationStore.mainTab
    });

    // Prepare analysis data for canvas
    sendAnalysisToCanvas(analysis);

    // Set canvas mode
    navigationStore.setCanvasMode(mode);

    // Navigate to canvas
    navigationStore.setMainTab('canvas');

    // Store additional context
    const context = {
      sourceTab: navigationStore.mainTab,
      analysisId: analysis.id,
      timestamp: Date.now(),
    };
    sessionStorage.setItem('canvasContext', JSON.stringify(context));
    console.log('🧭 Canvas context stored:', context);
  }, [sendAnalysisToCanvas, navigationStore]);

  // Return from canvas
  const returnFromCanvas = useCallback(() => {
    const context = sessionStorage.getItem('canvasContext');
    if (context) {
      try {
        const { sourceTab } = JSON.parse(context);
        navigationStore.setMainTab(sourceTab || 'analyze');
        sessionStorage.removeItem('canvasContext');
      } catch (error) {
        navigationStore.setMainTab('analyze');
      }
    } else {
      navigationStore.setMainTab('analyze');
    }
  }, [navigationStore]);

  // Set canvas ready state
  const setCanvasReady = useCallback((ready: boolean) => {
    setState(prev => ({
      ...prev,
      canvasReady: ready,
    }));
  }, []);

  // Select analysis
  const selectAnalysis = useCallback((analysis: AnalysisResult | null) => {
    setState(prev => ({
      ...prev,
      selectedAnalysis: analysis,
    }));
  }, []);

  // Get canvas data for consumption
  const getCanvasData = useCallback(() => {
    const sessionData = sessionStorage.getItem('canvasAnalysisData');
    const analysisData = sessionData ? JSON.parse(sessionData) : state.analysisData;
    
    return {
      analysisResults: analysisData,
      selectedAnalysis: state.selectedAnalysis,
      metadata: {
        totalCount: analysisData.length,
        lastSync: state.lastSync,
        canvasReady: state.canvasReady,
      },
    };
  }, [state]);

  // Get analysis by ID
  const getAnalysisById = useCallback((id: string): AnalysisResult | null => {
    return state.analysisData.find(analysis => analysis.id === id) || null;
  }, [state.analysisData]);

  return {
    state,
    sendAnalysisToCanvas,
    clearCanvasData,
    syncWithStores,
    openCanvasWithAnalysis,
    returnFromCanvas,
    setCanvasReady,
    selectAnalysis,
    getCanvasData,
    getAnalysisById,
  };
};

// Canvas Data Provider Hook
export const useCanvasDataProvider = () => {
  const bridge = useCanvasDataBridge();
  
  // Provide data to canvas components
  useEffect(() => {
    // Listen for canvas ready events
    const handleCanvasReady = () => {
      bridge.setCanvasReady(true);
      bridge.syncWithStores();
    };

    // Listen for canvas data requests
    const handleDataRequest = (event: CustomEvent) => {
      const data = bridge.getCanvasData();
      
      // Send data back to canvas
      window.dispatchEvent(new CustomEvent('canvasDataResponse', {
        detail: data,
      }));
    };

    window.addEventListener('canvasReady', handleCanvasReady);
    window.addEventListener('canvasDataRequest', handleDataRequest as EventListener);

    return () => {
      window.removeEventListener('canvasReady', handleCanvasReady);
      window.removeEventListener('canvasDataRequest', handleDataRequest as EventListener);
    };
  }, [bridge]);

  return bridge;
};

// Canvas Data Consumer Hook (for use in canvas components)
export const useCanvasDataConsumer = () => {
  const [canvasData, setCanvasData] = useState<unknown>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Request data from the bridge
    const requestData = () => {
      window.dispatchEvent(new CustomEvent('canvasDataRequest'));
    };

    // Listen for data response
    const handleDataResponse = (event: CustomEvent) => {
      setCanvasData(event.detail);
      setIsLoading(false);
    };

    // Notify that canvas is ready
    const notifyReady = () => {
      window.dispatchEvent(new CustomEvent('canvasReady'));
    };

    window.addEventListener('canvasDataResponse', handleDataResponse as EventListener);

    // Initial data request
    requestData();
    notifyReady();

    return () => {
      window.removeEventListener('canvasDataResponse', handleDataResponse as EventListener);
    };
  }, []);

  return {
    canvasData,
    isLoading,
    refreshData: () => {
      setIsLoading(true);
      window.dispatchEvent(new CustomEvent('canvasDataRequest'));
    },
  };
};

export default useCanvasDataBridge;
