
import { UserNote } from "@/types/conversation";
import { useHistoricalAnalysisSettingsStore } from "@/stores/useHistoricalAnalysisSettingsStore";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { AIServiceAPIHandler, CombinedAISettings } from "./aiHistoricalService/apiHandler";
import { AIHistoricalPromptBuilder } from "./aiHistoricalService/promptBuilder";
import {
  PatternAnalysis,
  RelationshipMap,
  InsightSummary,
  ConnectionSuggestion,
} from "./aiHistoricalService/types";
import { safeParseJsonResponse } from "./aiHistoricalService/jsonUtils";

export class AIHistoricalService {
  private apiHandler: AIServiceAPIHandler;

  constructor() {
    this.apiHandler = new AIServiceAPIHandler();
  }

  private getSettings(): CombinedAISettings {
    const mainSettings = useSettingsStore.getState().settings;
    const historicalSettings = useHistoricalAnalysisSettingsStore.getState().settings;
    
    return {
      ...historicalSettings,
      openRouterApiKey: mainSettings.openRouterApiKey,
      selectedModel: mainSettings.selectedModel,
    };
  }

  async analyzePatterns(notes: UserNote[]): Promise<PatternAnalysis> {
    const settings = this.getSettings();
    if (!settings.enablePatternAnalysis) {
      throw new Error("Pattern analysis is disabled in settings");
    }

    const { systemPrompt, userPrompt } = AIHistoricalPromptBuilder.buildPatternAnalysisPrompts(notes, settings);

    const response = await this.apiHandler.makeAPIRequest(userPrompt, systemPrompt, settings);
    
    try {
      return safeParseJsonResponse<PatternAnalysis>(response);
    } catch (error) {
      console.error('Failed to parse pattern analysis response:', error);
      throw error;
    }
  }

  async generateRelationshipMap(notes: UserNote[]): Promise<RelationshipMap> {
    const settings = this.getSettings();
    if (!settings.enableRelationshipMapping) {
      throw new Error("Relationship mapping is disabled in settings");
    }

    const { systemPrompt, userPrompt } = AIHistoricalPromptBuilder.buildRelationshipMapPrompts(notes, settings);

    const response = await this.apiHandler.makeAPIRequest(userPrompt, systemPrompt, settings);
    
    try {
      let parsedData = safeParseJsonResponse<unknown>(response);
      // The AI sometimes wraps the response in a "RelationshipMap" object.
      // This ensures we always return the core RelationshipMap object.
      if (parsedData && parsedData.RelationshipMap) {
        parsedData = parsedData.RelationshipMap;
      }
      return parsedData as RelationshipMap;
    } catch (error) {
      console.error('Failed to parse relationship mapping response:', error);
      throw error;
    }
  }

  async createInsightSummary(notes: UserNote[]): Promise<InsightSummary> {
    const settings = this.getSettings();
    if (!settings.enableInsightGeneration) {
      throw new Error("Insight generation is disabled in settings");
    }

    if (notes.length === 0) {
      return {
        overallInsights: [],
        keyPatterns: [],
        conversationGaps: [],
        strengthAreas: [],
        improvementAreas: [],
        nextSteps: [],
        confidence: 0,
      };
    }

    const { systemPrompt, userPrompt } = AIHistoricalPromptBuilder.buildInsightSummaryPrompts(notes, settings);

    const response = await this.apiHandler.makeAPIRequest(userPrompt, systemPrompt, settings);
    
    try {
      return safeParseJsonResponse<InsightSummary>(response);
    } catch (error) {
      console.error('Failed to parse insight summary response:', error);
      throw error;
    }
  }

  async suggestConnections(note: UserNote, allNotes: UserNote[]): Promise<ConnectionSuggestion[]> {
    const settings = this.getSettings();
    if (!settings.enableAutoConnections) {
      return [];
    }

    const otherNotes = allNotes.filter(n => n.id !== note.id);
    if (otherNotes.length === 0) return [];

    const { systemPrompt, userPrompt } = AIHistoricalPromptBuilder.buildConnectionSuggestionPrompts(note, otherNotes, settings);

    const response = await this.apiHandler.makeAPIRequest(userPrompt, systemPrompt, settings);
    
    try {
      return safeParseJsonResponse<ConnectionSuggestion[]>(response);
    } catch (error) {
      console.error('Failed to parse connection suggestions response:', error);
      return [];
    }
  }

  getTokenUsageStats() {
    return this.apiHandler.getTokenUsageStats();
  }

  isAPIConfigured(): boolean {
    return this.apiHandler.isAPIConfigured();
  }

  canMakeRequest(): boolean {
    return this.apiHandler.canMakeRequest();
  }
}
