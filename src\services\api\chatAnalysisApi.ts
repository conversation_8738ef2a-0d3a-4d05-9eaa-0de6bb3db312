import { AnalysisCluster, ChatSimulation, ConnectionData } from '@/components/visual-analysis/types';
import { AnalysisResult } from '@/types/conversation';

/**
 * API service for chat analysis records CRUD operations
 */
export class ChatAnalysisApi {
  private static baseUrl = '/api/chat-analysis';

  /**
   * Get all analysis results
   */
  static async getAllAnalysisResults(): Promise<AnalysisResult[]> {
    try {
      const response = await fetch(`${this.baseUrl}/results`);
      if (!response.ok) {
        throw new Error(`Failed to fetch analysis results: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching analysis results:', error);
      // Return mock data for development
      return this.getMockAnalysisResults();
    }
  }

  /**
   * Get analysis result by ID
   */
  static async getAnalysisResult(id: string): Promise<AnalysisResult | null> {
    try {
      const response = await fetch(`${this.baseUrl}/results/${id}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch analysis result: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching analysis result:', error);
      return null;
    }
  }

  /**
   * Create new analysis result
   */
  static async createAnalysisResult(analysisResult: Omit<AnalysisResult, 'id' | 'timestamp'>): Promise<AnalysisResult> {
    try {
      const response = await fetch(`${this.baseUrl}/results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisResult),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create analysis result: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating analysis result:', error);
      // Return mock created result for development
      const mockResult: AnalysisResult = {
        ...analysisResult,
        id: `mock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: new Date(),
      };
      return mockResult;
    }
  }

  /**
   * Update analysis result
   */
  static async updateAnalysisResult(id: string, updates: Partial<AnalysisResult>): Promise<AnalysisResult> {
    try {
      const response = await fetch(`${this.baseUrl}/results/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update analysis result: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating analysis result:', error);
      throw error;
    }
  }

  /**
   * Delete analysis result
   */
  static async deleteAnalysisResult(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/results/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete analysis result: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting analysis result:', error);
      throw error;
    }
  }

  /**
   * Archive analysis result
   */
  static async archiveAnalysisResult(id: string): Promise<AnalysisResult> {
    return this.updateAnalysisResult(id, { 
      // Add archived flag to the analysis result
      archived: true,
      archivedAt: new Date().toISOString()
    });
  }

  /**
   * Duplicate analysis result
   */
  static async duplicateAnalysisResult(id: string): Promise<AnalysisResult> {
    const original = await this.getAnalysisResult(id);
    if (!original) {
      throw new Error('Analysis result not found');
    }

    const duplicate = {
      ...original,
      question: `${original.question} (Copy)`,
      // Remove ID and timestamp to create new record
    };
    
    // Remove fields that should be regenerated
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _id, timestamp: _timestamp, ...rest } = duplicate;
    // Remove metadata if present (not in AnalysisResult type)
    if ('metadata' in rest) {
      delete (rest as Record<string, unknown>).metadata;
    }
    // Remove 'archived' property if present (not in AnalysisResult type)
    if ('archived' in rest) {
      delete (rest as Record<string, unknown>).archived;
    }
    
    return this.createAnalysisResult(rest);
  }

  /**
   * Search analysis results
   */
  static async searchAnalysisResults(query: string, filters?: {
    analysisType?: string;
    style?: string;
    model?: string;
    dateRange?: { start: string; end: string };
  }): Promise<AnalysisResult[]> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            if (typeof value === 'object') {
              params.append(key, JSON.stringify(value));
            } else {
              params.append(key, value);
            }
          }
        });
      }

      const response = await fetch(`${this.baseUrl}/results/search?${params}`);
      if (!response.ok) {
        throw new Error(`Failed to search analysis results: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error searching analysis results:', error);
      // Return filtered mock data for development
      const allResults = this.getMockAnalysisResults();
      return allResults.filter(result => 
        result.question.toLowerCase().includes(query.toLowerCase()) ||
        result.analysis.toLowerCase().includes(query.toLowerCase())
      );
    }
  }

  /**
   * Get analysis statistics
   */
  static async getAnalysisStatistics(): Promise<{
    total: number;
    byType: Record<string, number>;
    byStyle: Record<string, number>;
    byModel: Record<string, number>;
    recentActivity: { date: string; count: number }[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/statistics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch statistics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching statistics:', error);
      // Return mock statistics
      return {
        total: 25,
        byType: {
          'multiple': 8,
          'deep': 6,
          'character': 4,
          'pros-cons': 3,
          'six-hats': 2,
          'emotional-angles': 2
        },
        byStyle: {
          'detailed': 12,
          'concise': 8,
          'creative': 5
        },
        byModel: {
          'gpt-4': 15,
          'gpt-3.5-turbo': 10
        },
        recentActivity: [
          { date: '2024-01-15', count: 3 },
          { date: '2024-01-14', count: 5 },
          { date: '2024-01-13', count: 2 },
          { date: '2024-01-12', count: 4 },
          { date: '2024-01-11', count: 1 }
        ]
      };
    }
  }

  /**
   * Mock data for development
   */
  private static getMockAnalysisResults(): AnalysisResult[] {
    return [
      {
        id: 'mock_1',
        question: 'How can AI improve healthcare outcomes?',
        analysis: 'AI has tremendous potential to revolutionize healthcare through predictive analytics, personalized treatment plans, and early disease detection...',
        analysisType: 'multiple',
        style: 'professional',
        model: 'gpt-4',
        timestamp: new Date('2024-01-15T10:30:00Z'),
        rating: 4,
        followUpQuestions: [
          'What are the ethical considerations?',
          'How can we ensure data privacy?'
        ]
      },
      {
        id: 'mock_2',
        question: 'What are the benefits of renewable energy?',
        analysis: 'Renewable energy sources offer numerous advantages including environmental sustainability, economic benefits, and energy independence...',
        analysisType: 'pros-cons',
        style: 'professional',
        model: 'gpt-4',
        timestamp: new Date('2024-01-14T15:45:00Z'),
        rating: 5,
        followUpQuestions: [
          'What are the main challenges?',
          'How can we accelerate adoption?'
        ]
      },
      {
        id: 'mock_3',
        question: 'Explain quantum computing concepts',
        analysis: 'Quantum computing leverages quantum mechanical phenomena like superposition and entanglement to process information in fundamentally different ways...',
        analysisType: 'deep',
        style: 'professional',
        model: 'gpt-4',
        timestamp: new Date('2024-01-13T09:15:00Z'),
        rating: 5,
        followUpQuestions: [
          'What are practical applications?',
          'When will it become mainstream?'
        ]
      }
    ];
  }
}
