import React from "react";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "lucide-react";
import { SavedAnalysis } from "@/types/conversation";
import { useUIStore } from "@/stores/useUIStore";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FormattedContentDisplay } from "../conversation-planner/result-card/analysis-display/ContentFormatter";
import { Separator } from "@/components/ui/separator";
import { OptimizedList } from "@/components/conversation-planner/visualization/PerformanceOptimizer";

export const SavedAnalysisDialog: React.FC = () => {
  const { modal, closeModal } = useUIStore();
  const isOpen = modal.type === "SAVED_ANALYSIS_VIEWER";
  const { analysis } = (modal.props || {}) as { analysis?: SavedAnalysis };

  if (!isOpen || !analysis) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeModal()}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{analysis.title}</DialogTitle>
          <DialogDescription className="flex items-center gap-4 flex-wrap pt-2">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4 mr-1" />
              Created on {analysis.createdAt.toLocaleDateString()}
            </div>
            <Badge variant="outline">
              {analysis.results.length} result{analysis.results.length !== 1 ? "s" : ""}
            </Badge>
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-1 my-4 pr-6">
          <OptimizedList
            items={analysis.results}
            renderItem={(result, index) => (
              <div key={result.id}>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold">Q: {result.question}</h3>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                    <Badge variant="secondary">{result.style}</Badge>
                    <Badge variant="secondary">{result.model}</Badge>
                    <span>{result.timestamp.toLocaleString()}</span>
                  </div>
                </div>
                <FormattedContentDisplay content={result.analysis} />
                {index < analysis.results.length - 1 && <Separator className="mt-6" />}
              </div>
            )}
            itemHeight={160} // Adjust as needed for your result card height
            containerHeight={600} // Adjust as needed for your scrollable area
            className="space-y-6"
          />
        </ScrollArea>
        <DialogFooter>
          <Button variant="outline" onClick={closeModal}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
