import { useEffect, useRef } from 'react'; // Added useRef
import { Canvas, Group, Line, FabricObject } from 'fabric'; // Added FabricObject
import { UserNote } from '@/types/conversation';
import { Connection, NodePosition } from '@/stores/useCanvasStore';
import { 
    createFabricNode, 
    CustomLineObject, 
    GRID_SIZE, 
    NODE_HEIGHT, 
    NODE_WIDTH 
} from '../fabric-helpers';

interface FabricRendererProps {
    canvas: Canvas | null;
    notes: UserNote[];
    connections: Connection[];
    nodePositions: NodePosition[];
    selectedNodes: string[];
}

export const useFabricRenderer = ({
    canvas,
    notes,
    connections,
    nodePositions,
    selectedNodes,
}: FabricRendererProps) => {
    // Ref to track if initial render is done
    const initialRenderDone = useRef(false);

    useEffect(() => {
        if (!canvas) return;

        // If not initial render and only selectedNodes changed, update existing objects
        if (initialRenderDone.current && canvas.getObjects().length > 0) {            canvas.getObjects().forEach(obj => {
                const fabricObj = obj as FabricObject & { data?: { type: string; id: string; fromId?: string; toId?: string } }; // Type assertion
                if (fabricObj.data?.type === 'node') {
                    const isSelected = selectedNodes.includes(fabricObj.data.id);
                    // Update visual properties based on selection
                    fabricObj.set({
                        stroke: isSelected ? 'hsl(var(--primary))' : 'hsl(var(--ring))',
                        strokeWidth: isSelected ? 2 : 1,
                    });                } else if (fabricObj.data?.type === 'connection') {
                    const line = fabricObj as unknown as CustomLineObject;
                    const isFromSelected = selectedNodes.includes(line.data.fromId);
                    const isToSelected = selectedNodes.includes(line.data.toId);
                    const isHighlighted = isFromSelected || isToSelected;
                    line.set({
                        stroke: isHighlighted ? 'hsl(var(--primary))' : 'hsl(var(--border))',
                        strokeWidth: isHighlighted ? 2.5 : 1.5,
                    });
                }
            });
            canvas.requestRenderAll();
            return; // Skip full redraw
        }

        // Full redraw for initial render or other changes
        try {
            canvas.clear();

            const gridGroup = new Group([], { selectable: false, evented: false, left: -2500, top: -2500 });
            const width = 5000, height = 5000;
            for (let i = 0; i < (width / GRID_SIZE); i++) gridGroup.add(new Line([i * GRID_SIZE, 0, i * GRID_SIZE, height], { stroke: 'hsl(var(--border))', evented: false }));
            for (let i = 0; i < (height / GRID_SIZE); i++) gridGroup.add(new Line([0, i * GRID_SIZE, width, i * GRID_SIZE], { stroke: 'hsl(var(--border))', evented: false }));
            canvas.add(gridGroup);
          connections.forEach(conn => {
            const fromPos = nodePositions.find(p => p.id === conn.fromId);
            const toPos = nodePositions.find(p => p.id === conn.toId);
            if (fromPos && toPos && 
                fromPos.x !== undefined && fromPos.y !== undefined &&
                toPos.x !== undefined && toPos.y !== undefined) {
                const isFromSelected = selectedNodes.includes(conn.fromId);
                const isToSelected = selectedNodes.includes(conn.toId);
                const isHighlighted = isFromSelected || isToSelected;

                try {
                    const line = new Line(
                        [
                            fromPos.x + NODE_WIDTH / 2, 
                            fromPos.y + NODE_HEIGHT / 2, 
                            toPos.x + NODE_WIDTH / 2, 
                            toPos.y + NODE_HEIGHT / 2
                        ],
                        { 
                            stroke: isHighlighted ? 'hsl(var(--primary))' : 'hsl(var(--border))', 
                            strokeWidth: isHighlighted ? 2.5 : 1.5, 
                            evented: false, 
                            objectCaching: false 
                        }
                    ) as CustomLineObject;

                    line.data = {
                        type: 'connection',
                        fromId: conn.fromId,
                        toId: conn.toId
                    };
                    
                    canvas.add(line);
                } catch (error) {
                    console.warn('Failed to create connection line:', error);
                }
            }
        });        notes.forEach(note => {
            const pos = nodePositions.find(p => p.id === note.id);
            if (pos && pos.x !== undefined && pos.y !== undefined) {
                try {
                    const isSelected = selectedNodes.includes(note.id);
                    const nodeObject = createFabricNode(note, isSelected);
                    nodeObject.set({ left: pos.x, top: pos.y });
                    canvas.add(nodeObject);
                } catch (error) {
                    console.warn('Failed to create or position node:', error);
                }
            }
        });        
        canvas.renderAll();
            initialRenderDone.current = true; // Mark initial render as done
        } catch (error) {
            console.error('Canvas rendering failed:', error);
        }
    }, [canvas, notes, connections, nodePositions, selectedNodes]);
};
