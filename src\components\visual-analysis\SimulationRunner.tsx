import React, { useState, useCallback, useEffect } from 'react';
import { ChatSimulation, SimulationResult } from './types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw,
  X, 
  MessageSquare,
  Clock,
  Zap,
  BarChart3,
  Download,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';

interface SimulationRunnerProps {
  simulation: ChatSimulation;
  position: { x: number; y: number };
  onUpdateSimulation: (simulationId: string, updates: Partial<ChatSimulation>) => void;
  onClose: () => void;
}

export const SimulationRunner: React.FC<SimulationRunnerProps> = ({
  simulation,
  position,
  onUpdateSimulation,
  onClose
}) => {
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [results, setResults] = useState<SimulationResult[]>(simulation.results || []);
  const [currentResponse, setCurrentResponse] = useState('');
  const [progress, setProgress] = useState(0);

  const handleStartSimulation = useCallback(async () => {
    setIsRunning(true);
    setIsPaused(false);
    setCurrentPromptIndex(0);
    setResults([]);
    setProgress(0);
    
    // Update simulation status
    onUpdateSimulation(simulation.id, { 
      status: 'running',
      results: []
    });

    // Simulate running prompts
    for (let i = 0; i < simulation.prompts.length; i++) {
      if (!isRunning || isPaused) break;
      
      setCurrentPromptIndex(i);
      const prompt = simulation.prompts[i];
      
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Generate mock response (in real implementation, this would call the AI API)
        const mockResponse = await generateMockResponse(prompt.content);
        
        const result: SimulationResult = {
          id: `result_${Date.now()}_${i}`,
          promptId: prompt.id,
          response: mockResponse,
          timestamp: new Date(),
          metadata: {
            model: 'gpt-4',
            tokens: Math.floor(Math.random() * 500) + 100,
            duration: Math.floor(Math.random() * 3000) + 1000
          }
        };
        
        setResults(prev => [...prev, result]);
        setCurrentResponse(mockResponse);
        setProgress(((i + 1) / simulation.prompts.length) * 100);
        
      } catch (error) {
        console.error('Simulation error:', error);
        // Handle error
        break;
      }
    }
    
    // Complete simulation
    setIsRunning(false);
    onUpdateSimulation(simulation.id, { 
      status: 'completed',
      results: results
    });
  }, [simulation, isRunning, isPaused, results, onUpdateSimulation]);

  const handlePauseSimulation = useCallback(() => {
    setIsPaused(true);
    setIsRunning(false);
  }, []);

  const handleResumeSimulation = useCallback(() => {
    setIsPaused(false);
    setIsRunning(true);
    // Continue from current prompt
  }, []);

  const handleStopSimulation = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    onUpdateSimulation(simulation.id, { 
      status: 'completed',
      results: results
    });
  }, [simulation.id, results, onUpdateSimulation]);

  const handleResetSimulation = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    setCurrentPromptIndex(0);
    setResults([]);
    setCurrentResponse('');
    setProgress(0);
    onUpdateSimulation(simulation.id, { 
      status: 'pending',
      results: []
    });
  }, [simulation.id, onUpdateSimulation]);

  const generateMockResponse = async (promptContent: string): Promise<string> => {
    // Mock AI response generation
    const responses = [
      "This is a fascinating topic that deserves careful consideration. Let me explore multiple perspectives on this matter...",
      "From my analysis, I can see several key factors at play here. The complexity of this issue requires us to examine...",
      "Building on the previous discussion, I'd like to delve deeper into the underlying principles that govern...",
      "This presents an interesting challenge that can be approached from various angles. Consider the following aspects...",
      "The implications of this topic extend far beyond the surface level. Let's examine the broader context and..."
    ];
    
    const baseResponse = responses[Math.floor(Math.random() * responses.length)];
    const additionalContent = " ".repeat(Math.floor(Math.random() * 200) + 100);
    
    return baseResponse + additionalContent.replace(/ /g, ' detailed analysis continues with comprehensive insights and thoughtful examination of the core concepts.');
  };

  const modalStyle: React.CSSProperties = {
    position: 'absolute',
    left: Math.min(position.x, window.innerWidth - 700),
    top: Math.min(position.y, window.innerHeight - 600),
    zIndex: 25,
    width: '680px',
    height: '580px',
    backgroundColor: 'rgba(20, 20, 40, 0.98)',
    backdropFilter: 'blur(15px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    boxShadow: '0 12px 48px rgba(0, 0, 0, 0.6)',
  };

  return (
    <Card style={modalStyle}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-base flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            Simulation Runner: {simulation.name}
          </CardTitle>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Status Bar */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge 
              variant={simulation.status === 'running' ? 'default' : 'secondary'}
              className={`${
                simulation.status === 'running' ? 'bg-green-600' : 
                simulation.status === 'completed' ? 'bg-blue-600' : 'bg-gray-600'
              }`}
            >
              {simulation.status}
            </Badge>
            <span className="text-gray-400 text-sm">
              {simulation.prompts.length} prompts • {results.length} completed
            </span>
          </div>
          
          {isRunning && (
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Clock className="w-4 h-4" />
              Running prompt {currentPromptIndex + 1} of {simulation.prompts.length}
            </div>
          )}
        </div>
        
        {/* Progress Bar */}
        {(isRunning || results.length > 0) && (
          <div className="mt-2">
            <Progress value={progress} className="h-2" />
          </div>
        )}
      </CardHeader>
      
      <CardContent className="h-full overflow-hidden flex flex-col">
        {/* Control Buttons */}
        <div className="flex gap-2 mb-4">
          {!isRunning && !isPaused && (
            <Button
              onClick={handleStartSimulation}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Simulation
            </Button>
          )}
          
          {isRunning && (
            <Button
              onClick={handlePauseSimulation}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              <Pause className="w-4 h-4 mr-2" />
              Pause
            </Button>
          )}
          
          {isPaused && (
            <Button
              onClick={handleResumeSimulation}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Play className="w-4 h-4 mr-2" />
              Resume
            </Button>
          )}
          
          {(isRunning || isPaused) && (
            <Button
              onClick={handleStopSimulation}
              variant="destructive"
            >
              <Square className="w-4 h-4 mr-2" />
              Stop
            </Button>
          )}
          
          <Button
            onClick={handleResetSimulation}
            variant="outline"
            className="border-white/20 text-white hover:bg-white/10"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          
          {results.length > 0 && (
            <Button
              onClick={() => {
                // Export results functionality
                const dataStr = JSON.stringify(results, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `simulation_${simulation.name}_results.json`;
                link.click();
              }}
              variant="outline"
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
        
        {/* Results Display */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="space-y-4">
              {results.map((result, index) => (
                <div key={result.id} className="bg-white/5 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="w-4 h-4 text-blue-400" />
                      <span className="text-white font-medium text-sm">
                        {simulation.prompts.find(p => p.id === result.promptId)?.name || 'Unknown Prompt'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-400">
                      <span>{result.metadata?.tokens} tokens</span>
                      <span>{result.metadata?.duration}ms</span>
                    </div>
                  </div>
                  
                  <div className="text-gray-300 text-sm mb-2 max-h-32 overflow-y-auto">
                    {result.response}
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {result.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
              
              {/* Current Running Prompt */}
              {isRunning && currentResponse && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-green-400 font-medium text-sm">
                      Currently Processing: {simulation.prompts[currentPromptIndex]?.name}
                    </span>
                  </div>
                  <div className="text-gray-300 text-sm">
                    {currentResponse}
                  </div>
                </div>
              )}
              
              {results.length === 0 && !isRunning && (
                <div className="text-center py-12 text-gray-400">
                  <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg mb-2">Ready to Run Simulation</p>
                  <p className="text-sm">Click "Start Simulation" to begin processing prompts</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
};
