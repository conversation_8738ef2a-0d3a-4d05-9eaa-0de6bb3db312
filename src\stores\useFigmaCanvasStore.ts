import { create } from 'zustand';
import {
  CanvasState,
  DrawingObject,
  Layer,
  DrawingTool,
  Point,
  Transform,
  ToolSettings,
  ExportOptions,
  FigmaCanvasEvent
} from '@/types/figma';
import { persist } from 'zustand/middleware';
import { generateUUID } from '@/utils/uuid';
import { indexedDBManager } from '@/lib/storage/IndexedDBManager';

interface FigmaCanvasStore extends CanvasState {
  // Tool settings
  toolSettings: ToolSettings;
  
  // History for undo/redo
  history: CanvasState[];
  historyIndex: number;
  maxHistorySize: number;
  
  // Actions
  setActiveTool: (tool: DrawingTool) => void;
  setToolSettings: (settings: Partial<ToolSettings>) => void;
  
  // Object management
  addObject: (object: Omit<DrawingObject, 'id' | 'createdAt' | 'updatedAt'>) => string;
  updateObject: (id: string, updates: Partial<DrawingObject>) => void;
  deleteObject: (id: string) => void;
  duplicateObject: (id: string) => string;
  
  // Selection management
  selectObjects: (ids: string[]) => void;
  addToSelection: (id: string) => void;
  removeFromSelection: (id: string) => void;
  clearSelection: () => void;
  selectAll: () => void;
  
  // Layer management
  addLayer: (name: string) => string;
  updateLayer: (id: string, updates: Partial<Layer>) => void;
  deleteLayer: (id: string) => void;
  setActiveLayer: (id: string) => void;
  reorderLayers: (layerIds: string[]) => void;
  
  // Transform operations
  moveObjects: (ids: string[], delta: Point) => void;
  resizeObjects: (ids: string[], transform: Partial<Transform>) => void;
  rotateObjects: (ids: string[], angle: number) => void;
  
  // Canvas operations
  setZoom: (zoom: number) => void;
  setPan: (pan: Point) => void;
  resetView: () => void;
  fitToScreen: () => void;
  
  // Grid and snapping
  toggleGrid: () => void;
  toggleSnapToGrid: () => void;
  setGridSize: (size: number) => void;
  
  // History operations
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
  clearHistory: () => void;
  
  // Export/Import
  exportCanvas: (options: ExportOptions) => Promise<Blob>;
  importImage: (file: File) => Promise<string>;
  
  // Events
  addEventListener: (callback: (event: FigmaCanvasEvent) => void) => () => void;
  removeEventListener: (callback: (event: FigmaCanvasEvent) => void) => void;

  // Offline Storage
  saveToIndexedDB: (name?: string) => Promise<string>;
  loadFromIndexedDB: (id: string) => Promise<void>;
  autoSave: () => Promise<void>;
  loadAutoSave: () => Promise<boolean>;
  clearAutoSave: () => Promise<void>;

  // Utility
  getObjectById: (id: string) => DrawingObject | undefined;
  getLayerById: (id: string) => Layer | undefined;
  getSelectedObjects: () => DrawingObject[];
  getBoundingBox: (objectIds: string[]) => Transform | null;
}

// Alias generateUUID to generateId for compatibility
const generateId = generateUUID;

const createDefaultLayer = (): Layer => ({
  id: generateId(),
  name: 'Layer 1',
  visible: true,
  locked: false,
  opacity: 1,
  blendMode: 'normal',
  order: 0,
  objects: [],
});

const createInitialState = (): CanvasState => {
  const defaultLayer = createDefaultLayer();
  return {
    objects: {},
    layers: { [defaultLayer.id]: defaultLayer },
    selectedObjectIds: [],
    activeLayerId: defaultLayer.id,
    activeTool: 'select',
    zoom: 1,
    pan: { x: 0, y: 0 },
    gridVisible: true,
    snapToGrid: true,
    gridSize: 20,
    canvasSize: { width: 1920, height: 1080 },
    backgroundColor: '#ffffff',
  };
};

export const useFigmaCanvasStore = create<FigmaCanvasStore>()(
  persist(
    (set, get) => {
      const eventListeners: ((event: FigmaCanvasEvent) => void)[] = [];
      
      const emitEvent = (event: FigmaCanvasEvent) => {
        eventListeners.forEach(callback => callback(event));
      };
      
      const saveToHistory = () => {
        const state = get();
        const currentState: CanvasState = {
          objects: state.objects,
          layers: state.layers,
          selectedObjectIds: state.selectedObjectIds,
          activeLayerId: state.activeLayerId,
          activeTool: state.activeTool,
          zoom: state.zoom,
          pan: state.pan,
          gridVisible: state.gridVisible,
          snapToGrid: state.snapToGrid,
          gridSize: state.gridSize,
          canvasSize: state.canvasSize,
          backgroundColor: state.backgroundColor,
        };
        
        const newHistory = state.history.slice(0, state.historyIndex + 1);
        newHistory.push(currentState);
        
        if (newHistory.length > state.maxHistorySize) {
          newHistory.shift();
        }
        
        set({
          history: newHistory,
          historyIndex: newHistory.length - 1,
        });
      };
      
      return {
        ...createInitialState(),
        toolSettings: {},
        history: [],
        historyIndex: -1,
        maxHistorySize: 50,
        
        setActiveTool: (tool) => set({ activeTool: tool }),
        setToolSettings: (settings) => set((state) => ({
          toolSettings: { ...state.toolSettings, ...settings }
        })),
        
        addObject: (objectData) => {
          const id = generateId();
          const now = new Date();
          const object: DrawingObject = {
            ...objectData,
            id,
            createdAt: now,
            updatedAt: now,
          } as DrawingObject;
          
          set((state) => {
            const activeLayer = state.layers[state.activeLayerId];
            return {
              objects: { ...state.objects, [id]: object },
              layers: {
                ...state.layers,
                [state.activeLayerId]: {
                  ...activeLayer,
                  objects: [...activeLayer.objects, id],
                },
              },
            };
          });
          
          emitEvent({
            type: 'object-created',
            timestamp: now,
            data: { objectId: id, object },
          });
          
          saveToHistory();
          return id;
        },
        
        updateObject: (id, updates) => {
          set((state) => {
            const object = state.objects[id];
            if (!object) return state;
            const updatedObject = {
              ...object,
              ...updates,
              updatedAt: new Date(),
            } as typeof object;
            return {
              ...state,
              objects: { ...state.objects, [id]: updatedObject },
            };
          });
          
          emitEvent({
            type: 'object-updated',
            timestamp: new Date(),
            data: { objectId: id, changes: updates },
          });
          
          saveToHistory();
        },
        
        deleteObject: (id) => {
          set((state) => {
            const { [id]: deleted, ...remainingObjects } = state.objects;
            const updatedLayers = { ...state.layers };
            
            // Remove object from all layers
            Object.keys(updatedLayers).forEach(layerId => {
              const layer = updatedLayers[layerId];
              updatedLayers[layerId] = {
                ...layer,
                objects: layer.objects.filter(objId => objId !== id),
              };
            });
            
            return {
              objects: remainingObjects,
              layers: updatedLayers,
              selectedObjectIds: state.selectedObjectIds.filter(objId => objId !== id),
            };
          });
          
          emitEvent({
            type: 'object-deleted',
            timestamp: new Date(),
            data: { objectId: id },
          });
          
          saveToHistory();
        },
        
        duplicateObject: (id) => {
          const state = get();
          const object = state.objects[id];
          if (!object) return '';
          
          const duplicatedObject = {
            ...object,
            transform: {
              ...object.transform,
              x: object.transform.x + 20,
              y: object.transform.y + 20,
            },
          };
          
          return get().addObject(duplicatedObject);
        },
        
        selectObjects: (ids) => {
          set({ selectedObjectIds: ids });
          emitEvent({
            type: 'selection-changed',
            timestamp: new Date(),
            data: { selectedObjectIds: ids },
          });
        },
        
        addToSelection: (id) => {
          set((state) => {
            if (state.selectedObjectIds.includes(id)) return state;
            const newSelection = [...state.selectedObjectIds, id];
            emitEvent({
              type: 'selection-changed',
              timestamp: new Date(),
              data: { selectedObjectIds: newSelection },
            });
            return { selectedObjectIds: newSelection };
          });
        },
        
        removeFromSelection: (id) => {
          set((state) => {
            const newSelection = state.selectedObjectIds.filter(objId => objId !== id);
            emitEvent({
              type: 'selection-changed',
              timestamp: new Date(),
              data: { selectedObjectIds: newSelection },
            });
            return { selectedObjectIds: newSelection };
          });
        },
        
        clearSelection: () => {
          set({ selectedObjectIds: [] });
          emitEvent({
            type: 'selection-changed',
            timestamp: new Date(),
            data: { selectedObjectIds: [] },
          });
        },
        
        selectAll: () => {
          const state = get();
          const allObjectIds = Object.keys(state.objects);
          get().selectObjects(allObjectIds);
        },
        
        // Additional methods will be implemented in the next part...
        addLayer: (name) => {
          const id = generateId();
          const layer: Layer = {
            id,
            name,
            visible: true,
            locked: false,
            opacity: 1,
            blendMode: 'normal',
            order: Object.keys(get().layers).length,
            objects: [],
          };
          
          set((state) => ({
            layers: { ...state.layers, [id]: layer },
          }));
          
          return id;
        },
        
        updateLayer: (id, updates) => {
          set((state) => {
            const layer = state.layers[id];
            if (!layer) return state;
            
            return {
              layers: {
                ...state.layers,
                [id]: { ...layer, ...updates },
              },
            };
          });
        },
        
        deleteLayer: (id) => {
          set((state) => {
            if (Object.keys(state.layers).length <= 1) return state; // Keep at least one layer
            
            const { [id]: deleted, ...remainingLayers } = state.layers;
            const layerIds = Object.keys(remainingLayers);
            const newActiveLayerId = state.activeLayerId === id ? layerIds[0] : state.activeLayerId;
            
            return {
              layers: remainingLayers,
              activeLayerId: newActiveLayerId,
            };
          });
        },
        
        setActiveLayer: (id) => set({ activeLayerId: id }),
        
        reorderLayers: (layerIds) => {
          set((state) => {
            const updatedLayers = { ...state.layers };
            layerIds.forEach((layerId, index) => {
              if (updatedLayers[layerId]) {
                updatedLayers[layerId] = {
                  ...updatedLayers[layerId],
                  order: index,
                };
              }
            });
            return { layers: updatedLayers };
          });
        },
        
        // Transform operations
        moveObjects: (ids, delta) => {
          set((state) => {
            const updatedObjects = { ...state.objects };
            ids.forEach(id => {
              const object = updatedObjects[id];
              if (object) {
                updatedObjects[id] = {
                  ...object,
                  transform: {
                    ...object.transform,
                    x: object.transform.x + delta.x,
                    y: object.transform.y + delta.y,
                  },
                  updatedAt: new Date(),
                };
              }
            });
            return { objects: updatedObjects };
          });
          saveToHistory();
        },
        
        resizeObjects: (ids, transform) => {
          set((state) => {
            const updatedObjects = { ...state.objects };
            ids.forEach(id => {
              const object = updatedObjects[id];
              if (object) {
                updatedObjects[id] = {
                  ...object,
                  transform: {
                    ...object.transform,
                    ...transform,
                  },
                  updatedAt: new Date(),
                };
              }
            });
            return { objects: updatedObjects };
          });
          saveToHistory();
        },
        
        rotateObjects: (ids, angle) => {
          set((state) => {
            const updatedObjects = { ...state.objects };
            ids.forEach(id => {
              const object = updatedObjects[id];
              if (object) {
                updatedObjects[id] = {
                  ...object,
                  transform: {
                    ...object.transform,
                    rotation: object.transform.rotation + angle,
                  },
                  updatedAt: new Date(),
                };
              }
            });
            return { objects: updatedObjects };
          });
          saveToHistory();
        },
        
        // Canvas operations
        setZoom: (zoom) => set({ zoom: Math.max(0.1, Math.min(10, zoom)) }),
        setPan: (pan) => set({ pan }),
        resetView: () => set({ zoom: 1, pan: { x: 0, y: 0 } }),
        fitToScreen: () => {
          // Implementation would calculate optimal zoom and pan to fit all objects
          // This is a placeholder
          set({ zoom: 1, pan: { x: 0, y: 0 } });
        },
        
        // Grid and snapping
        toggleGrid: () => set((state) => ({ gridVisible: !state.gridVisible })),
        toggleSnapToGrid: () => set((state) => ({ snapToGrid: !state.snapToGrid })),
        setGridSize: (size) => set({ gridSize: Math.max(5, Math.min(100, size)) }),
        
        // History operations
        undo: () => {
          const state = get();
          if (state.historyIndex > 0) {
            const previousState = state.history[state.historyIndex - 1];
            set({
              ...previousState,
              history: state.history,
              historyIndex: state.historyIndex - 1,
              toolSettings: state.toolSettings,
              maxHistorySize: state.maxHistorySize,
            });
          }
        },
        
        redo: () => {
          const state = get();
          if (state.historyIndex < state.history.length - 1) {
            const nextState = state.history[state.historyIndex + 1];
            set({
              ...nextState,
              history: state.history,
              historyIndex: state.historyIndex + 1,
              toolSettings: state.toolSettings,
              maxHistorySize: state.maxHistorySize,
            });
          }
        },
        
        saveToHistory,
        
        clearHistory: () => set({ history: [], historyIndex: -1 }),
        
        // Export/Import (placeholder implementations)
        exportCanvas: async (options) => {
          // This would be implemented with actual canvas export logic
          return new Blob([''], { type: 'image/png' });
        },
        
        importImage: async (file) => {
          // This would be implemented with actual image import logic
          return '';
        },
        
        // Events
        addEventListener: (callback) => {
          eventListeners.push(callback);
          return () => {
            const index = eventListeners.indexOf(callback);
            if (index > -1) {
              eventListeners.splice(index, 1);
            }
          };
        },

        removeEventListener: (callback) => {
          const index = eventListeners.indexOf(callback);
          if (index > -1) {
            eventListeners.splice(index, 1);
          }
        },

        // Offline Storage
        saveToIndexedDB: async (name) => {
          const state = get();
          const canvasState: CanvasState = {
            objects: state.objects,
            layers: state.layers,
            selectedObjectIds: state.selectedObjectIds,
            activeLayerId: state.activeLayerId,
            activeTool: state.activeTool,
            zoom: state.zoom,
            pan: state.pan,
            gridVisible: state.gridVisible,
            snapToGrid: state.snapToGrid,
            gridSize: state.gridSize,
            canvasSize: state.canvasSize,
            backgroundColor: state.backgroundColor,
          };

          try {
            await indexedDBManager.init();
            const id = await indexedDBManager.saveCanvas({
              name: name || `Canvas ${new Date().toLocaleString()}`,
              canvasState,
              version: '1.0',
            });
            return id;
          } catch (error) {
            console.error('Failed to save to IndexedDB:', error);
            throw error;
          }
        },

        loadFromIndexedDB: async (id) => {
          try {
            await indexedDBManager.init();
            const storedCanvas = await indexedDBManager.getCanvas(id);

            if (storedCanvas) {
              const { canvasState } = storedCanvas;
              set({
                objects: canvasState.objects,
                layers: canvasState.layers,
                selectedObjectIds: canvasState.selectedObjectIds,
                activeLayerId: canvasState.activeLayerId,
                activeTool: canvasState.activeTool,
                zoom: canvasState.zoom,
                pan: canvasState.pan,
                gridVisible: canvasState.gridVisible,
                snapToGrid: canvasState.snapToGrid,
                gridSize: canvasState.gridSize,
                canvasSize: canvasState.canvasSize,
                backgroundColor: canvasState.backgroundColor,
              });
            }
          } catch (error) {
            console.error('Failed to load from IndexedDB:', error);
            throw error;
          }
        },

        autoSave: async () => {
          const state = get();
          const canvasState: CanvasState = {
            objects: state.objects,
            layers: state.layers,
            selectedObjectIds: state.selectedObjectIds,
            activeLayerId: state.activeLayerId,
            activeTool: state.activeTool,
            zoom: state.zoom,
            pan: state.pan,
            gridVisible: state.gridVisible,
            snapToGrid: state.snapToGrid,
            gridSize: state.gridSize,
            canvasSize: state.canvasSize,
            backgroundColor: state.backgroundColor,
          };

          try {
            await indexedDBManager.init();
            await indexedDBManager.autoSave(canvasState);
          } catch (error) {
            console.error('Auto-save failed:', error);
          }
        },

        loadAutoSave: async () => {
          try {
            await indexedDBManager.init();
            const autoSaveData = await indexedDBManager.getAutoSave();

            if (autoSaveData) {
              const { canvasState } = autoSaveData;
              set({
                objects: canvasState.objects,
                layers: canvasState.layers,
                selectedObjectIds: canvasState.selectedObjectIds,
                activeLayerId: canvasState.activeLayerId,
                activeTool: canvasState.activeTool,
                zoom: canvasState.zoom,
                pan: canvasState.pan,
                gridVisible: canvasState.gridVisible,
                snapToGrid: canvasState.snapToGrid,
                gridSize: canvasState.gridSize,
                canvasSize: canvasState.canvasSize,
                backgroundColor: canvasState.backgroundColor,
              });
              return true;
            }
            return false;
          } catch (error) {
            console.error('Failed to load auto-save:', error);
            return false;
          }
        },

        clearAutoSave: async () => {
          try {
            await indexedDBManager.init();
            await indexedDBManager.clearAutoSave();
          } catch (error) {
            console.error('Failed to clear auto-save:', error);
          }
        },
        
        // Utility methods
        getObjectById: (id) => get().objects[id],
        getLayerById: (id) => get().layers[id],
        getSelectedObjects: () => {
          const state = get();
          return state.selectedObjectIds.map(id => state.objects[id]).filter(Boolean);
        },
        getBoundingBox: (objectIds) => {
          const state = get();
          const objects = objectIds.map(id => state.objects[id]).filter(Boolean);
          
          if (objects.length === 0) return null;
          
          let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
          
          objects.forEach(obj => {
            const { x, y, width, height } = obj.transform;
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x + width);
            maxY = Math.max(maxY, y + height);
          });
          
          return {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
          };
        },
      };
    },
    {
      name: 'figma-canvas-store',
      partialize: (state) => ({
        objects: state.objects,
        layers: state.layers,
        activeLayerId: state.activeLayerId,
        zoom: state.zoom,
        pan: state.pan,
        gridVisible: state.gridVisible,
        snapToGrid: state.snapToGrid,
        gridSize: state.gridSize,
        canvasSize: state.canvasSize,
        backgroundColor: state.backgroundColor,
        toolSettings: state.toolSettings,
      }),
    }
  )
);
