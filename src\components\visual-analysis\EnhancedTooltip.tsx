import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto';
  delay?: number;
  disabled?: boolean;
  className?: string;
  maxWidth?: number;
  showArrow?: boolean;
  interactive?: boolean;
}

export const EnhancedTooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'auto',
  delay = 500,
  disabled = false,
  className = '',
  maxWidth = 300,
  showArrow = true,
  interactive = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [actualPosition, setActualPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let x = 0;
    let y = 0;
    let finalPosition = position;

    // Auto-position logic
    if (position === 'auto') {
      const spaceTop = triggerRect.top;
      const spaceBottom = viewport.height - triggerRect.bottom;
      const spaceLeft = triggerRect.left;
      const spaceRight = viewport.width - triggerRect.right;

      if (spaceTop >= tooltipRect.height && spaceTop >= spaceBottom) {
        finalPosition = 'top';
      } else if (spaceBottom >= tooltipRect.height) {
        finalPosition = 'bottom';
      } else if (spaceRight >= tooltipRect.width) {
        finalPosition = 'right';
      } else {
        finalPosition = 'left';
      }
    }
    // Only assign allowed values
    setActualPosition(finalPosition as 'top' | 'bottom' | 'left' | 'right');

    // Calculate position based on final position
    switch (finalPosition) {
      case 'top':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        y = triggerRect.top - tooltipRect.height - 8;
        break;
      case 'bottom':
        x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        y = triggerRect.bottom + 8;
        break;
      case 'left':
        x = triggerRect.left - tooltipRect.width - 8;
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        break;
      case 'right':
        x = triggerRect.right + 8;
        y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        break;
    }

    // Ensure tooltip stays within viewport
    x = Math.max(8, Math.min(x, viewport.width - tooltipRect.width - 8));
    y = Math.max(8, Math.min(y, viewport.height - tooltipRect.height - 8));

    setTooltipPosition({ x, y });
  };

  const showTooltip = () => {
    if (disabled) return;
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  useEffect(() => {
    if (isVisible) {
      calculatePosition();
      
      const handleResize = () => calculatePosition();
      const handleScroll = () => calculatePosition();
      
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll, true);
      
      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll, true);
      };
    }
  }, [isVisible]);

  const getArrowClasses = () => {
    const baseClasses = 'absolute w-2 h-2 bg-gray-900 transform rotate-45';
    
    switch (actualPosition) {
      case 'top':
        return `${baseClasses} -bottom-1 left-1/2 -translate-x-1/2`;
      case 'bottom':
        return `${baseClasses} -top-1 left-1/2 -translate-x-1/2`;
      case 'left':
        return `${baseClasses} -right-1 top-1/2 -translate-y-1/2`;
      case 'right':
        return `${baseClasses} -left-1 top-1/2 -translate-y-1/2`;
      default:
        return baseClasses;
    }
  };

  const tooltip = isVisible && (
    <div
      ref={tooltipRef}
      className={`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg border border-gray-700 ${className}`}
      style={{
        left: tooltipPosition.x,
        top: tooltipPosition.y,
        maxWidth: maxWidth,
        pointerEvents: interactive ? 'auto' : 'none'
      }}
      onMouseEnter={interactive ? showTooltip : undefined}
      onMouseLeave={interactive ? hideTooltip : undefined}
    >
      {content}
      {showArrow && <div className={getArrowClasses()} />}
    </div>
  );

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="inline-block"
      >
        {children}
      </div>
      {typeof document !== 'undefined' && createPortal(tooltip, document.body)}
    </>
  );
};

/**
 * Specialized tooltip for help text
 */
export const HelpTooltip: React.FC<{
  content: string;
  children: React.ReactNode;
}> = ({ content, children }) => (
  <EnhancedTooltip
    content={
      <div className="max-w-xs">
        <div className="font-medium text-blue-200 mb-1">Help</div>
        <div className="text-gray-300">{content}</div>
      </div>
    }
    position="auto"
    delay={300}
    showArrow={true}
    interactive={true}
  >
    {children}
  </EnhancedTooltip>
);

/**
 * Specialized tooltip for keyboard shortcuts
 */
export const ShortcutTooltip: React.FC<{
  shortcut: string;
  description: string;
  children: React.ReactNode;
}> = ({ shortcut, description, children }) => (
  <EnhancedTooltip
    content={
      <div className="flex items-center gap-2">
        <kbd className="px-2 py-1 text-xs bg-gray-700 rounded border border-gray-600">
          {shortcut}
        </kbd>
        <span className="text-gray-300">{description}</span>
      </div>
    }
    position="auto"
    delay={500}
  >
    {children}
  </EnhancedTooltip>
);

/**
 * Specialized tooltip for status information
 */
export const StatusTooltip: React.FC<{
  status: 'success' | 'warning' | 'error' | 'info';
  title: string;
  description: string;
  children: React.ReactNode;
}> = ({ status, title, description, children }) => {
  const statusColors = {
    success: 'text-green-200 border-green-500',
    warning: 'text-yellow-200 border-yellow-500',
    error: 'text-red-200 border-red-500',
    info: 'text-blue-200 border-blue-500'
  };

  const statusIcons = {
    success: '✓',
    warning: '⚠',
    error: '✗',
    info: 'ℹ'
  };

  return (
    <EnhancedTooltip
      content={
        <div className={`border-l-2 pl-3 ${statusColors[status]}`}>
          <div className="flex items-center gap-2 font-medium mb-1">
            <span>{statusIcons[status]}</span>
            {title}
          </div>
          <div className="text-gray-300 text-xs">{description}</div>
        </div>
      }
      position="auto"
      delay={200}
      showArrow={true}
    >
      {children}
    </EnhancedTooltip>
  );
};

/**
 * Hook for managing tooltip state
 */
export const useTooltip = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [content, setContent] = useState<React.ReactNode>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const showTooltip = (content: React.ReactNode, x: number, y: number) => {
    setContent(content);
    setPosition({ x, y });
    setIsVisible(true);
  };

  const hideTooltip = () => {
    setIsVisible(false);
  };

  return {
    isVisible,
    content,
    position,
    showTooltip,
    hideTooltip
  };
};
