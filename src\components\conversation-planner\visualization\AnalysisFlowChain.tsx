/**
 * Analysis Flow Chain
 * 
 * A dynamic visualization component that shows the flow of analysis from
 * question input through AI processing to final results, with interactive
 * nodes and animated connections.
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { AnalysisResult } from '@/types/conversation';
import { 
  MessageSquare, 
  Brain, 
  Lightbulb,
  ArrowRight,
  Zap,
  Target,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Settings,
  Sparkles
} from 'lucide-react';

interface AnalysisFlowChainProps {
  results: AnalysisResult[];
  selectedResultId?: string;
  onResultSelect?: (result: AnalysisResult) => void;
  onFollowUpQuestion?: (question: string) => void;
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  compactMode?: boolean;
}

interface FlowNode {
  id: string;
  type: 'input' | 'process' | 'output';
  title: string;
  content: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  position: { x: number; y: number };
  data?: Record<string, unknown>;
}

interface FlowConnection {
  id: string;
  from: string;
  to: string;
  animated: boolean;
  color: string;
}

export const AnalysisFlowChain: React.FC<AnalysisFlowChainProps> = ({
  results,
  selectedResultId,
  onResultSelect,
  onFollowUpQuestion,
  className,
  autoPlay = true,
  showControls = true,
  compactMode = false
}) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentStep, setCurrentStep] = useState(0);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [animationProgress, setAnimationProgress] = useState(0);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();

  // Create flow nodes from analysis results
  const createFlowNodes = (result: AnalysisResult): FlowNode[] => {
    return [
      {
        id: `input-${result.id}`,
        type: 'input',
        title: 'Question Input',
        content: result.question,
        icon: MessageSquare,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        position: { x: 50, y: 150 },
        data: { analysisType: result.analysisType, style: result.style }
      },
      {
        id: `process-${result.id}`,
        type: 'process',
        title: 'AI Processing',
        content: `${result.model} Analysis`,
        icon: Brain,
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        position: { x: 300, y: 150 },
        data: { model: result.model, type: result.analysisType }
      },
      {
        id: `output-${result.id}`,
        type: 'output',
        title: 'Analysis Result',
        content: result.analysis.substring(0, 100) + '...',
        icon: Lightbulb,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        position: { x: 550, y: 150 },
        data: { rating: result.rating, followUps: result.followUpQuestions?.length || 0 }
      }
    ];
  };

  // Create connections between nodes
  const createFlowConnections = (result: AnalysisResult): FlowConnection[] => {
    return [
      {
        id: `conn-1-${result.id}`,
        from: `input-${result.id}`,
        to: `process-${result.id}`,
        animated: true,
        color: 'stroke-blue-400'
      },
      {
        id: `conn-2-${result.id}`,
        from: `process-${result.id}`,
        to: `output-${result.id}`,
        animated: true,
        color: 'stroke-purple-400'
      }
    ];
  };

  const selectedResult = results.find(r => r.id === selectedResultId);
  const flowNodes = selectedResult ? createFlowNodes(selectedResult) : [];
  const flowConnections = selectedResult ? createFlowConnections(selectedResult) : [];

  // Animation control
  useEffect(() => {
    if (isPlaying && selectedResult) {
      const animate = () => {
        setAnimationProgress(prev => {
          const next = prev + 0.02;
          return next > 1 ? 0 : next;
        });
        animationRef.current = requestAnimationFrame(animate);
      };
      animationRef.current = requestAnimationFrame(animate);
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isPlaying, selectedResult]);

  const handleNodeClick = (node: FlowNode) => {
    setSelectedNode(node.id);
    if (node.type === 'input' && selectedResult) {
      onResultSelect?.(selectedResult);
    }
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleReset = () => {
    setAnimationProgress(0);
    setCurrentStep(0);
  };

  const renderNode = (node: FlowNode) => {
    const Icon = node.icon;
    const isSelected = selectedNode === node.id;
    const isHovered = hoveredNode === node.id;

    return (
      <motion.div
        key={node.id}
        className="absolute cursor-pointer"
        style={{
          left: node.position.x,
          top: node.position.y,
          transform: 'translate(-50%, -50%)'
        }}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ 
          opacity: 1, 
          scale: isSelected ? 1.05 : isHovered ? 1.02 : 1,
        }}
        transition={{ duration: 0.3 }}
        onMouseEnter={() => setHoveredNode(node.id)}
        onMouseLeave={() => setHoveredNode(null)}
        onClick={() => handleNodeClick(node)}
      >
        <Card className={cn(
          "transition-all duration-200 shadow-md hover:shadow-lg",
          isSelected && "ring-2 ring-blue-400 ring-offset-2",
          isHovered && "shadow-xl",
          compactMode ? "w-40" : "w-48"
        )}>
          <CardContent className={cn(
            "p-4",
            compactMode && "p-3"
          )}>
            <div className="flex items-center gap-3 mb-2">
              <div className={cn(
                "p-2 rounded-lg",
                node.bgColor
              )}>
                <Icon className={cn("h-4 w-4", node.color)} />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className={cn(
                  "font-semibold truncate",
                  compactMode ? "text-sm" : "text-base"
                )}>
                  {node.title}
                </h4>
              </div>
            </div>
            
            <p className={cn(
              "text-gray-600 line-clamp-2",
              compactMode ? "text-xs" : "text-sm"
            )}>
              {node.content}
            </p>

            {/* Node-specific data */}
            <div className="mt-2 flex flex-wrap gap-1">
              {node.type === 'input' && node.data && (
                <>
                  <Badge variant="secondary" className="text-xs">
                    {typeof node.data.analysisType === 'string' ? node.data.analysisType : ''}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {typeof node.data.style === 'string' ? node.data.style : ''}
                  </Badge>
                </>
              )}
              
              {node.type === 'process' && node.data && (
                <Badge variant="secondary" className="text-xs">
                  {typeof node.data.model === 'string' ? node.data.model : ''}
                </Badge>
              )}
              
              {node.type === 'output' && node.data && (
                <div className="flex items-center gap-1">
                  {typeof node.data.rating === 'number' && (
                    <Badge variant="outline" className="text-xs">
                      ★ {node.data.rating}/10
                    </Badge>
                  )}
                  {typeof node.data.followUps === 'number' && node.data.followUps > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {node.data.followUps} follow-ups
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  const renderConnection = (connection: FlowConnection) => {
    const fromNode = flowNodes.find(n => n.id === connection.from);
    const toNode = flowNodes.find(n => n.id === connection.to);
    
    if (!fromNode || !toNode) return null;

    const startX = fromNode.position.x + (compactMode ? 80 : 96);
    const startY = fromNode.position.y;
    const endX = toNode.position.x - (compactMode ? 80 : 96);
    const endY = toNode.position.y;

    const pathLength = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
    const animatedLength = pathLength * animationProgress;

    return (
      <g key={connection.id}>
        {/* Base line */}
        <line
          x1={startX}
          y1={startY}
          x2={endX}
          y2={endY}
          stroke="#e5e7eb"
          strokeWidth="2"
          strokeDasharray="5,5"
        />
        
        {/* Animated line */}
        {connection.animated && isPlaying && (
          <motion.line
            x1={startX}
            y1={startY}
            x2={startX + (endX - startX) * animationProgress}
            y2={startY + (endY - startY) * animationProgress}
            className={connection.color}
            strokeWidth="3"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: animationProgress }}
            transition={{ duration: 0.1 }}
          />
        )}
        
        {/* Arrow */}
        <motion.polygon
          points={`${endX-8},${endY-4} ${endX},${endY} ${endX-8},${endY+4}`}
          className={cn(
            "fill-current",
            connection.color.replace('stroke-', 'text-')
          )}
          initial={{ opacity: 0 }}
          animate={{ opacity: animationProgress > 0.8 ? 1 : 0 }}
        />
        
        {/* Flow particles */}
        {connection.animated && isPlaying && (
          <motion.circle
            cx={startX + (endX - startX) * animationProgress}
            cy={startY + (endY - startY) * animationProgress}
            r="3"
            className={cn(
              "fill-current",
              connection.color.replace('stroke-', 'text-')
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 1, repeat: Infinity }}
          />
        )}
      </g>
    );
  };

  if (!selectedResult) {
    return (
      <div className={cn(
        "flex items-center justify-center h-64 text-gray-500",
        className
      )}>
        <div className="text-center">
          <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Select an analysis to view the flow chain</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border",
        compactMode ? "h-80" : "h-96",
        className
      )}
    >
      {/* Controls */}
      {showControls && (
        <div className="absolute top-4 right-4 flex items-center gap-2 z-10">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePlayPause}
            className="h-8 w-8 p-0"
          >
            {isPlaying ? (
              <Pause className="h-3 w-3" />
            ) : (
              <Play className="h-3 w-3" />
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="h-8 w-8 p-0"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      )}

      {/* Flow Visualization */}
      <svg 
        className="absolute inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1 }}
      >
        {flowConnections.map(renderConnection)}
      </svg>

      {/* Nodes */}
      <div className="relative w-full h-full" style={{ zIndex: 2 }}>
        <AnimatePresence>
          {flowNodes.map(renderNode)}
        </AnimatePresence>
      </div>

      {/* Progress indicator */}
      {isPlaying && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-white/80 backdrop-blur-sm rounded-full p-2">
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-purple-500" />
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 h-2 rounded-full"
                  style={{ width: `${animationProgress * 100}%` }}
                />
              </div>
              <span className="text-xs text-gray-600 min-w-[3rem]">
                {Math.round(animationProgress * 100)}%
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisFlowChain;
