/**
 * Vitest Configuration
 * 
 * Configuration for unit and integration tests using Vitest,
 * including React Testing Library setup and coverage reporting.
 */

import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Setup files
    setupFiles: [
      './src/__tests__/setup/testSetup.ts',
      path.resolve(__dirname, 'src/__mocks__/three.ts'),
    ],
    
    // Global test configuration
    globals: true,
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'src/__tests__/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/dist/**',
        '**/build/**',
        '**/.next/**',
        '**/coverage/**',
        '**/*.test.*',
        '**/*.spec.*',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        // Specific thresholds for critical components
        './src/components/visual-analysis/': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
        './src/services/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
      },
    },
    
    // Test file patterns
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
    ],
    exclude: [
      'node_modules/',
      'dist/',
      '.idea/',
      '.git/',
      '.cache/',
      'src/__tests__/e2e/',
    ],
    
    // Test timeout
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Parallel execution
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1,
      },
    },
    
    // Reporter configuration
    reporters: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/results.html',
    },
    
    // Watch mode configuration
    watch: false,
    
    // Retry configuration
    retry: 2,
    
    // Mock configuration
    deps: {
      inline: [
        '@testing-library/react',
        '@testing-library/jest-dom',
      ],
    },
    
    // Environment variables for tests
    env: {
      NODE_ENV: 'test',
      VITE_APP_VERSION: '1.0.0-test',
      VITE_API_BASE_URL: 'http://localhost:3001',
    },
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/lib': path.resolve(__dirname, './src/lib'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      'three': path.resolve(__dirname, 'src/__mocks__/three.ts'),
      'three/examples/jsm/controls/OrbitControls': path.resolve(__dirname, 'src/__mocks__/three_examples_jsm_controls_OrbitControls.ts'),
    },
  },
  
  // Define configuration for different test types
  define: {
    __TEST__: true,
    __DEV__: false,
    __PROD__: false,
  },
});
