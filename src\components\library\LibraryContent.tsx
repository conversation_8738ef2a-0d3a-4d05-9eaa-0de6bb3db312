import React from "react";
import { SavedAnalysis, SavedFolder } from "@/types/conversation";
import { LibraryHeader } from "./LibraryHeader";
import { LibrarySearch } from "./LibrarySearch";
import { FolderGrid } from "./FolderGrid";
import { AnalysisGrid } from "./AnalysisGrid";
import { EmptyLibraryState } from "./EmptyLibraryState";

interface LibraryContentProps {
  savedAnalyses: SavedAnalysis[];
  savedFolders: SavedFolder[];
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedFolder: string;
  setSelectedFolder: (folder: string) => void;
  showNewFolderDialog: boolean;
  setShowNewFolderDialog: (show: boolean) => void;
  newFolderName: string;
  setNewFolderName: (name: string) => void;
  filteredAnalyses: SavedAnalysis[];
  onCreateFolder: () => void;
  onDeleteAnalysis: (id: string) => void;
  onDeleteFolder: (id: string) => void;
  onMoveToFolder: (analysisId: string, folderId: string) => void;
  onViewAnalysis: (analysis: SavedAnalysis) => void;
}

export const LibraryContent: React.FC<LibraryContentProps> = ({
  savedAnalyses,
  savedFolders,
  searchQuery,
  setSearchQuery,
  selectedFolder,
  setSelectedFolder,
  showNewFolderDialog,
  setShowNewFolderDialog,
  newFolderName,
  setNewFolderName,
  filteredAnalyses,
  onCreateFolder,
  onDeleteAnalysis,
  onDeleteFolder,
  onMoveToFolder,
  onViewAnalysis,
}) => {
  // If no analyses and no folders, show empty state.
  if (savedAnalyses.length === 0 && savedFolders.length === 0 && !searchQuery) {
    return (
      <div className="space-y-6">
        <LibraryHeader
          showNewFolderDialog={showNewFolderDialog}
          setShowNewFolderDialog={setShowNewFolderDialog}
          newFolderName={newFolderName}
          setNewFolderName={setNewFolderName}
          onCreateFolder={onCreateFolder}
        />
        <EmptyLibraryState />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <LibraryHeader
        showNewFolderDialog={showNewFolderDialog}
        setShowNewFolderDialog={setShowNewFolderDialog}
        newFolderName={newFolderName}
        setNewFolderName={setNewFolderName}
        onCreateFolder={onCreateFolder}
      />

      <LibrarySearch
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedFolder={selectedFolder}
        setSelectedFolder={setSelectedFolder}
        savedFolders={savedFolders}
      />

      <FolderGrid
        savedFolders={savedFolders}
        savedAnalyses={savedAnalyses}
        onDeleteFolder={onDeleteFolder}
      />

      <AnalysisGrid
        filteredAnalyses={filteredAnalyses}
        savedFolders={savedFolders}
        onDeleteAnalysis={onDeleteAnalysis}
        onMoveToFolder={onMoveToFolder}
        onViewAnalysis={onViewAnalysis}
      />
    </div>
  );
};
