/**
 * Enhanced 3-Column View
 * 
 * A visually appealing and interactive 3-column layout for analysis results
 * that provides better organization and flow visualization than the standard view.
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { AnalysisResult } from '@/types/conversation';
import {
  Unified3ColumnLayout,
  createAnalysisLayout,
  useColumnLayout
} from '@/components/layout/Unified3ColumnLayout';
import { 
  ArrowRight, 
  Brain, 
  MessageSquare, 
  Lightbulb,
  Zap,
  Target,
  TrendingUp,
  Eye,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Network,
  Filter
} from 'lucide-react';

interface Enhanced3ColumnViewProps {
  results: AnalysisResult[];
  onResultSelect?: (result: AnalysisResult) => void;
  onFollowUpQuestion?: (question: string) => void;
  selectedResultId?: string;
  className?: string;
  showAnimations?: boolean;
  compactMode?: boolean;
}

interface ColumnConfig {
  id: 'input' | 'process' | 'output';
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgGradient: string;
  borderColor: string;
}

const columnConfigs: ColumnConfig[] = [
  {
    id: 'input',
    title: 'Input Analysis',
    subtitle: 'Questions & Context',
    icon: MessageSquare,
    color: 'text-blue-600',
    bgGradient: 'from-blue-50 to-blue-100',
    borderColor: 'border-blue-200'
  },
  {
    id: 'process',
    title: 'AI Processing',
    subtitle: 'Analysis & Reasoning',
    icon: Brain,
    color: 'text-purple-600',
    bgGradient: 'from-purple-50 to-purple-100',
    borderColor: 'border-purple-200'
  },
  {
    id: 'output',
    title: 'Results & Insights',
    subtitle: 'Answers & Follow-ups',
    icon: Lightbulb,
    color: 'text-green-600',
    bgGradient: 'from-green-50 to-green-100',
    borderColor: 'border-green-200'
  }
];

export const Enhanced3ColumnView: React.FC<Enhanced3ColumnViewProps> = ({
  results,
  onResultSelect,
  onFollowUpQuestion,
  selectedResultId,
  className,
  showAnimations = true,
  compactMode = false
}) => {
  const [hoveredResult, setHoveredResult] = useState<string | null>(null);
  const [expandedColumns, setExpandedColumns] = useState<Set<string>>(new Set(['input', 'process', 'output']));
  const [flowAnimation, setFlowAnimation] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<string | null>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);

  // Trigger flow animation when results change
  useEffect(() => {
    if (showAnimations && results.length > 0) {
      setFlowAnimation(true);
      const timer = setTimeout(() => setFlowAnimation(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [results, showAnimations]);

  const toggleColumnExpansion = (columnId: string) => {
    const newExpanded = new Set(expandedColumns);
    if (newExpanded.has(columnId)) {
      newExpanded.delete(columnId);
    } else {
      newExpanded.add(columnId);
    }
    setExpandedColumns(newExpanded);
  };

  const handleResultClick = (result: AnalysisResult) => {
    onResultSelect?.(result);
  };

  const handleFollowUpClick = (question: string) => {
    onFollowUpQuestion?.(question);
  };

  const renderColumnHeader = (config: ColumnConfig) => {
    const Icon = config.icon;
    const isExpanded = expandedColumns.has(config.id);
    
    return (
      <CardHeader 
        className={cn(
          "pb-3 cursor-pointer transition-all duration-200",
          selectedColumn === config.id && "bg-gradient-to-r " + config.bgGradient
        )}
        onClick={() => {
          toggleColumnExpansion(config.id);
          setSelectedColumn(selectedColumn === config.id ? null : config.id);
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-lg bg-gradient-to-br shadow-sm",
              config.bgGradient
            )}>
              <Icon className={cn("h-5 w-5", config.color)} />
            </div>
            <div>
              <h3 className={cn(
                "font-semibold",
                compactMode ? "text-sm" : "text-base",
                config.color
              )}>
                {config.title}
              </h3>
              <p className={cn(
                "text-muted-foreground",
                compactMode ? "text-xs" : "text-sm"
              )}>
                {config.subtitle}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {results.length}
            </Badge>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
        </div>
      </CardHeader>
    );
  };

  const renderInputColumn = (config: ColumnConfig) => {
    const isExpanded = expandedColumns.has(config.id);
    
    return (
      <Card className={cn(
        "h-full transition-all duration-300",
        config.borderColor,
        !isExpanded && "opacity-60"
      )}>
        {renderColumnHeader(config)}
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CardContent className="pt-0">
                <ScrollArea className={cn(
                  compactMode ? "h-[400px]" : "h-[500px]"
                )}>
                  <div className="space-y-3">
                    {results.map((result, index) => (
                      <motion.div
                        key={result.id}
                        initial={showAnimations ? { opacity: 0, x: -20 } : {}}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={cn(
                          "p-3 rounded-lg border cursor-pointer transition-all duration-200",
                          "hover:shadow-md hover:border-blue-300",
                          selectedResultId === result.id && "bg-blue-50 border-blue-300 shadow-sm",
                          hoveredResult === result.id && "shadow-lg"
                        )}
                        onMouseEnter={() => setHoveredResult(result.id)}
                        onMouseLeave={() => setHoveredResult(null)}
                        onClick={() => handleResultClick(result)}
                      >
                        <div className="flex items-start gap-2">
                          <MessageSquare className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className={cn(
                              "font-medium text-gray-900 line-clamp-2",
                              compactMode ? "text-sm" : "text-base"
                            )}>
                              {result.question}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {result.analysisType}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {result.style}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    );
  };

  const renderProcessColumn = (config: ColumnConfig) => {
    const isExpanded = expandedColumns.has(config.id);
    
    return (
      <Card className={cn(
        "h-full transition-all duration-300",
        config.borderColor,
        !isExpanded && "opacity-60"
      )}>
        {renderColumnHeader(config)}
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <CardContent className="pt-0">
                <ScrollArea className={cn(
                  compactMode ? "h-[400px]" : "h-[500px]"
                )}>
                  <div className="space-y-4">
                    {selectedResultId ? (
                      results
                        .filter(r => r.id === selectedResultId)
                        .map((result) => (
                          <motion.div
                            key={result.id}
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="space-y-4"
                          >
                            {/* AI Model Info */}
                            <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                              <Zap className="w-4 h-4 text-purple-600" />
                              <span className="text-sm font-medium text-purple-800">
                                AI Model: {result.model}
                              </span>
                              <Sparkles className="w-4 h-4 text-purple-500 ml-auto" />
                            </div>

                            {/* Processing Steps */}
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                                <Network className="w-4 h-4" />
                                Processing Pipeline
                              </div>
                              
                              <div className="space-y-2 pl-6">
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                  Question Analysis
                                </div>
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                  Context Processing
                                </div>
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                                  Response Generation
                                </div>
                              </div>
                            </div>

                            {/* Analysis Metadata */}
                            <div className="grid grid-cols-2 gap-2">
                              <div className="p-2 bg-gray-50 rounded text-center">
                                <div className="text-xs text-gray-500">Type</div>
                                <div className="text-sm font-medium">{result.analysisType}</div>
                              </div>
                              <div className="p-2 bg-gray-50 rounded text-center">
                                <div className="text-xs text-gray-500">Style</div>
                                <div className="text-sm font-medium">{result.style}</div>
                              </div>
                            </div>
                          </motion.div>
                        ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Brain className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Select an input to see processing details</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    );
  };

  const renderOutputColumn = (config: ColumnConfig) => {
    const isExpanded = expandedColumns.has(config.id);
    
    return (
      <Card className={cn(
        "h-full transition-all duration-300",
        config.borderColor,
        !isExpanded && "opacity-60"
      )}>
        {renderColumnHeader(config)}
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <CardContent className="pt-0">
                <ScrollArea className={cn(
                  compactMode ? "h-[400px]" : "h-[500px]"
                )}>
                  <div className="space-y-4">
                    {selectedResultId ? (
                      results
                        .filter(r => r.id === selectedResultId)
                        .map((result) => (
                          <motion.div
                            key={result.id}
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="space-y-4"
                          >
                            {/* Main Analysis */}
                            <div className="prose prose-sm max-w-none">
                              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                                <div className="flex items-center gap-2 mb-3">
                                  <Lightbulb className="w-4 h-4 text-green-600" />
                                  <span className="text-sm font-medium text-green-800">Analysis Result</span>
                                </div>
                                <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                                  {result.analysis}
                                </div>
                              </div>
                            </div>

                            {/* Follow-up Questions */}
                            {result.followUpQuestions && result.followUpQuestions.length > 0 && (
                              <div className="space-y-2">
                                <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                                  <Target className="w-4 h-4" />
                                  Follow-up Questions
                                </div>
                                <div className="space-y-2">
                                  {result.followUpQuestions.map((question, index) => (
                                    <Button
                                      key={index}
                                      variant="outline"
                                      size="sm"
                                      className="w-full justify-start text-left h-auto p-3"
                                      onClick={() => handleFollowUpClick(question)}
                                    >
                                      <div className="flex items-start gap-2">
                                        <ArrowRight className="w-3 h-3 mt-0.5 flex-shrink-0" />
                                        <span className="text-xs leading-relaxed">{question}</span>
                                      </div>
                                    </Button>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Rating */}
                            {result.rating && (
                              <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                                <TrendingUp className="w-4 h-4 text-gray-600" />
                                <span className="text-sm text-gray-600">
                                  Quality Score: {result.rating}/10
                                </span>
                              </div>
                            )}
                          </motion.div>
                        ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Lightbulb className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Select an input to see results</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    );
  };

  return (
    <div 
      ref={containerRef}
      className={cn(
        "w-full h-full",
        className
      )}
    >
      {/* Flow Animation Overlay */}
      <AnimatePresence>
        {flowAnimation && showAnimations && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-10"
          >
            {/* Animated flow lines */}
            <svg className="w-full h-full">
              <motion.path
                d="M 33% 50% Q 50% 30% 66% 50%"
                stroke="url(#flowGradient1)"
                strokeWidth="2"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1, ease: "easeInOut" }}
              />
              <motion.path
                d="M 66% 50% Q 83% 30% 100% 50%"
                stroke="url(#flowGradient2)"
                strokeWidth="2"
                fill="none"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{ duration: 1, delay: 0.5, ease: "easeInOut" }}
              />
              <defs>
                <linearGradient id="flowGradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" stopOpacity="0" />
                  <stop offset="50%" stopColor="#3b82f6" stopOpacity="1" />
                  <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
                </linearGradient>
                <linearGradient id="flowGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0" />
                  <stop offset="50%" stopColor="#8b5cf6" stopOpacity="1" />
                  <stop offset="100%" stopColor="#10b981" stopOpacity="0" />
                </linearGradient>
              </defs>
            </svg>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main 3-Column Layout */}
      <div className="grid grid-cols-3 gap-4 h-full">
        {renderInputColumn(columnConfigs[0])}
        {renderProcessColumn(columnConfigs[1])}
        {renderOutputColumn(columnConfigs[2])}
      </div>
    </div>
  );
};

export default Enhanced3ColumnView;
