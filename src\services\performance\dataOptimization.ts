import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation, NodeData, ConnectionData } from '@/components/visual-analysis/types';

/**
 * Data processing optimization utilities
 */
export class DataOptimization {
  private static readonly CHUNK_SIZE = 100;
  private static readonly DEBOUNCE_DELAY = 300;
  private static readonly CACHE_SIZE = 1000;

  /**
   * Debounced function creator
   */
  static debounce<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number = this.DEBOUNCE_DELAY
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  /**
   * Throttled function creator
   */
  static throttle<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func(...args);
      }
    };
  }

  /**
   * Process large datasets in chunks to avoid blocking the UI
   */
  static async processInChunks<T, R>(
    data: T[],
    processor: (chunk: T[]) => R[],
    chunkSize: number = this.CHUNK_SIZE,
    onProgress?: (progress: number) => void
  ): Promise<R[]> {
    const results: R[] = [];
    const totalChunks = Math.ceil(data.length / chunkSize);

    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const chunkResults = processor(chunk);
      results.push(...chunkResults);

      // Report progress
      if (onProgress) {
        const progress = Math.min(100, ((i / chunkSize + 1) / totalChunks) * 100);
        onProgress(progress);
      }

      // Yield control to the browser
      await new Promise(resolve => setTimeout(resolve, 0));
    }

    return results;
  }

  /**
   * Memoization cache for expensive computations
   */
  static createMemoizedFunction<T extends (...args: unknown[]) => unknown>(
    func: T,
    keyGenerator?: (...args: Parameters<T>) => string
  ): T & { cache: Map<string, ReturnType<T>>; clearCache: () => void } {
    const cache = new Map<string, ReturnType<T>>();
    
    const memoized = ((...args: Parameters<T>) => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
      
      if (cache.has(key)) {
        return cache.get(key)!;
      }
      
      const result = func(...args);
      
      // Implement LRU cache
      if (cache.size >= this.CACHE_SIZE) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }
      
      cache.set(key, result);
      return result;
    }) as T & { cache: Map<string, ReturnType<T>>; clearCache: () => void };

    memoized.cache = cache;
    memoized.clearCache = () => cache.clear();

    return memoized;
  }

  /**
   * Optimize analysis result processing
   */
  static optimizeAnalysisResults(
    results: AnalysisResult[]
  ): {
    processed: AnalysisResult[];
    indexed: Map<string, AnalysisResult>;
    byType: Map<string, AnalysisResult[]>;
    byDate: Map<string, AnalysisResult[]>;
  } {
    const indexed = new Map<string, AnalysisResult>();
    const byType = new Map<string, AnalysisResult[]>();
    const byDate = new Map<string, AnalysisResult[]>();

    // Single pass through data for multiple indexes
    results.forEach(result => {
      // Index by ID
      indexed.set(result.id, result);

      // Index by type
      if (!byType.has(result.analysisType)) {
        byType.set(result.analysisType, []);
      }
      byType.get(result.analysisType)!.push(result);

      // Index by date (day)
      const dateKey = new Date(result.timestamp).toDateString();
      if (!byDate.has(dateKey)) {
        byDate.set(dateKey, []);
      }
      byDate.get(dateKey)!.push(result);
    });

    return {
      processed: results,
      indexed,
      byType,
      byDate
    };
  }

  /**
   * Optimize cluster processing
   */
  static optimizeClusters(
    clusters: AnalysisCluster[],
    nodes: NodeData[]
  ): {
    clustersWithNodes: Map<string, { cluster: AnalysisCluster; nodes: NodeData[] }>;
    nodeToCluster: Map<string, string>;
  } {
    const clustersWithNodes = new Map<string, { cluster: AnalysisCluster; nodes: NodeData[] }>();
    const nodeToCluster = new Map<string, string>();
    const nodeMap = new Map(nodes.map(node => [node.id, node]));

    clusters.forEach(cluster => {
      const clusterNodes = cluster.nodeIds
        .map(nodeId => nodeMap.get(nodeId))
        .filter(Boolean) as NodeData[];

      clustersWithNodes.set(cluster.id, { cluster, nodes: clusterNodes });

      // Build reverse mapping
      cluster.nodeIds.forEach(nodeId => {
        nodeToCluster.set(nodeId, cluster.id);
      });
    });

    return { clustersWithNodes, nodeToCluster };
  }

  /**
   * Virtual scrolling for large lists
   */
  static createVirtualList<T>(
    items: T[],
    itemHeight: number,
    containerHeight: number
  ): {
    visibleItems: T[];
    startIndex: number;
    endIndex: number;
    totalHeight: number;
    offsetY: number;
  } {
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const totalHeight = items.length * itemHeight;
    
    return {
      visibleItems: items.slice(0, visibleCount),
      startIndex: 0,
      endIndex: Math.min(visibleCount, items.length),
      totalHeight,
      offsetY: 0
    };
  }

  /**
   * Update virtual list based on scroll position
   */
  static updateVirtualList<T>(
    items: T[],
    itemHeight: number,
    containerHeight: number,
    scrollTop: number
  ): {
    visibleItems: T[];
    startIndex: number;
    endIndex: number;
    totalHeight: number;
    offsetY: number;
  } {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const endIndex = Math.min(startIndex + visibleCount + 1, items.length);
    const visibleItems = items.slice(startIndex, endIndex);
    const offsetY = startIndex * itemHeight;
    const totalHeight = items.length * itemHeight;

    return {
      visibleItems,
      startIndex,
      endIndex,
      totalHeight,
      offsetY
    };
  }

  /**
   * Batch operations for better performance
   */
  static createBatchProcessor<T>(
    processor: (items: T[]) => void,
    batchSize: number = 50,
    delay: number = 16 // ~60fps
  ): {
    add: (item: T) => void;
    flush: () => void;
    clear: () => void;
  } {
    let batch: T[] = [];
    let timeoutId: NodeJS.Timeout | null = null;

    const processBatch = () => {
      if (batch.length > 0) {
        processor([...batch]);
        batch = [];
      }
      timeoutId = null;
    };

    const scheduleBatch = () => {
      if (timeoutId === null) {
        timeoutId = setTimeout(processBatch, delay);
      }
    };

    return {
      add: (item: T) => {
        batch.push(item);
        if (batch.length >= batchSize) {
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          processBatch();
        } else {
          scheduleBatch();
        }
      },
      flush: () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        processBatch();
      },
      clear: () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        batch = [];
      }
    };
  }

  /**
   * Optimize search operations
   */
  static createSearchIndex(
    items: AnalysisResult[]
  ): {
    search: (query: string) => AnalysisResult[];
    addItem: (item: AnalysisResult) => void;
    removeItem: (id: string) => void;
  } {
    const searchIndex = new Map<string, Set<string>>();
    const itemsMap = new Map<string, AnalysisResult>();

    const addToIndex = (item: AnalysisResult) => {
      itemsMap.set(item.id, item);
      
      // Index searchable fields
      const searchableText = [
        item.question,
        item.analysis,
        item.analysisType,
        item.style,
        ...(item.followUpQuestions || [])
      ].join(' ').toLowerCase();

      // Create word tokens
      const words = searchableText.split(/\s+/).filter(word => word.length > 2);
      
      words.forEach(word => {
        if (!searchIndex.has(word)) {
          searchIndex.set(word, new Set());
        }
        searchIndex.get(word)!.add(item.id);
      });
    };

    // Build initial index
    items.forEach(addToIndex);

    return {
      search: (query: string) => {
        const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
        
        if (queryWords.length === 0) return [];

        // Find items that match all query words
        let resultIds: Set<string> | null = null;
        
        queryWords.forEach(word => {
          const matchingIds = searchIndex.get(word) || new Set();
          
          if (resultIds === null) {
            resultIds = new Set(matchingIds);
          } else {
            // Intersection
            resultIds = new Set([...resultIds].filter(id => matchingIds.has(id)));
          }
        });

        return resultIds ? 
          Array.from(resultIds).map(id => itemsMap.get(id)!).filter(Boolean) : 
          [];
      },
      addItem: addToIndex,
      removeItem: (id: string) => {
        itemsMap.delete(id);
        // Remove from search index
        searchIndex.forEach(idSet => idSet.delete(id));
      }
    };
  }
}
