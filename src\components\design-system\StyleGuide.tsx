/**
 * Design System Style Guide
 * 
 * A comprehensive showcase of the design system components and patterns.
 * This component serves as both documentation and testing for the design system.
 */

import { <PERSON>, CardHeader, Card<PERSON>ontent, CardFooter } from '@/components/ui/card';
import { 
  Palette, 
  Type, 
  Layout, 
  Zap, 
  Heart, 
  Star,
  Download,
  Settings,
  Plus,
  Search,
  Bell,
  User
} from 'lucide-react';

export const StyleGuide: React.FC = () => {
  const { mode, toggleMode, getColor, getSpacing } = useAppTheme();

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-12">
      {/* Header */}
      <header className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary-hover bg-clip-text text-transparent">
          Design System Style Guide
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          A comprehensive showcase of our unified design system components, patterns, and guidelines.
        </p>
        <Button onClick={toggleMode} variant="outline">
          Switch to {mode === 'dark' ? 'Light' : 'Dark'} Mode
        </Button>
      </header>

      {/* Color Palette */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Palette className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Color Palette</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Primary Colors */}
          <Card>
            <CardHeader title="Primary Colors" />
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-primary"></div>
                <span className="text-sm">Primary</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-primary-hover"></div>
                <span className="text-sm">Primary Hover</span>
              </div>
            </CardContent>
          </Card>

          {/* Semantic Colors */}
          <Card>
            <CardHeader title="Semantic Colors" />
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-success"></div>
                <span className="text-sm">Success</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-warning"></div>
                <span className="text-sm">Warning</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-destructive"></div>
                <span className="text-sm">Destructive</span>
              </div>
            </CardContent>
          </Card>

          {/* Neutral Colors */}
          <Card>
            <CardHeader title="Neutral Colors" />
            <CardContent className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-background border"></div>
                <span className="text-sm">Background</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-muted"></div>
                <span className="text-sm">Muted</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-accent"></div>
                <span className="text-sm">Accent</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Typography */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Type className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Typography</h2>
        </div>
        
        <Card>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold">Heading 1</h1>
              <h2 className="text-3xl font-semibold">Heading 2</h2>
              <h3 className="text-2xl font-semibold">Heading 3</h3>
              <h4 className="text-xl font-medium">Heading 4</h4>
              <p className="text-base">Body text - Regular paragraph text with normal weight and spacing.</p>
              <p className="text-sm text-muted-foreground">Caption text - Smaller text for secondary information.</p>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Buttons */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Zap className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Buttons</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Button Variants */}
          <Card>
            <CardHeader title="Button Variants" />
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <Button variant="default">Default</Button>
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
              </div>
              <div className="flex flex-wrap gap-3">
                <Button variant="destructive">Destructive</Button>
                <Button variant="success">Success</Button>
                <Button variant="warning">Warning</Button>
              </div>
            </CardContent>
          </Card>

          {/* Button Sizes */}
          <Card>
            <CardHeader title="Button Sizes" />
            <CardContent className="space-y-4">
              <div className="flex flex-wrap items-center gap-3">
                <Button size="xs">Extra Small</Button>
                <Button size="sm">Small</Button>
                <Button size="md">Medium</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </CardContent>
          </Card>

          {/* Button States */}
          <Card>
            <CardHeader title="Button States" />
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <Button>Normal</Button>
                <Button loading>Loading</Button>
                <Button disabled>Disabled</Button>
                <Button leftIcon={<Plus className="h-4 w-4" />}>With Icon</Button>
              </div>
            </CardContent>
          </Card>

          {/* Icon Buttons */}
          <Card>
            <CardHeader title="Icon Buttons" />
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-3">
                <Button size="icon" variant="default">
                  <Search className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="outline">
                  <Settings className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="ghost">
                  <Bell className="h-4 w-4" />
                </Button>
                <Button size="icon" variant="destructive">
                  <User className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Cards */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Layout className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Cards</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Default Card */}
          <Card variant="default">
            <CardHeader title="Default Card" subtitle="Basic card with shadow" />
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This is a default card with standard styling and shadow.
              </p>
            </CardContent>
            <CardFooter>
              <Button size="sm">Action</Button>
            </CardFooter>
          </Card>

          {/* Elevated Card */}
          <Card variant="elevated">
            <CardHeader title="Elevated Card" subtitle="Enhanced shadow on hover" />
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This card has enhanced shadows and hover effects.
              </p>
            </CardContent>
            <CardFooter>
              <Button size="sm" variant="outline">Action</Button>
            </CardFooter>
          </Card>

          {/* Interactive Card */}
          <Card variant="elevated" interactive>
            <CardHeader title="Interactive Card" subtitle="Clickable with hover effects" />
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This card responds to hover and click interactions.
              </p>
            </CardContent>
            <CardFooter>
              <Button size="sm" variant="ghost">Action</Button>
            </CardFooter>
          </Card>
        </div>
      </section>

      {/* Badges */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Star className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Badges</h2>
        </div>
        
        <Card>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <Badge variant="default">Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Form Elements */}
      <section className="space-y-6">
        <div className="flex items-center gap-3">
          <Heart className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold">Form Elements</h2>
        </div>
        
        <Card>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input placeholder="Default input" />
              <Input placeholder="Disabled input" disabled />
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="text-center py-8 border-t">
        <p className="text-muted-foreground">
          Design System v1.0.0 - Built with React, TypeScript, and Tailwind CSS
        </p>
      </footer>
    </div>
  );
};
