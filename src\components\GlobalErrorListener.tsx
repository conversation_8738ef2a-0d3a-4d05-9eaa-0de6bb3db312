/**
 * Global Error Listener
 * 
 * Captures and logs all unhandled errors and promise rejections
 * throughout the application for comprehensive error tracking.
 */

import { useEffect } from 'react';
import { useErrorStore } from '@/stores/useErrorStore';

interface GlobalErrorListenerProps {
  children?: React.ReactNode;
}

export const GlobalErrorListener: React.FC<GlobalErrorListenerProps> = ({ children }) => {
  const { addError } = useErrorStore();

  useEffect(() => {
    console.log('🛡️ Global Error Listener initialized');

    // Handle JavaScript errors
    const handleGlobalError = (event: ErrorEvent) => {
      console.group('🚨 Global Error Captured');
      console.error('Error:', event.error);
      console.error('Message:', event.message);
      console.error('Filename:', event.filename);
      console.error('Line:', event.lineno);
      console.error('Column:', event.colno);
      console.error('Stack:', event.error?.stack);
      console.error('Event:', event);
      console.groupEnd();

      // Add to error store
      addError({
        message: event.message || 'Unknown global error',
        severity: 'high',
        category: 'system',
        context: 'Global Error Handler',
        stack: event.error?.stack,
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          type: 'javascript-error',
        },
      });

      // Prevent default browser error handling in development
      if (process.env.NODE_ENV === 'development') {
        event.preventDefault();
      }
    };

    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.group('🚨 Unhandled Promise Rejection Captured');
      console.error('Reason:', event.reason);
      console.error('Promise:', event.promise);
      console.error('Event:', event);
      console.groupEnd();

      let errorMessage = 'Unhandled promise rejection';
      let errorStack: string | undefined;

      if (event.reason instanceof Error) {
        errorMessage = event.reason.message;
        errorStack = event.reason.stack;
      } else if (typeof event.reason === 'string') {
        errorMessage = event.reason;
      } else if (event.reason && typeof event.reason === 'object') {
        errorMessage = JSON.stringify(event.reason);
      }

      // Add to error store
      addError({
        message: errorMessage,
        severity: 'high',
        category: 'system',
        context: 'Unhandled Promise Rejection',
        stack: errorStack,
        metadata: {
          reason: event.reason,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          type: 'promise-rejection',
        },
      });

      // Prevent default browser handling in development
      if (process.env.NODE_ENV === 'development') {
        event.preventDefault();
      }
    };

    // Handle resource loading errors
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement;
      
      console.group('🚨 Resource Loading Error Captured');
      console.error('Target:', target);
      console.error('Tag:', target?.tagName);
      console.error('Source:', (target as any)?.src || (target as any)?.href);
      console.error('Event:', event);
      console.groupEnd();

      // Add to error store
      addError({
        message: `Failed to load resource: ${(target as any)?.src || (target as any)?.href || 'unknown'}`,
        severity: 'medium',
        category: 'network',
        context: 'Resource Loading Error',
        metadata: {
          tagName: target?.tagName,
          src: (target as any)?.src,
          href: (target as any)?.href,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          type: 'resource-error',
        },
      });
    };

    // Handle security errors
    const handleSecurityError = (event: SecurityPolicyViolationEvent) => {
      console.group('🚨 Security Policy Violation Captured');
      console.error('Violated Directive:', event.violatedDirective);
      console.error('Blocked URI:', event.blockedURI);
      console.error('Original Policy:', event.originalPolicy);
      console.error('Event:', event);
      console.groupEnd();

      // Add to error store
      addError({
        message: `Security policy violation: ${event.violatedDirective}`,
        severity: 'high',
        category: 'security',
        context: 'Security Policy Violation',
        metadata: {
          violatedDirective: event.violatedDirective,
          blockedURI: event.blockedURI,
          originalPolicy: event.originalPolicy,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          type: 'security-violation',
        },
      });
    };

    // Add event listeners
    window.addEventListener('error', handleGlobalError, true);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleResourceError, true); // Capture phase for resource errors
    window.addEventListener('securitypolicyviolation', handleSecurityError);

    // Log performance and memory info periodically in development
    let performanceInterval: NodeJS.Timeout | null = null;
    if (process.env.NODE_ENV === 'development') {
      performanceInterval = setInterval(() => {
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          console.log('🔍 Memory Usage:', {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
            limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB',
          });
        }
      }, 30000); // Every 30 seconds
    }

    // Cleanup function
    return () => {
      console.log('🛡️ Global Error Listener cleanup');
      
      window.removeEventListener('error', handleGlobalError, true);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleResourceError, true);
      window.removeEventListener('securitypolicyviolation', handleSecurityError);
      
      if (performanceInterval) {
        clearInterval(performanceInterval);
      }
    };
  }, [addError]);

  // Log initial state
  useEffect(() => {
    console.log('🛡️ Global Error Listener - Initial State:', {
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      nodeEnv: process.env.NODE_ENV,
    });
  }, []);

  return children ? <>{children}</> : null;
};

export default GlobalErrorListener;
