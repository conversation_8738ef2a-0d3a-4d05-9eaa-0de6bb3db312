import React, { useState, useCallback } from 'react';
import { ChatSimulation, SimulationPrompt, SimpleNode } from './types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Play, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Zap,
  MessageSquare,
  Settings,
  BarChart3
} from 'lucide-react';
import { AnalysisResult } from '@/types/conversation';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface ChatSimulationManagerProps {
  selectedNodes: SimpleNode[];
  position: { x: number; y: number };
  onCreateSimulation: (name: string, description: string, sourceNodeIds: string[], prompts: SimulationPrompt[]) => void;
  onClose: () => void;
  existingSimulation?: ChatSimulation;
}

export const ChatSimulationManager: React.FC<ChatSimulationManagerProps> = ({
  selectedNodes,
  position,
  onCreateSimulation,
  onClose,
  existingSimulation
}) => {
  const [name, setName] = useState(existingSimulation?.name || '');
  const [description, setDescription] = useState(existingSimulation?.description || '');
  const [prompts, setPrompts] = useState<SimulationPrompt[]>(existingSimulation?.prompts || []);
  const [activeTab, setActiveTab] = useState('setup');

  const handleAddPrompt = useCallback(() => {
    const newPrompt: SimulationPrompt = {
      id: `prompt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: `Prompt ${prompts.length + 1}`,
      content: '',
      sourceType: 'custom'
    };
    setPrompts(prev => [...prev, newPrompt]);
  }, [prompts.length]);

  const handleUpdatePrompt = useCallback((promptId: string, updates: Partial<SimulationPrompt>) => {
    setPrompts(prev => prev.map(prompt => 
      prompt.id === promptId ? { ...prompt, ...updates } : prompt
    ));
  }, []);

  const handleRemovePrompt = useCallback((promptId: string) => {
    setPrompts(prev => prev.filter(prompt => prompt.id !== promptId));
  }, []);

  const generatePromptsFromNodes = useCallback(() => {
    const generatedPrompts: SimulationPrompt[] = [];
    
    selectedNodes.forEach((node, index) => {
      if (node.data.analysisRecord) {
        const record = node.data.analysisRecord;
        
        // Generate prompt based on analysis record
        const prompt: SimulationPrompt = {
          id: `generated_${node.id}_${Date.now()}`,
          name: `${record.analysisType} Simulation`,
          content: generatePromptContent(record),
          sourceType: 'node-derived',
          sourceNodeId: node.id
        };
        
        generatedPrompts.push(prompt);
      }
    });
    
    setPrompts(prev => [...prev, ...generatedPrompts]);
  }, [selectedNodes]);

  const generatePromptContent = (record: AnalysisResult): string => {
    let content = `Based on the ${record.analysisType} analysis of "${record.question}", `;
    
    switch (record.analysisType) {
      case 'multiple':
        content += 'provide multiple perspectives on this topic and explore different angles of discussion.';
        break;
      case 'deep':
        content += 'dive deeper into the underlying concepts and provide comprehensive insights.';
        break;
      case 'character':
        if (record.characterPersona) {
          content += `respond as ${record.characterPersona.name} would, maintaining their personality and perspective.`;
        } else {
          content += 'respond from a specific character perspective that would be relevant to this topic.';
        }
        break;
      case 'pros-cons':
        content += 'present both advantages and disadvantages, then engage in a balanced discussion.';
        break;
      case 'six-hats':
        content += 'apply the six thinking hats methodology to explore this topic from multiple cognitive perspectives.';
        break;
      case 'emotional-angles':
        content += 'explore the emotional dimensions and psychological aspects of this topic.';
        break;
      default:
        content += 'continue the conversation in a way that builds upon the previous analysis.';
    }
    
    content += `\n\nOriginal question: "${record.question}"`;
    content += `\nAnalysis style: ${record.style}`;
    
    if (record.analysis) {
      content += `\nPrevious analysis summary: ${record.analysis.substring(0, 200)}...`;
    }
    
    return content;
  };

  const handleSave = useCallback(() => {
    if (!name.trim()) {
      alert('Please enter a simulation name');
      return;
    }
    
    if (prompts.length === 0) {
      alert('Please add at least one prompt');
      return;
    }
    
    onCreateSimulation(
      name.trim(),
      description.trim(),
      selectedNodes.map(node => node.id),
      prompts
    );
    
    onClose();
  }, [name, description, selectedNodes, prompts, onCreateSimulation, onClose]);

  const modalStyle: React.CSSProperties = {
    position: 'absolute',
    left: Math.min(position.x, window.innerWidth - 600),
    top: Math.min(position.y, window.innerHeight - 700),
    zIndex: 25,
    width: '580px',
    height: '650px',
    backgroundColor: 'rgba(20, 20, 40, 0.98)',
    backdropFilter: 'blur(15px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    boxShadow: '0 12px 48px rgba(0, 0, 0, 0.6)',
  };

  return (
    <Card style={modalStyle}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <CardTitle className="text-white text-base flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            {existingSimulation ? 'Edit Simulation' : 'Create Chat Simulation'}
          </CardTitle>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white text-lg leading-none"
            style={{ background: 'none', border: 'none', cursor: 'pointer' }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </CardHeader>
      
      <CardContent className="h-full overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3 bg-white/10">
            <TabsTrigger value="setup" className="text-white">Setup</TabsTrigger>
            <TabsTrigger value="prompts" className="text-white">Prompts</TabsTrigger>
            <TabsTrigger value="preview" className="text-white">Preview</TabsTrigger>
          </TabsList>
          
          <div className="flex-1 overflow-hidden">
            <TabsContent value="setup" className="space-y-4 h-full overflow-y-auto">
              {/* Simulation Name */}
              <div>
                <Label htmlFor="sim-name" className="text-white text-sm">
                  Simulation Name *
                </Label>
                <Input
                  id="sim-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter simulation name..."
                  className="mt-1 bg-white/10 border-white/20 text-white placeholder-gray-400"
                />
              </div>

              {/* Simulation Description */}
              <div>
                <Label htmlFor="sim-description" className="text-white text-sm">
                  Description
                </Label>
                <Textarea
                  id="sim-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Describe the simulation purpose and goals..."
                  className="mt-1 bg-white/10 border-white/20 text-white placeholder-gray-400 resize-none"
                  rows={3}
                />
              </div>

              {/* Source Nodes */}
              <div>
                <Label className="text-white text-sm">Source Nodes ({selectedNodes.length})</Label>
                <div className="mt-2 max-h-32 overflow-y-auto space-y-1">
                  {selectedNodes.map((node, index) => (
                    <div key={index} className="flex items-center justify-between bg-white/5 rounded px-2 py-1">
                      <span className="text-gray-300 text-sm truncate">
                        {node.data.label || node.id}
                      </span>
                      {node.data.analysisRecord && (
                        <Badge variant="outline" className="text-xs">
                          {node.data.analysisRecord.analysisType}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="prompts" className="space-y-4 h-full overflow-y-auto">
              <div className="flex justify-between items-center">
                <Label className="text-white text-sm">Simulation Prompts ({prompts.length})</Label>
                <div className="flex gap-2">
                  <Button
                    onClick={generatePromptsFromNodes}
                    size="sm"
                    variant="outline"
                    className="border-white/20 text-white hover:bg-white/10"
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Auto-Generate
                  </Button>
                  <Button
                    onClick={handleAddPrompt}
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Prompt
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                {prompts.map((prompt, index) => (
                  <div key={prompt.id} className="bg-white/5 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <Input
                        value={prompt.name}
                        onChange={(e) => handleUpdatePrompt(prompt.id, { name: e.target.value })}
                        className="flex-1 mr-2 bg-white/10 border-white/20 text-white text-sm"
                        placeholder="Prompt name..."
                      />
                      <Button
                        onClick={() => handleRemovePrompt(prompt.id)}
                        size="sm"
                        variant="ghost"
                        className="text-red-400 hover:text-red-300 hover:bg-red-500/10"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    <Textarea
                      value={prompt.content}
                      onChange={(e) => handleUpdatePrompt(prompt.id, { content: e.target.value })}
                      placeholder="Enter prompt content..."
                      className="bg-white/10 border-white/20 text-white placeholder-gray-400 resize-none text-sm"
                      rows={4}
                    />
                    
                    <div className="flex justify-between items-center mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {prompt.sourceType === 'node-derived' ? 'Auto-generated' : 'Custom'}
                      </Badge>
                      {prompt.sourceNodeId && (
                        <span className="text-xs text-gray-400">
                          From: {selectedNodes.find(n => n.id === prompt.sourceNodeId)?.data.label || prompt.sourceNodeId}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
                
                {prompts.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <MessageSquare className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No prompts added yet</p>
                    <p className="text-sm">Click "Auto-Generate" or "Add Prompt" to get started</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4 h-full overflow-y-auto">
              <div className="bg-white/5 rounded-lg p-4">
                <h3 className="text-white font-medium mb-2">Simulation Overview</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Name:</span>
                    <span className="text-white">{name || 'Untitled Simulation'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Source Nodes:</span>
                    <span className="text-white">{selectedNodes.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Prompts:</span>
                    <span className="text-white">{prompts.length}</span>
                  </div>
                </div>
              </div>
              
              {description && (
                <div className="bg-white/5 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2">Description</h3>
                  <p className="text-gray-300 text-sm">{description}</p>
                </div>
              )}
            </TabsContent>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t border-white/10">
            <Button
              onClick={handleSave}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {existingSimulation ? 'Update' : 'Create'} Simulation
            </Button>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
};
