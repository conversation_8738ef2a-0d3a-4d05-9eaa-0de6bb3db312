import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Save, Copy, Edit3, Trash2 } from "lucide-react";

interface ResultCardActionsProps {
  /** Save the entire result (main Save button) */
  onSave: () => void;
  /** Copy the result content */
  onCopy: () => void;
  /** Toggle the refinement UI */
  onToggleRefinement: () => void;
  /** Delete the result */
  onDelete: () => void;
  /** Show the refinement button */
  showRefinement?: boolean;
}

export const ResultCardActions: React.FC<ResultCardActionsProps> = ({
  onSave,
  onCopy,
  onToggleRefinement,
  onDelete,
  showRefinement = true,
}) => {
  return (
    <div className="flex gap-3">
      <Button
        onClick={onSave}
        variant="outline"
        size="sm"
        className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 font-medium"
      >
        <Save className="mr-2 h-4 w-4" />
        Save
      </Button>
      <Button
        onClick={onCopy}
        variant="outline"
        size="sm"
        className="bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 font-medium"
      >
        <Copy className="mr-2 h-4 w-4" />
        Copy
      </Button>
      {showRefinement && (
        <Button
          onClick={onToggleRefinement}
          variant="outline"
          size="sm"
          className="bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 font-medium"
        >
          <Edit3 className="mr-2 h-4 w-4" />
          Refine
        </Button>
      )}
      <Button
        onClick={onDelete}
        variant="destructive"
        size="sm"
        className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 font-medium"
      >
        <Trash2 className="mr-2 h-4 w-4" />
        Delete
      </Button>
    </div>
  );
};
