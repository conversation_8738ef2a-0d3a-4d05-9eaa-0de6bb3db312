import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
// @ts-expect-error - OrbitControls is not typed in @types/three, or path is problematic
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GUI } from 'dat.gui';
import { createLights } from './sceneElements';
import { NodeData, CanvasData, VisualConnection, SimpleNode } from './types'; 
import { useForceDirectedLayout, ForceDirectedLayoutOptions } from './useForceDirectedLayout';

interface LivingDataCanvasProps {
  initialData?: CanvasData;
}

interface DraggedNodeInfo {
  nodeId: string;
  offset: THREE.Vector2; 
}

const LivingDataCanvasSafe: React.FC<LivingDataCanvasProps> = ({ initialData }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  
  const [nodes, setNodes] = useState<Map<string, SimpleNode>>(new Map());
  const [visualConnections, setVisualConnections] = useState<Map<string, VisualConnection>>(new Map());

  const raycaster = useRef(new THREE.Raycaster());
  const mouse = useRef(new THREE.Vector2());
  const worldMouse = useRef(new THREE.Vector3());
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [draggedNodeInfo, setDraggedNodeInfo] = useState<DraggedNodeInfo | null>(null);
  const guiRef = useRef<GUI | null>(null);
  const originalNodePositionsRef = useRef<Map<string, THREE.Vector2>>(new Map());
  const animationTimeRef = useRef(0);

  // Force-directed layout integration
  const initialLayoutOptions: ForceDirectedLayoutOptions = {
    repulsionStrength: 50,
    attractionStrength: 0.05,
    restLength: 5,
    dampingFactor: 0.95,
    maxSpeed: 1,
    centerForceStrength: 0.01,
    iterationsPerFrame: 1,
  };

  const [layoutOptions, setLayoutOptions] = useState<ForceDirectedLayoutOptions>(initialLayoutOptions);
  const guiControlParamsRef = useRef<ForceDirectedLayoutOptions>({...layoutOptions});

  const { 
    step: stepLayout,
    isRunning: isLayoutRunning,
    setIsRunning: setIsLayoutRunning,
    config: layoutConfig
  } = useForceDirectedLayout(nodes, visualConnections, layoutOptions);

  // Custom createRenderer function
  const createRenderer = (mount: HTMLDivElement): THREE.WebGLRenderer => {
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(mount.clientWidth, mount.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    mount.appendChild(renderer.domElement);
    return renderer;
  };
  
  // Custom createOrthographicCamera function
  const createOrthographicCamera = (mount: HTMLDivElement): THREE.OrthographicCamera => {
    const aspect = mount.clientWidth / mount.clientHeight;
    const frustumSize = 20;
    const camera = new THREE.OrthographicCamera(
      frustumSize * aspect / -2,
      frustumSize * aspect / 2,
      frustumSize / 2,
      frustumSize / -2,
      1,
      1000
    );
    camera.position.z = 10;
    return camera;
  };

  // Calculate node degree for dynamic sizing
  const calculateNodeDegree = (nodeId: string, connections: CanvasData['connections']): number => {
    return connections.filter(conn => conn.source === nodeId || conn.target === nodeId).length;
  };

  // Get color based on category
  const getCategoryColor = (category: string): THREE.Color => {
    const colorMap: Record<string, number> = {
      'core': 0xff4444,
      'input': 0x4444ff,
      'process': 0x44ff44,
      'output': 0xffff44,
      'interface': 0xff44ff,
      'storage': 0x44ffff,
    };
    return new THREE.Color(colorMap[category] || 0xcccccc);
  };

  // Get edge color by strength (simpler version)
  const getEdgeColorByStrength = (strength: number): THREE.Color => {
    const hue = (1 - Math.min(Math.max(strength, 0), 1)) * 0.6;
    return new THREE.Color().setHSL(hue, 0.8, 0.6);
  };

  // Initialize Scene, Camera, Renderer, Lights, Controls
  useEffect(() => {
    if (!mountRef.current) return;

    guiControlParamsRef.current = {...layoutOptions};

    sceneRef.current = new THREE.Scene();
    sceneRef.current.background = new THREE.Color(0x1a1a2e);

    cameraRef.current = createOrthographicCamera(mountRef.current);
    rendererRef.current = createRenderer(mountRef.current);
    
    createLights(sceneRef.current);

    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);
    controlsRef.current.enableRotate = false;
    controlsRef.current.mouseButtons = {
      LEFT: THREE.MOUSE.PAN,
      MIDDLE: THREE.MOUSE.DOLLY,
      RIGHT: THREE.MOUSE.PAN
    };
    controlsRef.current.touches = {
      ONE: THREE.TOUCH.PAN,
      TWO: THREE.TOUCH.DOLLY_PAN
    };
    controlsRef.current.screenSpacePanning = true;

    let animationFrameId: number;

    const animate = () => {
      animationFrameId = requestAnimationFrame(animate);
      animationTimeRef.current += 0.016; // ~60fps

      if (isLayoutRunning) {
        stepLayout(draggedNodeInfo ? draggedNodeInfo.nodeId : null);
      }

      // Animate nodes (gentle pulsing for idle animation)
      nodes.forEach(node => {
        if (!node.isDragged) {
          const scale = 1 + Math.sin(animationTimeRef.current * 2 + node.position.x) * 0.05;
          node.mesh.scale.setScalar(scale);
        }
      });

      // Update visual connections
      visualConnections.forEach(conn => conn.update());
      
      controlsRef.current?.update();
      rendererRef.current?.render(sceneRef.current!, cameraRef.current!);
    };

    animate();

    // Setup enhanced dat.gui
    if (!guiRef.current) {
      guiRef.current = new GUI();
      const generalFolder = guiRef.current.addFolder('Canvas Controls');
      
      generalFolder.add({ 
        resetView: () => {
          if (cameraRef.current && mountRef.current) {
            const aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
            const frustumSize = 20;
            cameraRef.current.left = frustumSize * aspect / -2;
            cameraRef.current.right = frustumSize * aspect / 2;
            cameraRef.current.top = frustumSize / 2;
            cameraRef.current.bottom = frustumSize / -2;
            cameraRef.current.position.set(0,0,10);
            cameraRef.current.zoom = 1;
            cameraRef.current.updateProjectionMatrix();
            controlsRef.current?.reset();
          }
          resetNodePositions();
          setLayoutOptions(initialLayoutOptions);
        }
      }, 'resetView').name('🔄 Reset View & Nodes');

      const layoutFolder = guiRef.current.addFolder('Layout Controls');
      
      // Creative Layout Presets
      const layoutPresets: Record<string, ForceDirectedLayoutOptions> = {
        "🔍 Default View": initialLayoutOptions,
        "🎯 Tight Clusters": { 
          repulsionStrength: 30, attractionStrength: 0.15, restLength: 2.5, 
          dampingFactor: 0.9, maxSpeed: 0.8, centerForceStrength: 0.02, iterationsPerFrame: 1 
        },
        "🌐 Loose Exploration": { 
          repulsionStrength: 120, attractionStrength: 0.02, restLength: 12, 
          dampingFactor: 0.98, maxSpeed: 1.5, centerForceStrength: 0.005, iterationsPerFrame: 1 
        },
        "⚡ Quick Settle": { 
          repulsionStrength: 60, attractionStrength: 0.08, restLength: 4, 
          dampingFactor: 0.85, maxSpeed: 0.6, centerForceStrength: 0.015, iterationsPerFrame: 2 
        },
        "🕸️ Web Analysis": { 
          repulsionStrength: 40, attractionStrength: 0.12, restLength: 6, 
          dampingFactor: 0.92, maxSpeed: 1.2, centerForceStrength: 0.008, iterationsPerFrame: 1 
        },
        "🔬 Detail Focus": { 
          repulsionStrength: 80, attractionStrength: 0.25, restLength: 3, 
          dampingFactor: 0.88, maxSpeed: 0.5, centerForceStrength: 0.025, iterationsPerFrame: 2 
        },
      };
      
      const presetHolder = { currentPreset: "🔍 Default View" };
      layoutFolder.add(presetHolder, 'currentPreset', Object.keys(layoutPresets))
        .name('🎨 Analysis Preset')
        .onChange(presetName => {
          const selectedPreset = layoutPresets[presetName as keyof typeof layoutPresets];
          if (selectedPreset) {
            setLayoutOptions(selectedPreset);
          }
        });

      // Enhanced sliders with emojis
      layoutFolder.add(guiControlParamsRef.current, 'repulsionStrength', 10, 200).name('💥 Repulsion').onChange(val => setLayoutOptions(prev => ({...prev, repulsionStrength: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'attractionStrength', 0.01, 0.5, 0.01).name('🧲 Attraction').onChange(val => setLayoutOptions(prev => ({...prev, attractionStrength: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'restLength', 1, 20).name('📏 Rest Length').onChange(val => setLayoutOptions(prev => ({...prev, restLength: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'dampingFactor', 0.8, 0.99, 0.01).name('🌊 Damping').onChange(val => setLayoutOptions(prev => ({...prev, dampingFactor: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'maxSpeed', 0.1, 5, 0.1).name('🚀 Max Speed').onChange(val => setLayoutOptions(prev => ({...prev, maxSpeed: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'centerForceStrength', 0, 0.1, 0.001).name('🎯 Center Pull').onChange(val => setLayoutOptions(prev => ({...prev, centerForceStrength: val}))).listen();
      layoutFolder.add(guiControlParamsRef.current, 'iterationsPerFrame', 1, 10, 1).name('⚙️ Iterations/Frame').onChange(val => setLayoutOptions(prev => ({...prev, iterationsPerFrame: val}))).listen();
      
      layoutFolder.add({ toggleLayout: () => setIsLayoutRunning(prev => !prev) }, 'toggleLayout').name(isLayoutRunning ? '⏸️ Pause Layout' : '▶️ Run Layout');
      
      layoutFolder.add({ 
        reShuffleNodes: () => {
          nodes.forEach(node => {
            if (!node.isDragged && node.velocity) { 
              const shuffleStrength = layoutOptions.maxSpeed * 0.3;
              node.velocity.set(
                (Math.random() - 0.5) * shuffleStrength, 
                (Math.random() - 0.5) * shuffleStrength
              );
            }
          });
          if (!isLayoutRunning) setIsLayoutRunning(true);
        }
      }, 'reShuffleNodes').name('🎲 Re-Shuffle Nodes');
      
      layoutFolder.add({ 
        energizeLayout: () => {
          nodes.forEach(node => {
            if (!node.isDragged && node.velocity) {
              const energyBoost = layoutOptions.maxSpeed * 0.8;
              const angle = Math.random() * Math.PI * 2;
              node.velocity.add(new THREE.Vector2(
                Math.cos(angle) * energyBoost,
                Math.sin(angle) * energyBoost
              ));
            }
          });
          if (!isLayoutRunning) setIsLayoutRunning(true);
        }
      }, 'energizeLayout').name('⚡ Energize Layout');
      
      layoutFolder.open();
    }

    // Event handlers
    const handleResize = () => {
      if (cameraRef.current && rendererRef.current && mountRef.current) {
        const aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
        const frustumSize = cameraRef.current.userData.frustumSize || 20;
        cameraRef.current.left = frustumSize * aspect / -2;
        cameraRef.current.right = frustumSize * aspect / 2;
        cameraRef.current.top = frustumSize / 2;
        cameraRef.current.bottom = frustumSize / -2;
        cameraRef.current.updateProjectionMatrix();
        rendererRef.current.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
      }
    };

    if (cameraRef.current) cameraRef.current.userData.frustumSize = 20; 
    window.addEventListener('resize', handleResize);
    
    const updateMouseWorldPosition = (event: MouseEvent) => {
      if (!mountRef.current || !cameraRef.current) return;
      const rect = mountRef.current.getBoundingClientRect();
      mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      worldMouse.current.set(mouse.current.x, mouse.current.y, 0.5);
      worldMouse.current.unproject(cameraRef.current);
    };

    const onMouseMove = (event: MouseEvent) => {
      updateMouseWorldPosition(event);

      if (draggedNodeInfo && nodes.has(draggedNodeInfo.nodeId)) {
        const node = nodes.get(draggedNodeInfo.nodeId)!;
        node.position.x = worldMouse.current.x - draggedNodeInfo.offset.x;
        node.position.y = worldMouse.current.y - draggedNodeInfo.offset.y;
        node.mesh.position.set(node.position.x, node.position.y, 0);
        if (node.velocity) node.velocity.set(0,0);
        node.isDragged = true;
      } else {
        if (!sceneRef.current || !cameraRef.current || nodes.size === 0) return;

        raycaster.current.setFromCamera(mouse.current, cameraRef.current);
        const intersects = raycaster.current.intersectObjects(
          Array.from(nodes.values()).map(n => n.mesh)
        );

        let newHoveredId: string | null = null;
        if (intersects.length > 0) {
          const firstIntersect = intersects[0].object;
          if (firstIntersect.userData.nodeId) {
            newHoveredId = firstIntersect.userData.nodeId as string;
          }
        }

        if (hoveredNodeId !== newHoveredId) {
          if (hoveredNodeId && nodes.has(hoveredNodeId)) {
            nodes.get(hoveredNodeId)?.setHighlight(false);
          }
          if (newHoveredId && nodes.has(newHoveredId)) {
            nodes.get(newHoveredId)?.setHighlight(true);
          }
          setHoveredNodeId(newHoveredId);
        }
      }
    };

    const onMouseDown = (event: MouseEvent) => {
      if (event.button !== 0) return;
      updateMouseWorldPosition(event);

      if (!sceneRef.current || !cameraRef.current || nodes.size === 0) return;

      raycaster.current.setFromCamera(mouse.current, cameraRef.current);
      const intersects = raycaster.current.intersectObjects(
        Array.from(nodes.values()).map(n => n.mesh)
      );

      if (intersects.length > 0) {
        const firstIntersect = intersects[0];
        const clickedNodeId = firstIntersect.object.userData.nodeId as string;
        
        if (clickedNodeId && nodes.has(clickedNodeId) && controlsRef.current) {
          controlsRef.current.enabled = false;

          const node = nodes.get(clickedNodeId)!;
          const offset = new THREE.Vector2(
            worldMouse.current.x - node.position.x,
            worldMouse.current.y - node.position.y
          );
          
          setDraggedNodeInfo({ nodeId: clickedNodeId, offset: offset });
          node.isDragged = true;
          if (node.velocity) node.velocity.set(0,0);
        }
      }
    };

    const onMouseUp = (event: MouseEvent) => {
      if (event.button !== 0) return; 
      if (controlsRef.current) {
        controlsRef.current.enabled = true; 
      }
      if (draggedNodeInfo) {
        const node = nodes.get(draggedNodeInfo.nodeId);
        if (node) {
          node.isDragged = false;
        }
        setDraggedNodeInfo(null);
      }
    };

    mountRef.current.addEventListener('mousemove', onMouseMove);
    mountRef.current.addEventListener('mousedown', onMouseDown);
    document.addEventListener('mouseup', onMouseUp);

    return () => {
      window.removeEventListener('resize', handleResize);
      mountRef.current?.removeEventListener('mousemove', onMouseMove);
      mountRef.current?.removeEventListener('mousedown', onMouseDown);
      document.removeEventListener('mouseup', onMouseUp);
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      if (mountRef.current && rendererRef.current?.domElement) {
        mountRef.current.removeChild(rendererRef.current.domElement);
      }
      controlsRef.current?.dispose();
      
      if (guiRef.current) {
        guiRef.current.destroy();
        guiRef.current = null;
      }
      nodes.forEach(node => node.dispose());
      visualConnections.forEach(conn => conn.dispose());
    };
  }, [isLayoutRunning, stepLayout, layoutOptions, nodes, visualConnections]);

  // Effect to update dat.gui controllers when layoutOptions state changes
  useEffect(() => {
    guiControlParamsRef.current.repulsionStrength = layoutOptions.repulsionStrength;
    guiControlParamsRef.current.attractionStrength = layoutOptions.attractionStrength;
    guiControlParamsRef.current.restLength = layoutOptions.restLength;
    guiControlParamsRef.current.dampingFactor = layoutOptions.dampingFactor;
    guiControlParamsRef.current.maxSpeed = layoutOptions.maxSpeed;
    guiControlParamsRef.current.centerForceStrength = layoutOptions.centerForceStrength;
    guiControlParamsRef.current.iterationsPerFrame = layoutOptions.iterationsPerFrame || 1;

    if (guiRef.current) {
      const layoutFolder = guiRef.current.__folders['Layout Controls'];
      if (layoutFolder) {
        layoutFolder.__controllers.forEach(controller => {
          if (guiControlParamsRef.current.hasOwnProperty(controller.property) && controller.property !== 'currentPreset') {
            controller.updateDisplay();
          }
        });
      }
    }
  }, [layoutOptions]);

  // Update GUI play/pause button text when isLayoutRunning changes
  useEffect(() => {
    if (guiRef.current) {
      const layoutFolder = guiRef.current.__folders['Layout Controls'];
      if (layoutFolder) {
        const controller = layoutFolder.__controllers.find(c => c.property === 'toggleLayout');
        if (controller) {
          controller.name(isLayoutRunning ? '⏸️ Pause Layout' : '▶️ Run Layout');
        }
      }
    }
  }, [isLayoutRunning]);

  // Load initial data with enhanced node styling but safer edge rendering
  useEffect(() => {
    if (initialData && sceneRef.current) {
      const newNodesMap = new Map<string, SimpleNode>();
      originalNodePositionsRef.current.clear();

      initialData.nodes.forEach(nodeData => {
        // Calculate dynamic sizing based on node degree and value
        const degree = calculateNodeDegree(nodeData.id, initialData.connections);
        const baseSize = 0.3;
        const sizeMultiplier = 1 + (nodeData.value || 50) / 100 + degree * 0.1;
        const nodeSize = baseSize * sizeMultiplier;

        // Get color based on category
        const nodeColor = getCategoryColor(nodeData.category || 'default');

        // Create enhanced node geometry
        const geometry = new THREE.CircleGeometry(nodeSize, 32);
        const material = new THREE.MeshBasicMaterial({ 
          color: nodeColor,
          transparent: true,
          opacity: 0.9
        });
        const circleMesh = new THREE.Mesh(geometry, material);
        
        // Add a subtle ring for enhanced visual appeal
        const ringGeometry = new THREE.RingGeometry(nodeSize * 1.1, nodeSize * 1.3, 32);
        const ringMaterial = new THREE.MeshBasicMaterial({ 
          color: nodeColor,
          transparent: true,
          opacity: 0.3
        });
        const ringMesh = new THREE.Mesh(ringGeometry, ringMaterial);
        circleMesh.add(ringMesh);
        
        const position = nodeData.position 
          ? new THREE.Vector2(nodeData.position.x, nodeData.position.y)
          : new THREE.Vector2((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);
        
        circleMesh.position.set(position.x, position.y, 0);
        circleMesh.userData.nodeId = nodeData.id;
        circleMesh.userData.originalColor = nodeColor;

        sceneRef.current!.add(circleMesh);

        const simpleNode: SimpleNode = {
          id: nodeData.id,
          mesh: circleMesh,
          position: position.clone(),
          data: nodeData,
          velocity: new THREE.Vector2(),
          force: new THREE.Vector2(),
          isDragged: false,
          setHighlight: (highlight) => {
            const material = circleMesh.material as THREE.MeshBasicMaterial;
            const ringMaterial = ringMesh.material as THREE.MeshBasicMaterial;
            if (highlight) {
              material.color.set(0xffff00);
              ringMaterial.color.set(0xffff00);
              circleMesh.scale.setScalar(1.2);
            } else {
              material.color.copy(nodeColor);
              ringMaterial.color.copy(nodeColor);
              circleMesh.scale.setScalar(1.0);
            }
          },
          dispose: () => {
            try {
              if (sceneRef.current && circleMesh) {
                sceneRef.current.remove(circleMesh);
              }
              if (geometry) geometry.dispose();
              if (material) material.dispose();
              if (ringGeometry) ringGeometry.dispose();
              if (ringMaterial) ringMaterial.dispose();
            } catch (error) {
              console.warn('Error disposing node:', error);
            }
          }
        };
        newNodesMap.set(nodeData.id, simpleNode);
        originalNodePositionsRef.current.set(nodeData.id, position.clone());
      });
      setNodes(newNodesMap);

      // Safe connections with basic LineSegments instead of Line2
      const newConnectionsMap = new Map<string, VisualConnection>();
      initialData.connections.forEach(connData => {
        const sourceNode = newNodesMap.get(connData.source);
        const targetNode = newNodesMap.get(connData.target);

        if (sourceNode && targetNode && sceneRef.current) {
          const edgeStrength = connData.strength || 1;
          const edgeColor = getEdgeColorByStrength(edgeStrength);

          // Use basic LineSegments instead of Line2 to avoid count errors
          const points = [
            new THREE.Vector3(sourceNode.mesh.position.x, sourceNode.mesh.position.y, 0),
            new THREE.Vector3(targetNode.mesh.position.x, targetNode.mesh.position.y, 0)
          ];
          const geometry = new THREE.BufferGeometry().setFromPoints(points);
          const material = new THREE.LineBasicMaterial({ 
            color: edgeColor,
            transparent: true,
            opacity: 0.8
          });
          const line = new THREE.Line(geometry, material);
          sceneRef.current.add(line);

          const visualConn: VisualConnection = {
            id: connData.id,
            lineMesh: line,
            sourceNode: sourceNode, 
            targetNode: targetNode, 
            update: () => {
              try {
                const positions = [
                  sourceNode.mesh.position.x, sourceNode.mesh.position.y, sourceNode.mesh.position.z,
                  targetNode.mesh.position.x, targetNode.mesh.position.y, targetNode.mesh.position.z,
                ];
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
                geometry.attributes.position.needsUpdate = true;
              } catch (error) {
                console.warn('Error updating connection:', error);
              }
            },
            dispose: () => {
              try {
                if (sceneRef.current && line) {
                  sceneRef.current.remove(line);
                }
                if (geometry) geometry.dispose();
                if (material) material.dispose();
              } catch (error) {
                console.warn('Error disposing connection:', error);
              }
            }
          };
          newConnectionsMap.set(connData.id, visualConn);
        }
      });
      setVisualConnections(newConnectionsMap);
    }
  }, [initialData]);

  const resetNodePositions = () => {
    nodes.forEach(node => {
      const originalPos = originalNodePositionsRef.current.get(node.id);
      if (originalPos) {
        node.position.copy(originalPos);
        node.mesh.position.set(originalPos.x, originalPos.y, 0);
        if (node.velocity) node.velocity.set(0,0);
      }
    });
  };
  
  return <div ref={mountRef} style={{ width: '100%', height: 'calc(100vh - 70px)', border: '1px solid #333', touchAction: 'none' }} />;
};

// Enhanced Sample Data
const sampleData: CanvasData = {
  nodes: [
    { id: 'node1', label: 'Analytics Hub', position: { x: -2, y: 3, z: 0 }, color: 0xff4444, value: 85, category: 'core', tags: ['analytics', 'hub'] },
    { id: 'node2', label: 'Data Source', position: { x: 2, y: 4, z: 0 }, color: 0x4444ff, value: 65, category: 'input', tags: ['data', 'source'] },
    { id: 'node3', label: 'Processing', position: { x: 0, y: 1, z: 0 }, color: 0x44ff44, value: 45, category: 'process', tags: ['processing', 'transform'] },
    { id: 'node4', label: 'Visualization', position: { x: 5, y: 2, z: 0 }, color: 0xffff44, value: 75, category: 'output', tags: ['visualization', 'display'] },
    { id: 'node5', label: 'User Interface', position: { x: -4, y: 5, z: 0 }, color: 0xff44ff, value: 90, category: 'interface', tags: ['ui', 'interaction'] },
    { id: 'node6', label: 'Storage', position: { x: 1, y: -2, z: 0 }, color: 0x44ffff, value: 55, category: 'storage', tags: ['database', 'storage'] },
  ],
  connections: [
    { id: 'conn1', source: 'node1', target: 'node2', strength: 1.2, label: 'data flow' },
    { id: 'conn2', source: 'node2', target: 'node3', strength: 0.8, label: 'processing' },
    { id: 'conn3', source: 'node1', target: 'node3', strength: 1.0, label: 'control' },
    { id: 'conn4', source: 'node4', target: 'node2', strength: 0.9, label: 'feedback' },
    { id: 'conn5', source: 'node5', target: 'node1', strength: 1.5, label: 'user input' },
    { id: 'conn6', source: 'node3', target: 'node6', strength: 1.1, label: 'storage' },
    { id: 'conn7', source: 'node6', target: 'node4', strength: 0.7, label: 'retrieve' },
  ],
};

export const LivingDataCanvasSafeContainer: React.FC = () => {  return (
    <div style={{backgroundColor: '#0a0a1e', padding: '10px', height: '100vh', boxSizing: 'border-box'}}>
      <h2 style={{color: 'white', textAlign: 'center', margin: '0 0 10px 0'}}>Living Data Canvas - Safe Mode</h2>
      <LivingDataCanvasSafe initialData={sampleData} />
    </div>
  );
};

export default LivingDataCanvasSafeContainer;
