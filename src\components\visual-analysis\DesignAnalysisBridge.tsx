/**
 * Design Analysis Bridge
 * 
 * Bridges the gap between professional design tools and analysis data,
 * allowing users to convert analysis results into visual design elements
 * and create professional presentations from data insights.
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, Tabs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Palette, 
  Type, 
  Square, 
  Circle,
  ArrowRight,
  Download,
  Share2,
  Wand2,
  Layout,
  Image,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  FileText,
  Presentation,
  Sparkles
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { DesignElement } from '@/services/designIntegration/analysisToDesignConverter';
import type { AnalysisResult } from '@/types/conversation';

interface DesignAnalysisBridgeProps {
  analysisResults: AnalysisResult[];
  selectedResultId?: string;
  onCreateDesignElement?: (element: DesignElement) => void;
  onExportPresentation?: (format: ExportFormat) => void;
  className?: string;
}

type ExportFormat = 'pdf' | 'png' | 'svg' | 'pptx';

export const DesignAnalysisBridge: React.FC<DesignAnalysisBridgeProps> = ({
  analysisResults,
  selectedResultId,
  onCreateDesignElement,
  onExportPresentation,
  className,
}) => {
  const [activeTab, setActiveTab] = useState<'convert' | 'templates' | 'export'>('convert');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [generatingElements, setGeneratingElements] = useState(false);

  const selectedResult = analysisResults.find(r => r.id === selectedResultId);

  // Design element templates
  const elementTemplates = [
    {
      id: 'text-summary',
      name: 'Text Summary',
      description: 'Convert analysis to formatted text block',
      icon: Type,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      id: 'info-card',
      name: 'Information Card',
      description: 'Create a styled card with key insights',
      icon: Square,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      id: 'process-flow',
      name: 'Process Flow',
      description: 'Visualize analysis steps as a flow diagram',
      icon: ArrowRight,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      id: 'data-chart',
      name: 'Data Visualization',
      description: 'Convert insights to charts and graphs',
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  // Presentation templates
  const presentationTemplates = [
    {
      id: 'executive-summary',
      name: 'Executive Summary',
      description: 'Professional summary for stakeholders',
      icon: Presentation,
      slides: 5,
    },
    {
      id: 'detailed-analysis',
      name: 'Detailed Analysis',
      description: 'Comprehensive analysis presentation',
      icon: FileText,
      slides: 12,
    },
    {
      id: 'infographic',
      name: 'Infographic',
      description: 'Visual infographic layout',
      icon: Image,
      slides: 1,
    },
  ];

  const handleCreateElement = useCallback(async (templateId: string) => {
    if (!selectedResult) return;

    setGeneratingElements(true);

    try {
      // Simulate element generation based on template and analysis data
      const element: DesignElement = {
        id: `element-${Date.now()}`,
        type: templateId.includes('chart') ? 'chart' : 
              templateId.includes('card') ? 'shape' : 'text',
        data: {
          content: selectedResult.analysis,
          title: selectedResult.question,
        },
        style: {
          color: '#1f2937',
          fontSize: 14,
          fontFamily: 'Inter',
          backgroundColor: '#ffffff',
          borderColor: '#e5e7eb',
          borderWidth: 1,
        },
        position: { x: 100, y: 100 },
        size: { width: 300, height: 200 },
        metadata: {
          analysisId: selectedResult.id,
          analysisType: selectedResult.analysisType,
          createdAt: Date.now(),
          tags: [],
          category: 'auto',
          template: templateId,
        },
      };

      onCreateDesignElement?.(element);
    } catch (error) {
      console.error('Error creating design element:', error);
    } finally {
      setGeneratingElements(false);
    }
  }, [selectedResult, onCreateDesignElement]);

  const handleExport = useCallback((format: ExportFormat) => {
    onExportPresentation?.(format);
  }, [onExportPresentation]);

  const renderConvertTab = () => (
    <div className="space-y-6">
      {/* Analysis Selection */}
      {!selectedResult && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-6 text-center">
            <Wand2 className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-gray-600">Select an analysis result to convert to design elements</p>
          </CardContent>
        </Card>
      )}

      {selectedResult && (
        <>
          {/* Selected Analysis Preview */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Selected Analysis</h3>
                <Badge variant="outline">{selectedResult.analysisType}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-2">{selectedResult.question}</p>
              <p className="text-xs text-gray-500 line-clamp-3">{selectedResult.analysis}</p>
            </CardContent>
          </Card>

          {/* Element Templates */}
          <div className="space-y-4">
            <h3 className="font-semibold">Convert to Design Elements</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {elementTemplates.map((template) => {
                const Icon = template.icon;
                return (
                  <Card
                    key={template.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handleCreateElement(template.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={cn("p-2 rounded-lg", template.bgColor)}>
                          <Icon className={cn("h-4 w-4", template.color)} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm">{template.name}</h4>
                          <p className="text-xs text-gray-600 mt-1">{template.description}</p>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                          disabled={generatingElements}
                        >
                          {generatingElements ? (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-primary" />
                          ) : (
                            <ArrowRight className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );

  const renderTemplatesTab = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="font-semibold">Presentation Templates</h3>
        <div className="grid grid-cols-1 gap-4">
          {presentationTemplates.map((template) => {
            const Icon = template.icon;
            return (
              <Card
                key={template.id}
                className={cn(
                  "cursor-pointer transition-all",
                  selectedTemplate === template.id 
                    ? "ring-2 ring-primary shadow-md" 
                    : "hover:shadow-md"
                )}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-primary/10">
                        <Icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <p className="text-sm text-gray-600">{template.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary">{template.slides} slides</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {selectedTemplate && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Generate Presentation</h4>
                <p className="text-sm text-gray-600">
                  Create a presentation using the selected template and analysis data
                </p>
              </div>
              <Button onClick={() => handleCreateElement(selectedTemplate)}>
                <Sparkles className="h-4 w-4 mr-2" />
                Generate
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderExportTab = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="font-semibold">Export Options</h3>
        <div className="grid grid-cols-2 gap-4">
          {[
            { format: 'pdf' as ExportFormat, name: 'PDF Document', icon: FileText },
            { format: 'png' as ExportFormat, name: 'PNG Image', icon: Image },
            { format: 'svg' as ExportFormat, name: 'SVG Vector', icon: Layout },
            { format: 'pptx' as ExportFormat, name: 'PowerPoint', icon: Presentation },
          ].map(({ format, name, icon: Icon }) => (
            <Button
              key={format}
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleExport(format)}
            >
              <Icon className="h-6 w-6" />
              <span className="text-sm">{name}</span>
            </Button>
          ))}
        </div>
      </div>

      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Share2 className="h-5 w-5 text-primary" />
            <div>
              <h4 className="font-medium">Share & Collaborate</h4>
              <p className="text-sm text-gray-600">
                Export and share your analysis presentations with team members
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <h2 className="text-lg font-semibold">Design Analysis Bridge</h2>
        <p className="text-sm text-gray-600">
          Convert analysis results into professional design elements and presentations
        </p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'convert' | 'templates' | 'export')}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="convert">Convert</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
          </TabsList>
          
          <TabsContent value="convert" className="mt-6">
            {renderConvertTab()}
          </TabsContent>
          
          <TabsContent value="templates" className="mt-6">
            {renderTemplatesTab()}
          </TabsContent>
          
          <TabsContent value="export" className="mt-6">
            {renderExportTab()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DesignAnalysisBridge;
