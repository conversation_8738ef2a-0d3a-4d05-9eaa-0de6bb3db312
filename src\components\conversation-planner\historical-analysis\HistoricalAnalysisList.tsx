import React from 'react';
import { UserNote } from '@/types/conversation';
import { EnhancedNoteCard } from './EnhancedNoteCard';
import { Search } from 'lucide-react';
import { useLibraryStore } from '@/stores/useLibraryStore';
import { useUIStore } from '@/stores/useUIStore';
import { OptimizedList } from "@/components/conversation-planner/visualization/PerformanceOptimizer";

interface HistoricalAnalysisListProps {
  notes: UserNote[];
  onView: (note: UserNote) => void;
  onDiscover: (note: UserNote) => void;
  onDelete: (noteId: string) => void;
}

export const HistoricalAnalysisList: React.FC<HistoricalAnalysisListProps> = ({
  notes,
  onView,
  onDiscover,
  onDelete,
}) => {
  const { savedAnalyses } = useLibraryStore();
  const openModal = useUIStore((state) => state.openModal);

  const handleView = (note: UserNote) => {
    const parentAnalysis = savedAnalyses.find(sa => 
      sa.results.some(r => r.id === note.questionId)
    );
    if (parentAnalysis) {
        openModal("SAVED_ANALYSIS_VIEWER", { analysis: parentAnalysis });
    } else {
        // Fallback to original behavior if no analysis is found
        onView(note);
        console.warn("Could not find parent analysis for note:", note, "Falling back to Note Editor.");
    }
  };

  if (notes.length === 0) {
    return (
      <div className="text-center py-8">
        <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No results found</h3>
        <p className="text-muted-foreground">
          Try adjusting your search terms or filters.
        </p>
      </div>
    );
  }

  return (
    <OptimizedList
      items={notes}
      renderItem={(note) => (
        <EnhancedNoteCard
          key={note.id}
          note={note}
          onView={() => handleView(note)}
          onDiscover={() => onDiscover(note)}
          onDelete={() => onDelete(note.id)}
        />
      )}
      itemHeight={120} // Adjust as needed for your card height
      containerHeight={600} // Adjust as needed for your scrollable area
      className="grid gap-4"
    />
  );
};
