
import { useState, useRef } from 'react';
import { Canvas, Line, TPointerEventInfo, FabricObject } from 'fabric';
import { CustomFabricObject } from '../../fabric-helpers';

interface ConnectHandleObject extends FabricObject {
    data?: {
        type: 'connect-handle';
    };
}

interface ConnectionHandlerProps {
    canvas: Canvas | null;
    onConnectStart: (nodeId: string) => void;
    onConnectEnd: (fromId: string, toId: string) => void;
    onConnectCancel: () => void;
}

export const useConnectionHandler = ({ canvas, onConnectStart, onConnectEnd, onConnectCancel }: ConnectionHandlerProps) => {
    const connectingFromNode = useRef<CustomFabricObject | null>(null);
    const [connectingLine, setConnectingLine] = useState<Line | null>(null);
    
    const isConnecting = () => !!connectingLine;

    const isConnectionAttempt = (opt: TPointerEventInfo): boolean => {        if (opt.subTargets && opt.subTargets.length > 0) {
            const handle = opt.subTargets.find(st => (st as ConnectHandleObject).data?.type === 'connect-handle');
            return !!(handle && opt.target && (opt.target as CustomFabricObject).data?.id);
        }
        return false;
    };

    const handleConnectStart = (opt: TPointerEventInfo) => {
        if (!canvas || !opt.target) return;
        
        const evt = opt.e;
        evt.preventDefault();
        evt.stopPropagation();
        
        const startNode = opt.target as CustomFabricObject;
        const startNodeId = startNode.data!.id;
        onConnectStart(startNodeId);
        connectingFromNode.current = startNode;

        const pointer = canvas.getPointer(evt);
        const startPoint = {
            x: (startNode.left || 0) + (startNode.width || 0) / 2,
            y: (startNode.top || 0) + (startNode.height || 0) / 2
        };

        const line = new Line([startPoint.x, startPoint.y, pointer.x, pointer.y], {
            stroke: 'hsl(var(--primary))',
            strokeWidth: 2,
            selectable: false,
            evented: false,
        });
        canvas.add(line);
        setConnectingLine(line);
    };

    const handleConnectMove = (opt: TPointerEventInfo) => {
        if (!canvas || !connectingLine) return;
        const pointer = canvas.getPointer(opt.e);
        connectingLine.set({ x2: pointer.x, y2: pointer.y });
        canvas.requestRenderAll();
    };

    const handleConnectEnd = (opt: TPointerEventInfo) => {
        if (!canvas || !connectingLine || !connectingFromNode.current) return;
        
        canvas.remove(connectingLine);
        setConnectingLine(null);
        
        const fromId = connectingFromNode.current.data!.id;
        connectingFromNode.current = null;
        
        const toId = (opt.target as CustomFabricObject | undefined)?.data?.id;
        if (toId && toId !== fromId) {
            onConnectEnd(fromId, toId);
        } else {
            onConnectCancel();
        }
    };

    return { isConnecting, isConnectionAttempt, handleConnectStart, handleConnectMove, handleConnectEnd, connectingLine };
};
