// Enforce JSDoc/TSDoc coverage for all exported functions/classes
const { execSync } = require('child_process');

try {
  const output = execSync('npx eslint "src/**/*.{ts,tsx,js,jsx}" --rule "jsdoc/require-jsdoc: [2, { require: { FunctionDeclaration: true, ClassDeclaration: true, MethodDefinition: true } }]"', { encoding: 'utf8' });
  console.log('JSDoc coverage check passed.');
} catch (e) {
  console.error('JSDoc coverage check failed. Please add missing documentation.');
  process.exit(1);
}
