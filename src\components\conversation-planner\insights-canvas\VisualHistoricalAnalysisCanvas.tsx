import React, { useState, useMemo } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, EyeOff, Settings, Download, RefreshCcw } from "lucide-react";
import { CanvasToolbar } from './CanvasToolbar';
import { AICanvasControls } from './AICanvasControls';
import { FabricCanvas } from './FabricCanvas';
import { EmptyCanvasState } from './EmptyCanvasState';
import { AIConnectionSuggestions } from './AIConnectionSuggestions';
import { SmartClusterOverlay } from './SmartClusterOverlay';
import { PatternOverlayVisualization } from './PatternOverlayVisualization';
import { AIInsightsSummary } from './AIInsightsSummary';
import { AICanvasExport } from './AICanvasExport';
import { CanvasActionMenu } from './CanvasActionMenu';
import { useVisualCanvas } from './hooks/useVisualCanvas';
import { useCanvasStore } from '@/stores/useCanvasStore';

// Define a new store or context for canvas viewport state if it doesn't exist
// For this example, we'll lift state to VisualHistoricalAnalysisCanvas
// and pass it down. Alternatively, use a dedicated Zustand store slice.

export const VisualHistoricalAnalysisCanvas: React.FC = () => {
  // Lifted state for zoom and pan
  const [currentZoom, setCurrentZoom] = useState(1);
  const [currentPan, setCurrentPan] = useState({ x: 0, y: 0 });
  
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  
  const {
    notes,
    connections,
    searchQuery,
    setSearchQuery,
    analysisTypeFilter,
    setAnalysisTypeFilter,
    tagFilter,
    setTagFilter,
    allTags,
    nodePositions,
    connectingNodeId,
    snapToGrid,
    setSnapToGrid,
    contextMenu,
    closeContextMenu,
    handleNodeDoubleClick,
    handleViewDetails,
    handleConnectEnd,
    handleConnectCancel,
    handleContextMenu,
    
    // AI state
    isAnalyzing,
    connectionSuggestions,
    smartClusters,
    patternOverlays,
    insightSummary,
    showSuggestions,
    showClusters,
    showPatterns,
    showInsights,
    showExport,
    isAIEnabled,
    
    // AI actions
    runAIAnalysis,
    acceptSuggestion,
    rejectSuggestion,
    clearAIData,
    toggleSuggestions,
    toggleClusters,
    togglePatterns,
    toggleInsights,
    toggleExport,
    
    // Canvas interactions
    handleConnectNodes,
    handleToggleConnection,
    handleDeleteNote,
    getNodePosition,
  } = useVisualCanvas();

  // Get the setNodePositions function from the store for node movement
  const setNodePositions = useCanvasStore((state) => state.setNodePositions);

  const filteredNotes = useMemo(() => {
    return notes.filter(note => {
      const searchMatch = !searchQuery || 
        note.noteText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const typeMatch = analysisTypeFilter.length === 0 || 
        (note.analysisType && analysisTypeFilter.includes(note.analysisType));
      
      const tagMatch = tagFilter.length === 0 || 
        note.tags?.some(tag => tagFilter.includes(tag));
      
      return searchMatch && typeMatch && tagMatch;
    });
  }, [notes, searchQuery, analysisTypeFilter, tagFilter]);

  const filteredConnections = useMemo(() => {
    const visibleNodeIds = new Set(filteredNotes.map(note => note.id));
    return connections.filter(conn => 
      visibleNodeIds.has(conn.fromId) && visibleNodeIds.has(conn.toId)
    );
  }, [connections, filteredNotes]);

  const areSelectedNodesConnected = useMemo(() => {
    if (selectedNodes.length !== 2) return false;
    const [node1, node2] = selectedNodes;
    return filteredConnections.some(conn => 
      (conn.fromId === node1 && conn.toId === node2) ||
      (conn.fromId === node2 && conn.toId === node1)
    );
  }, [selectedNodes, filteredConnections]);

  const handleZoomIn = () => { /* Logic will be handled by FabricCanvas via usePanAndZoomHandler */ };
  const handleZoomOut = () => { /* Logic will be handled by FabricCanvas via usePanAndZoomHandler */ };
  const handleResetView = () => {
    // This function might need to interact with Fabric.js directly 
    // or trigger an action in usePanAndZoomHandler to reset view.
    // For now, we update local state, assuming Fabric will also reset.
    setCurrentZoom(1);
    setCurrentPan({ x: 0, y: 0 });
    // TODO: Ensure Fabric canvas also resets its viewport
  };

  const handleNodeMove = (updates: { id: string; x: number; y: number }[]) => {
    setNodePositions(currentPositions => {
      const positionMap = new Map(currentPositions.map(p => [p.id, p]));
      
      updates.forEach(update => {
        const existingPosition = positionMap.get(update.id);
        if (existingPosition) {
          existingPosition.x = update.x;
          existingPosition.y = update.y;
        } else {
          positionMap.set(update.id, update);
        }
      });
      
      return Array.from(positionMap.values());
    });
  };

  const handleSelectionChange = (selectedIds: string[], isMultiSelect: boolean) => {
    setSelectedNodes(selectedIds);
  };

  const handleCanvasClick = () => {
    setSelectedNodes([]);
    closeContextMenu();
  };

  const handleConnectStart = (nodeId: string) => {
    // This is handled by the canvas state
  };

  const handleRunAIAnalysis = () => {
    const nodePositionsForAnalysis = nodePositions.map(pos => ({
      id: pos.id,
      x: pos.x,
      y: pos.y
    }));
    runAIAnalysis(filteredNotes, filteredConnections, nodePositionsForAnalysis);
  };

  // Show empty state if no notes
  if (filteredNotes.length === 0 && notes.length === 0) {
    return <EmptyCanvasState />;
  }

  return (
    <div className="h-[800px] flex flex-col bg-slate-50 rounded-lg border border-slate-200">
      {/* Canvas Toolbar - Pass real zoom */}
      <CanvasToolbar
        zoom={currentZoom} // Use lifted state
        onZoomIn={handleZoomIn} // May become direct Fabric calls or be removed if toolbar controls Fabric directly
        onZoomOut={handleZoomOut} // May become direct Fabric calls or be removed
        onResetView={handleResetView}
        selectedNodesCount={selectedNodes.length}
        onConnectNodes={handleToggleConnection}
        snapToGrid={snapToGrid}
        onToggleSnapToGrid={() => setSnapToGrid(!snapToGrid)}
        searchQuery={searchQuery}
        onSearchQueryChange={setSearchQuery}
        analysisTypeFilter={analysisTypeFilter}
        onAnalysisTypeFilterChange={setAnalysisTypeFilter}
        allTags={allTags}
        tagFilter={tagFilter}
        onTagFilterChange={setTagFilter}
        areSelectedNodesConnected={areSelectedNodesConnected}
      />

      {/* AI Canvas Controls */}
      <div className="border-b border-slate-200 p-2">
        <div id="ai-canvas-controls">
          <AICanvasControls
            isAIEnabled={isAIEnabled}
            showSuggestions={showSuggestions}
            showClusters={showClusters}
            showPatterns={showPatterns}
            showInsights={showInsights}
            showExport={showExport}
            isAnalyzing={isAnalyzing}
            onToggleSuggestions={toggleSuggestions}
            onToggleClusters={toggleClusters}
            onTogglePatterns={togglePatterns}
            onToggleInsights={toggleInsights}
            onToggleExport={toggleExport}
            onRunAnalysis={handleRunAIAnalysis}
            onClearAIData={clearAIData}
          />
        </div>
      </div>

      {/* Fabric Canvas - Pass callbacks to update lifted state */}
      <div className="flex-grow relative">
        <FabricCanvas
          notes={filteredNotes}
          connections={filteredConnections}
          nodePositions={nodePositions}
          selectedNodes={selectedNodes}
          connectingNodeId={connectingNodeId}
          onNodeMove={handleNodeMove}
          onSelectionChange={handleSelectionChange}
          onNodeDoubleClick={handleNodeDoubleClick}
          onCanvasClick={handleCanvasClick}
          onConnectStart={handleConnectStart} // Ensure this is correctly named if it's `handleConnectStart`
          onConnectEnd={handleConnectEnd}
          onConnectCancel={handleConnectCancel}
          onContextMenu={handleContextMenu}
          onZoomChange={setCurrentZoom} // Pass callback to update zoom
          onPanChange={setCurrentPan} // Pass callback to update pan
        />
        {/* AI Overlays - Pass real zoom and pan */}
        {isAIEnabled && (
          <>
            {showSuggestions && connectionSuggestions.length > 0 && (
              <AIConnectionSuggestions 
                suggestions={connectionSuggestions} 
                onAccept={acceptSuggestion} 
                onReject={rejectSuggestion} 
              />
            )}
            {showClusters && smartClusters.length > 0 && (
              <SmartClusterOverlay 
                clusters={smartClusters} 
                zoom={currentZoom} // Pass real zoom
                pan={currentPan} // Pass real pan
              />
            )}
            {showPatterns && patternOverlays.length > 0 && (
              <PatternOverlayVisualization 
                patterns={patternOverlays} 
                zoom={currentZoom} // Pass real zoom
                pan={currentPan} // Pass real pan
              />
            )}
            {showInsights && insightSummary && (
              <AIInsightsSummary summary={insightSummary} />
            )}
            {showExport && (
              <AICanvasExport 
                notes={notes} 
                connections={connections} 
                nodePositions={nodePositions} 
                onClose={toggleExport} 
              />
            )}
          </>
        )}
      </div>

      {contextMenu && (
        <CanvasActionMenu 
          menuState={contextMenu} 
          onClose={closeContextMenu} 
        />
      )}
    </div>
  );
};
