import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Info, 
  Zap, 
  Eye, 
  <PERSON>Pointer,
  <PERSON>rk<PERSON>,
  ArrowR<PERSON>,
  X,
  Maximize2,
  Play,
  Pause
} from 'lucide-react';
import { ChainNode, VisualizationChain } from '@/types/visualization';

interface InteractiveOverlayProps {
  chain: VisualizationChain;
  hoveredNodeId?: string | null;
  selectedNodeId?: string | null;
  onNodeClick?: (nodeId: string) => void;
  onPlayAnimation?: () => void;
  onPauseAnimation?: () => void;
  isAnimationPlaying?: boolean;
  className?: string;
}

interface NodeTooltipProps {
  node: ChainNode;
  position: { x: number; y: number };
  onClose: () => void;
}

const NodeTooltip: React.FC<NodeTooltipProps> = ({ node, position, onClose }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: 10 }}
      transition={{ duration: 0.2 }}
      className="fixed z-50 pointer-events-auto"
      style={{
        left: position.x,
        top: position.y - 10,
        transform: 'translateX(-50%)'
      }}
    >
      <Card className="w-80 shadow-xl border-2 border-blue-200 bg-white/95 backdrop-blur-sm">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
              <span>{node.title}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 h-6 w-6"
            >
              <X className="w-3 h-3" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-xs text-gray-600 mb-3 leading-relaxed">
            {node.content}
          </p>
          
          {node.metadata && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs">
                <Badge variant="outline" className="text-xs">
                  {node.type}
                </Badge>
                <Badge 
                  variant={node.status === 'completed' ? 'default' : 'secondary'} 
                  className="text-xs"
                >
                  {node.status}
                </Badge>
              </div>
              
              {node.metadata.timestamp && (
                <div className="text-xs text-gray-500">
                  {node.metadata.timestamp.toLocaleString()}
                </div>
              )}
              
              {node.metadata.duration && (
                <div className="text-xs text-gray-500">
                  Duration: {node.metadata.duration}ms
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

interface AnimationControlsProps {
  isPlaying: boolean;
  onPlay: () => void;
  onPause: () => void;
  onReset?: () => void;
  progress?: number;
}

const AnimationControls: React.FC<AnimationControlsProps> = ({
  isPlaying,
  onPlay,
  onPause,
  onReset,
  progress = 0
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40"
    >
      <Card className="bg-white/90 backdrop-blur-sm shadow-lg border border-gray-200">
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            <Button
              variant={isPlaying ? "secondary" : "default"}
              size="sm"
              onClick={isPlaying ? onPause : onPlay}
              className="flex items-center gap-2"
            >
              {isPlaying ? (
                <>
                  <Pause className="w-4 h-4" />
                  Pause
                </>
              ) : (
                <>
                  <Play className="w-4 h-4" />
                  Play
                </>
              )}
            </Button>
            
            {onReset && (
              <Button
                variant="outline"
                size="sm"
                onClick={onReset}
                className="text-xs"
              >
                Reset
              </Button>
            )}
            
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Sparkles className="w-3 h-3" />
              <span>Animation</span>
            </div>
            
            {progress > 0 && (
              <div className="w-20 h-1 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-blue-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const InteractiveOverlay: React.FC<InteractiveOverlayProps> = ({
  chain,
  hoveredNodeId,
  selectedNodeId,
  onNodeClick,
  onPlayAnimation,
  onPauseAnimation,
  isAnimationPlaying = false,
  className
}) => {
  const [tooltipNode, setTooltipNode] = useState<ChainNode | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showControls, setShowControls] = useState(false);
  const [animationProgress, setAnimationProgress] = useState(0);

  // Handle node hover for tooltip
  useEffect(() => {
    if (hoveredNodeId) {
      const node = chain.nodes.find(n => n.id === hoveredNodeId);
      if (node) {
        setTooltipNode(node);
        // Position tooltip based on mouse position (simplified)
        setTooltipPosition({ x: window.innerWidth / 2, y: 200 });
      }
    } else {
      setTooltipNode(null);
    }
  }, [hoveredNodeId, chain.nodes]);

  // Show controls when there are animations available
  useEffect(() => {
    const hasAnimations = chain.connections.some(conn => conn.animated);
    setShowControls(hasAnimations);
  }, [chain.connections]);

  // Simulate animation progress
  useEffect(() => {
    if (isAnimationPlaying) {
      const interval = setInterval(() => {
        setAnimationProgress(prev => {
          if (prev >= 100) {
            return 0;
          }
          return prev + 2;
        });
      }, 100);
      
      return () => clearInterval(interval);
    }
  }, [isAnimationPlaying]);

  const handleNodeClick = (nodeId: string) => {
    onNodeClick?.(nodeId);
  };

  const handlePlayAnimation = () => {
    setAnimationProgress(0);
    onPlayAnimation?.();
  };

  const handlePauseAnimation = () => {
    onPauseAnimation?.();
  };

  const handleResetAnimation = () => {
    setAnimationProgress(0);
    onPauseAnimation?.();
  };

  return (
    <div className={cn("relative", className)}>
      {/* Node Selection Indicators */}
      <AnimatePresence>
        {selectedNodeId && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-10"
          >
            {/* Selection glow effect */}
            <div className="absolute inset-0">
              {chain.nodes
                .filter(node => node.id === selectedNodeId)
                .map(node => (
                  <motion.div
                    key={node.id}
                    className="absolute rounded-lg border-2 border-blue-400 bg-blue-100/20"
                    style={{
                      left: node.position.x - 4,
                      top: node.position.y - 4,
                      width: 'calc(100% + 8px)',
                      height: 'calc(100% + 8px)'
                    }}
                    animate={{
                      boxShadow: [
                        '0 0 0 0 rgba(59, 130, 246, 0.4)',
                        '0 0 0 10px rgba(59, 130, 246, 0)',
                        '0 0 0 0 rgba(59, 130, 246, 0)'
                      ]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hover Effects */}
      <AnimatePresence>
        {hoveredNodeId && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 pointer-events-none z-5"
          >
            {/* Hover highlight */}
            {chain.nodes
              .filter(node => node.id === hoveredNodeId)
              .map(node => (
                <motion.div
                  key={node.id}
                  className="absolute rounded-lg bg-blue-50/50 border border-blue-200"
                  style={{
                    left: node.position.x - 2,
                    top: node.position.y - 2,
                    width: 'calc(100% + 4px)',
                    height: 'calc(100% + 4px)'
                  }}
                  animate={{
                    scale: [1, 1.02, 1],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tooltip */}
      <AnimatePresence>
        {tooltipNode && (
          <NodeTooltip
            node={tooltipNode}
            position={tooltipPosition}
            onClose={() => setTooltipNode(null)}
          />
        )}
      </AnimatePresence>

      {/* Animation Controls */}
      <AnimatePresence>
        {showControls && (
          <AnimationControls
            isPlaying={isAnimationPlaying}
            onPlay={handlePlayAnimation}
            onPause={handlePauseAnimation}
            onReset={handleResetAnimation}
            progress={animationProgress}
          />
        )}
      </AnimatePresence>

      {/* Interactive Hints */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
        className="absolute top-4 right-4 z-30"
      >
        <Card className="bg-white/80 backdrop-blur-sm shadow-sm border border-gray-200">
          <CardContent className="p-3">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <MousePointer className="w-3 h-3" />
              <span>Hover nodes for details</span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};
