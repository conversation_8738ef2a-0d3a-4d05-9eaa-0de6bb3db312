import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  MessageSquare, 
  Cog, 
  Target, 
  ChevronRight,
  Clock,
  Zap,
  Brain,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { AnalysisResult } from '@/types/conversation';
import { cn } from '@/lib/utils';

interface AnalysisColumnsViewProps {
  results: AnalysisResult[];
  onResultSelect?: (result: AnalysisResult) => void;
  onFollowUpQuestion?: (question: string) => void;
  selectedResultId?: string;
  className?: string;
}

interface ColumnData {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  borderColor: string;
}

const columnConfig: Record<string, ColumnData> = {
  input: {
    title: 'Input & Context',
    icon: MessageSquare,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200'
  },
  process: {
    title: 'Analysis Process',
    icon: Brain,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200'
  },
  output: {
    title: 'Results & Insights',
    icon: Target,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200'
  }
};

export const AnalysisColumnsView: React.FC<AnalysisColumnsViewProps> = ({
  results,
  onResultSelect,
  onFollowUpQuestion,
  selectedResultId,
  className
}) => {
  const [hoveredResult, setHoveredResult] = useState<string | null>(null);
  const [expandedColumn, setExpandedColumn] = useState<string | null>(null);

  const handleResultClick = (result: AnalysisResult) => {
    onResultSelect?.(result);
  };

  const handleFollowUpClick = (question: string) => {
    onFollowUpQuestion?.(question);
  };

  const toggleColumnExpansion = (column: string) => {
    setExpandedColumn(expandedColumn === column ? null : column);
  };

  const getAnalysisTypeColor = (type: string) => {
    const colors = {
      'multiple': 'bg-blue-100 text-blue-800',
      'deep': 'bg-purple-100 text-purple-800',
      'character': 'bg-green-100 text-green-800',
      'pros-cons': 'bg-orange-100 text-orange-800',
      'six-hats': 'bg-indigo-100 text-indigo-800',
      'emotional-angles': 'bg-pink-100 text-pink-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const renderInputColumn = () => (
    <motion.div
      className={cn(
        "flex-1 min-w-0 transition-all duration-300",
        expandedColumn === 'input' ? 'flex-[2]' : '',
        expandedColumn && expandedColumn !== 'input' ? 'flex-[0.5]' : ''
      )}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.1 }}
    >
      <Card className={cn("h-full", columnConfig.input.borderColor)}>
        <CardHeader className={cn("pb-3", columnConfig.input.bgColor)}>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <columnConfig.input.icon className={cn("w-5 h-5", columnConfig.input.color)} />
              <span className={cn("text-lg font-semibold", columnConfig.input.color)}>
                {columnConfig.input.title}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleColumnExpansion('input')}
              className="p-1"
            >
              <ChevronRight className={cn(
                "w-4 h-4 transition-transform",
                expandedColumn === 'input' ? 'rotate-90' : ''
              )} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <div className="p-4 space-y-4">
              {results.map((result, index) => (
                <motion.div
                  key={result.id}
                  className={cn(
                    "p-4 rounded-lg border cursor-pointer transition-all duration-200",
                    selectedResultId === result.id 
                      ? "border-blue-300 bg-blue-50 shadow-md" 
                      : "border-gray-200 hover:border-blue-200 hover:bg-blue-25",
                    hoveredResult === result.id ? "shadow-lg" : ""
                  )}
                  onClick={() => handleResultClick(result)}
                  onMouseEnter={() => setHoveredResult(result.id)}
                  onMouseLeave={() => setHoveredResult(null)}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <h4 className="font-medium text-sm text-gray-800 leading-relaxed">
                        {result.question}
                      </h4>
                      <Badge 
                        variant="secondary" 
                        className={cn("text-xs ml-2 flex-shrink-0", getAnalysisTypeColor(result.analysisType))}
                      >
                        {result.analysisType}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>{result.timestamp.toLocaleString()}</span>
                      <Separator orientation="vertical" className="h-3" />
                      <span className="font-medium">{result.model}</span>
                    </div>

                    {result.seedContext && (
                      <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                        <span className="font-medium">Context:</span> {result.seedContext.substring(0, 100)}...
                      </div>
                    )}

                    {result.characterPersona && (
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          Character: {result.characterPersona.name}
                        </Badge>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </motion.div>
  );

  const renderProcessColumn = () => (
    <motion.div
      className={cn(
        "flex-1 min-w-0 transition-all duration-300",
        expandedColumn === 'process' ? 'flex-[2]' : '',
        expandedColumn && expandedColumn !== 'process' ? 'flex-[0.5]' : ''
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <Card className={cn("h-full", columnConfig.process.borderColor)}>
        <CardHeader className={cn("pb-3", columnConfig.process.bgColor)}>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <columnConfig.process.icon className={cn("w-5 h-5", columnConfig.process.color)} />
              <span className={cn("text-lg font-semibold", columnConfig.process.color)}>
                {columnConfig.process.title}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleColumnExpansion('process')}
              className="p-1"
            >
              <ChevronRight className={cn(
                "w-4 h-4 transition-transform",
                expandedColumn === 'process' ? 'rotate-90' : ''
              )} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <div className="p-4">
              {selectedResultId ? (
                <div className="space-y-6">
                  {results
                    .filter(r => r.id === selectedResultId)
                    .map((result) => (
                      <motion.div
                        key={result.id}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="space-y-4"
                      >
                        {/* Processing Steps */}
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                            <Zap className="w-4 h-4 text-purple-600" />
                            <span className="text-sm font-medium text-purple-800">
                              AI Model: {result.model}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                            <Cog className="w-4 h-4 text-purple-600" />
                            <span className="text-sm font-medium text-purple-800">
                              Analysis Type: {result.analysisType}
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
                            <Sparkles className="w-4 h-4 text-purple-600" />
                            <span className="text-sm font-medium text-purple-800">
                              Style: {result.style}
                            </span>
                          </div>
                        </div>

                        {/* Processing Flow */}
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm text-gray-800">Processing Flow</h4>
                          <div className="space-y-2">
                            {[
                              'Question Analysis',
                              'Context Processing',
                              'AI Model Inference',
                              'Response Generation',
                              'Output Formatting'
                            ].map((step, index) => (
                              <motion.div
                                key={step}
                                className="flex items-center gap-3 p-2 rounded"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                              >
                                <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center">
                                  <span className="text-xs font-medium text-purple-600">
                                    {index + 1}
                                  </span>
                                </div>
                                <span className="text-sm text-gray-700">{step}</span>
                                <ArrowRight className="w-3 h-3 text-gray-400 ml-auto" />
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Brain className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p className="text-sm">Select an analysis to view the process</p>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </motion.div>
  );

  const renderOutputColumn = () => (
    <motion.div
      className={cn(
        "flex-1 min-w-0 transition-all duration-300",
        expandedColumn === 'output' ? 'flex-[2]' : '',
        expandedColumn && expandedColumn !== 'output' ? 'flex-[0.5]' : ''
      )}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3 }}
    >
      <Card className={cn("h-full", columnConfig.output.borderColor)}>
        <CardHeader className={cn("pb-3", columnConfig.output.bgColor)}>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <columnConfig.output.icon className={cn("w-5 h-5", columnConfig.output.color)} />
              <span className={cn("text-lg font-semibold", columnConfig.output.color)}>
                {columnConfig.output.title}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleColumnExpansion('output')}
              className="p-1"
            >
              <ChevronRight className={cn(
                "w-4 h-4 transition-transform",
                expandedColumn === 'output' ? 'rotate-90' : ''
              )} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <div className="p-4">
              {selectedResultId ? (
                <div className="space-y-4">
                  {results
                    .filter(r => r.id === selectedResultId)
                    .map((result) => (
                      <motion.div
                        key={result.id}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="space-y-4"
                      >
                        <div className="prose prose-sm max-w-none">
                          <div className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
                            {result.analysis}
                          </div>
                        </div>

                        {result.followUpQuestions && result.followUpQuestions.length > 0 && (
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm text-gray-800">Follow-up Questions</h4>
                            <div className="space-y-2">
                              {result.followUpQuestions.map((question, index) => (
                                <Button
                                  key={index}
                                  variant="outline"
                                  size="sm"
                                  className="text-left justify-start h-auto p-3 text-xs"
                                  onClick={() => handleFollowUpClick(question)}
                                >
                                  {question}
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}

                        {result.rating && (
                          <div className="flex items-center gap-2 pt-2 border-t">
                            <span className="text-xs text-gray-500">Rating:</span>
                            <div className="flex">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <span
                                  key={star}
                                  className={cn(
                                    "text-sm",
                                    star <= result.rating! ? "text-yellow-400" : "text-gray-300"
                                  )}
                                >
                                  ★
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Target className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p className="text-sm">Select an analysis to view results</p>
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </motion.div>
  );

  if (results.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>No analysis results to display</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full", className)}>
      <div className="flex gap-4 h-full">
        {renderInputColumn()}
        {renderProcessColumn()}
        {renderOutputColumn()}
      </div>
    </div>
  );
};
