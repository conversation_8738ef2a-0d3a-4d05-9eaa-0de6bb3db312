/**
 * Canvas Quick Access Component
 * 
 * Provides quick access to the Visual Canvas from analysis results and other parts
 * of the application. Enables seamless workflow integration.
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { 
  <PERSON>, 
  ArrowRight, 
  Zap, 
  Eye, 
  Share2,
  <PERSON>rk<PERSON>
} from 'lucide-react';

interface CanvasQuickAccessProps {
  analysisResult?: AnalysisResult;
  className?: string;
  variant?: 'card' | 'inline' | 'floating';
  showPreview?: boolean;
}

export const CanvasQuickAccess: React.FC<CanvasQuickAccessProps> = ({
  analysisResult,
  className,
  variant = 'card',
  showPreview = true,
}) => {
  const { openCanvasWithAnalysis } = useCanvasDataBridge();

  const handleOpenCanvas = (mode: 'standard' | 'safe' | 'chat-analysis' = 'standard') => {
    if (analysisResult) {
      openCanvasWithAnalysis(analysisResult, mode);
    } else {
      // Fallback to direct navigation
      const { setMainTab, setCanvasMode } = useNavigationStore.getState();
      setCanvasMode(mode);
      setMainTab('canvas');
    }
  };

  const canvasFeatures = [
    {
      icon: Brain,
      title: '3D Visualization',
      description: 'Interactive 3D representation of your analysis',
    },
    {
      icon: Zap,
      title: 'Real-time Updates',
      description: 'Live data processing and visualization',
    },
    {
      icon: Share2,
      title: 'Collaborative',
      description: 'Share and collaborate on visual insights',
    },
  ];

  if (variant === 'inline') {
    return (
      <div className={cn("flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20 border border-blue-200 dark:border-blue-800", className)}>
        <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 text-white">
          <Brain className="h-5 w-5" />
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-blue-900 dark:text-blue-100">
            Visualize in Canvas
          </h4>
          <p className="text-sm text-blue-700 dark:text-blue-300">
            See your analysis in interactive 3D space
          </p>
        </div>
        <Button
          onClick={() => handleOpenCanvas()}
          className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
        >
          <Eye className="h-4 w-4 mr-2" />
          Open Canvas
        </Button>
      </div>
    );
  }

  if (variant === 'floating') {
    return (
      <div className={cn("fixed bottom-6 right-6 z-50", className)}>
        <Button
          onClick={() => handleOpenCanvas()}
          size="lg"
          className="rounded-full shadow-lg bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 hover:shadow-xl transition-all duration-300"
        >
          <Brain className="h-5 w-5 mr-2" />
          Canvas
        </Button>
      </div>
    );
  }

  // Default card variant
  return (
    <Card className={cn("shadow-soft border-border/50 overflow-hidden", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-500/20">
              <Brain className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Visual Canvas</h3>
              <p className="text-sm text-muted-foreground">
                Interactive data visualization workspace
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-700 dark:from-blue-900 dark:to-cyan-900 dark:text-blue-300">
            <Sparkles className="h-3 w-3 mr-1" />
            Enhanced
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {showPreview && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {canvasFeatures.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div
                  key={index}
                  className="p-3 rounded-lg bg-muted/30 border border-border/50 text-center"
                >
                  <Icon className="h-5 w-5 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                  <h4 className="text-sm font-medium mb-1">{feature.title}</h4>
                  <p className="text-xs text-muted-foreground">{feature.description}</p>
                </div>
              );
            })}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={() => handleOpenCanvas('standard')}
            className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
          >
            <Brain className="h-4 w-4 mr-2" />
            Open Enhanced Canvas
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
          
          <Button
            onClick={() => handleOpenCanvas('safe')}
            variant="outline"
            className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-950/20"
          >
            Safe Mode
          </Button>
        </div>

        {analysisResult && (
          <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Analysis Ready
              </span>
            </div>
            <p className="text-xs text-blue-700 dark:text-blue-300">
              Your analysis "{analysisResult.question}" is ready to be visualized in the canvas.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Hook for canvas integration (enhanced with data bridge)
export const useCanvasIntegration = () => {
  const { openCanvasWithAnalysis, sendAnalysisToCanvas } = useCanvasDataBridge();
  const { setMainTab, setCanvasMode } = useNavigationStore();

  const openCanvasWithData = (data: unknown, mode: 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated' = 'figma-integrated') => {
    if (data && data.id) {
      // If it's an analysis result, use the bridge
      openCanvasWithAnalysis(data, mode);
    } else {
      // Fallback for other data types
      sessionStorage.setItem('canvasData', JSON.stringify(data));
      setCanvasMode(mode);
      setMainTab('canvas');
    }
  };

  const openCanvas = (mode: 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated' = 'figma-integrated') => {
    setCanvasMode(mode);
    setMainTab('canvas');
  };

  return {
    openCanvasWithData,
    openCanvas,
    openCanvasWithAnalysis,
    sendAnalysisToCanvas,
  };
};

export default CanvasQuickAccess;
