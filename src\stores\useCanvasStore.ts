import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { UserNote } from "@/types/conversation";

export interface NodePosition {
  id: string;
  x: number;
  y: number;
}

export interface Connection {
  id: string;
  fromId: string;
  toId: string;
}

interface CanvasState {
  notes: UserNote[];
  nodePositions: NodePosition[];
  connections: Connection[];
  connectingNodeId: string | null;
  addNote: (note: UserNote) => Promise<void>;
  deleteNote: (id: string) => void;
  updateNote: (id: string, updatedNote: Partial<UserNote>) => void;
  setNodePositions: (positions: NodePosition[] | ((prev: NodePosition[]) => NodePosition[])) => void;
  setConnections: (connections: Connection[] | ((prev: Connection[]) => Connection[])) => void;
  setConnectingNodeId: (id: string | null) => void;
}

// Reviver function to convert date strings back to Date objects
const dateReviver = (key: string, value: unknown) => {
  if (['createdAt', 'updatedAt', 'timestamp'].includes(key) && typeof value === 'string') {
    const date = new Date(value);
    // Check if the parsed date is valid
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  return value;
};

const cleanState = (storageValue: unknown) => {
  if (!storageValue || typeof storageValue !== 'object' || storageValue === null || !('state' in storageValue)) {
    return { state: {}, version: 0 };
  }
  const { state, version } = storageValue as { state: object; version: number };
  const newState = state as { notes?: unknown };
  if (newState.notes && Array.isArray(newState.notes)) {
    newState.notes = (newState.notes as UserNote[]).map((n: UserNote) => ({
      ...n,
      createdAt: new Date(n.createdAt),
      updatedAt: new Date(n.updatedAt),
    }));
  }
  return { state: newState, version };
};

export const useCanvasStore = create<CanvasState>()(
  persist(
    (set) => ({
      notes: [],
      nodePositions: [],
      connections: [],
      connectingNodeId: null,
      addNote: async (note) => {
        set((state) => ({
          notes: [...state.notes, note],
        }));
        await new Promise(resolve => setTimeout(resolve, 10));
      },
      deleteNote: (id) =>
        set((state) => ({
          notes: state.notes.filter((n) => n.id !== id),
          nodePositions: state.nodePositions.filter((p) => p.id !== id),
          connections: state.connections.filter(
            (c) => c.fromId !== id && c.toId !== id
          ),
        })),
      updateNote: (id, updatedNote) =>
        set((state) => ({
          notes: state.notes.map((n) =>
            n.id === id ? { ...n, ...updatedNote, updatedAt: new Date() } : n
          ),
        })),
      setNodePositions: (positions) => set((state) => ({ nodePositions: typeof positions === 'function' ? positions(state.nodePositions) : positions })),
      setConnections: (connections) => set((state) => ({ connections: typeof connections === 'function' ? connections(state.connections) : connections })),
      setConnectingNodeId: (id) => set({ connectingNodeId: id }),
    }),
    {
      name: "canvas_planner_data",
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          try {
            const storedValue = JSON.parse(str);
            const cleanedValue = cleanState(storedValue);
            return JSON.stringify(cleanedValue);
          } catch {
            return str;
          }
        },
        setItem: (name, value) => {
          localStorage.setItem(name, value);
        },
        removeItem: (name) => localStorage.removeItem(name),
      }), {
        reviver: dateReviver,
      }),
      partialize: (state) => ({
        notes: state.notes,
        nodePositions: state.nodePositions,
        connections: state.connections,
      }),
    }
  )
);
