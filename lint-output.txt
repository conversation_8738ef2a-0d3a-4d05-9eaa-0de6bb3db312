
F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\3d-visualization\ThreeDVisualMap.tsx
  382:6  warning  React Hook useCallback has a missing dependency: 'createNodeMaterial'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ai-integration\LangFlowVisualDesigner.tsx
  225:6  warning  React Hook useCallback has a missing dependency: 'nodeTypes'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\conversation-planner\ModelSelector.tsx
  62:6  warning  React Hook useEffect has a missing dependency: 'fetchModels'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\conversation-planner\result-card\analysis-display\ContentFormatter.tsx
   3:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  31:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  37:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\conversation-planner\visualization\PerformanceOptimizer.tsx
    7:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  114:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  170:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  231:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\conversation-planner\visualization\ThemeProvider.tsx
   34:6   warning  React Hook useEffect has a missing dependency: 'availableThemes'. Either include it or remove the dependency array              react-hooks/exhaustive-deps
   66:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
   75:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
   85:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  125:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\FigmaCanvas.tsx
  87:6  warning  React Hook useEffect has missing dependencies: 'activeTool', 'backgroundColor', 'canvasSize.height', and 'canvasSize.width'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\FigmaToolbar.tsx
  266:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\compatibility\VisualCanvasIntegration.tsx
  137:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\selection\TransformControls.tsx
  367:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\storage\CanvasManagerDialog.tsx
  43:6  warning  React Hook useEffect has missing dependencies: 'loadSavedCanvases' and 'loadStorageUsage'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\styles\ColorPicker.tsx
  43:6  warning  React Hook useEffect has missing dependencies: 'drawColorPicker' and 'drawHueSlider'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\styles\GradientEditor.tsx
  38:6  warning  React Hook useEffect has a missing dependency: 'drawGradientPreview'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\figma-canvas\tools\EnhancedPenTool.tsx
  259:6  warning  React Hook useEffect has a missing dependency: 'renderCanvas'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\layout\Unified3ColumnLayout.tsx
   40:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components
  117:9   warning  The 'toggleColumn' function makes the dependencies of useEffect Hook (at line 172) change on every render. To fix this, wrap the definition of 'toggleColumn' in its own useCallback() Hook  react-hooks/exhaustive-deps
  153:13  error    Unexpected lexical declaration in case block                                                                                                                                                 no-case-declarations
  154:13  error    Unexpected lexical declaration in case block                                                                                                                                                 no-case-declarations
  402:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components
  451:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components
  501:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components
  518:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components
  525:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components                                                               react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\navigation\CanvasNavigationHelper.tsx
  112:61  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\nodes\AdvancedNodeSystem.tsx
  184:6   warning  React Hook useCallback has a missing dependency: 'calculateSmartPath'. Either include it or remove the dependency array         react-hooks/exhaustive-deps
  552:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  592:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  642:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  695:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  745:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  792:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\simulation\VisualScenarioDesigner.tsx
  30:31  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\badge.tsx
  40:17  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\button.tsx
  155:18  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\card.tsx
  153:3  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\form.tsx
  168:3  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\navigation-menu.tsx
  119:3  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\sidebar.tsx
  760:3  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\sonner.tsx
  29:19  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\textarea.tsx
  6:18  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\ui\toggle.tsx
  43:18  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\AdvancedControlPanel.tsx
  58:45  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  71:30  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\CanvasQuickAccess.tsx
  182:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\ChatSimulationManager.tsx
  89:0  error  Parsing error: Declaration or statement expected

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\ContextualHelpSystem.tsx
  426:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\DesignAnalysisBridge.tsx
  359:81  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\EnhancedTooltip.tsx
  125:6   warning  React Hook useEffect has a missing dependency: 'calculatePosition'. Either include it or remove the dependency array            react-hooks/exhaustive-deps
  272:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\FigmaIntegratedCanvas.tsx
  153:63  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\IntegratedCanvasContainer.tsx
  184:85  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\KeyboardShortcutsManager.tsx
  255:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  296:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\LivingDataCanvas.tsx
   621:11  warning  The 'onMouseMove' function makes the dependencies of useEffect Hook (at line 674) change on every render. To fix this, wrap the definition of 'onMouseMove' in its own useCallback() Hook                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                react-hooks/exhaustive-deps
   671:34  error    React Hook "useRef" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         react-hooks/rules-of-hooks
   672:5   error    React Hook "useEffect" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      react-hooks/rules-of-hooks
   909:5   error    React Hook "useCanvasEventHandlers" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         react-hooks/rules-of-hooks
   924:16  warning  The ref value 'mountRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mountRef.current' to a variable inside the effect, and use that variable in the cleanup function                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          react-hooks/exhaustive-deps
   946:6   warning  React Hook useEffect has missing dependencies: 'backgroundMeshRef', 'calculateForceDirectedLayout', 'draggedNodeInfo', 'enableChatAnalysisIntegration', 'getWorkerLayoutData', 'handleCancelConnection', 'handleNodeClick', 'handleShowClusterManager', 'handleShowSimulationManager', 'highlightOptions', 'hoveredNodeId', 'initialLayoutOptions', 'isCreatingConnection', 'isLayoutRunning', 'isWorkerAvailable', 'layoutOptions', 'nodes', 'particleFieldRef', 'resetNodePositions', 'selectedNodeId', 'selectedNodes.size', 'setHelpContext', 'showAdvancedControls', 'showContextMenu', 'toggleHelp', 'toggleShortcuts', 'visualConnections', and 'workerLayoutPending'. Either include them or remove the dependency array. You can also do a functional update 'setShowAdvancedControls(s => ...)' if you only need 'showAdvancedControls' in the 'setShowAdvancedControls' call  react-hooks/exhaustive-deps
   967:43  error    Do not access Object.prototype method 'hasOwnProperty' from target object                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                no-prototype-builtins
  1228:6   warning  React Hook useCallback has a missing dependency: 'nodes'. Either include it or remove the dependency array                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               react-hooks/exhaustive-deps
  1250:6   warning  React Hook useCallback has a missing dependency: 'nodes'. Either include it or remove the dependency array                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               react-hooks/exhaustive-deps
  1263:6   warning  React Hook useCallback has a missing dependency: 'nodes'. Either include it or remove the dependency array                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               react-hooks/exhaustive-deps
  1359:6   warning  React Hook useCallback has a missing dependency: 'handleShowSimulationManager'. Either include it or remove the dependency array                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\LivingDataCanvasSafe.tsx
  391:18  warning  The ref value 'mountRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mountRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
  402:6   warning  React Hook useEffect has missing dependencies: 'draggedNodeInfo', 'hoveredNodeId', 'initialLayoutOptions', 'resetNodePositions', and 'setIsLayoutRunning'. Either include them or remove the dependency array                                                    react-hooks/exhaustive-deps
  418:43  error    Do not access Object.prototype method 'hasOwnProperty' from target object                                                                                                                                                                                        no-prototype-builtins

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\LivingDataCanvasWithFallback.tsx
  5:11  error  An empty interface declaration allows any non-nullish value, including literals like `0` and `""`.
- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead  @typescript-eslint/no-empty-object-type

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\OnboardingTour.tsx
  151:6   warning  React Hook useEffect has a missing dependency: 'steps.length'. Either include it or remove the dependency array                       react-hooks/exhaustive-deps
  177:6   warning  React Hook useEffect has missing dependencies: 'handleNext' and 'handlePrevious'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  330:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components        react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\edgeUtils.ts
  2:1  error  Include a description after the "@ts-expect-error" directive to explain why the @ts-expect-error is necessary. The description must be 3 characters or longer  @typescript-eslint/ban-ts-comment

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\components\visual-analysis\useBackgroundEffects.ts
  36:18  warning  The ref value 'sceneRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'sceneRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\design-system\components\Button.tsx
  144:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\design-system\components\Card.tsx
  71:18  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type
  88:18  error  An interface declaring no members is equivalent to its supertype  @typescript-eslint/no-empty-object-type

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\design-system\theme-provider.tsx
  143:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  152:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components
  190:14  warning  Fast refresh only works when a file only exports components. Use a new file to share constants or functions between components  react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useAnalysisResults.ts
  24:32  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useAutoSave.ts
   64:6  warning  React Hook useEffect has missing dependencies: 'createStateHash' and 'performAutoSave'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  112:6  warning  React Hook useEffect has missing dependencies: 'createStateHash' and 'performAutoSave'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useCanvasDataBridge.ts
  35:24  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useChatAnalysisCanvas.ts
  144:6   warning  React Hook useCallback has a missing dependency: 'processDataOnMainThread'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  274:14  error    Unexpected any. Specify a different type                                                                                      @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useEnterpriseCanvas.ts
  69:6  warning  React Hook useCallback has a missing dependency: 'setLoading'. Either include it or remove the dependency array  react-hooks/exhaustive-deps
  81:6  warning  React Hook useCallback has a missing dependency: 'setLoading'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\hooks\useWebWorker.ts
  74:23  warning  The ref value 'pendingTasksRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'pendingTasksRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\AIOrchestrationManager.ts
  247:9   error  Unexpected lexical declaration in case block  no-case-declarations
  248:9   error  Unexpected lexical declaration in case block  no-case-declarations
  334:9   error  Unexpected lexical declaration in case block  no-case-declarations
  335:9   error  Unexpected lexical declaration in case block  no-case-declarations
  336:9   error  Unexpected lexical declaration in case block  no-case-declarations
  337:9   error  Unexpected lexical declaration in case block  no-case-declarations
  338:9   error  Unexpected lexical declaration in case block  no-case-declarations
  346:9   error  Unexpected lexical declaration in case block  no-case-declarations
  350:9   error  Unexpected lexical declaration in case block  no-case-declarations
  351:9   error  Unexpected lexical declaration in case block  no-case-declarations
  352:9   error  Unexpected lexical declaration in case block  no-case-declarations
  359:9   error  Unexpected lexical declaration in case block  no-case-declarations
  363:9   error  Unexpected lexical declaration in case block  no-case-declarations
  364:9   error  Unexpected lexical declaration in case block  no-case-declarations
  377:9   error  Unexpected lexical declaration in case block  no-case-declarations
  378:9   error  Unexpected lexical declaration in case block  no-case-declarations
  379:9   error  Unexpected lexical declaration in case block  no-case-declarations
  380:9   error  Unexpected lexical declaration in case block  no-case-declarations
  382:9   error  Unexpected lexical declaration in case block  no-case-declarations
  400:9   error  Unexpected lexical declaration in case block  no-case-declarations
  549:13  error  Unexpected lexical declaration in case block  no-case-declarations

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\aiCanvasService.ts
  63:47  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\api\canvasStateApi.ts
  167:5  warning  Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars')

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\api\chatAnalysisApi.ts
  143:5  warning  Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars')

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\api\clusterApi.ts
  208:5  warning  Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars')

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\api\simulationApi.ts
  224:5  warning  Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars')

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\designIntegration\analysisToDesignConverter.ts
  463:45  error  Unnecessary escape character: \*  no-useless-escape
  464:60  error  Unnecessary escape character: \*  no-useless-escape
  469:45  error  Unnecessary escape character: \*  no-useless-escape
  470:60  error  Unnecessary escape character: \*  no-useless-escape

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\export\professionalExportService.ts
  628:21  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\offline\localAIService.ts
  197:37  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\offline\offlineServiceManager.ts
  95:42  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\offline\offlineStorageService.ts
   98:84  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  398:38  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\openRouter\parsers\conversationSectionsParser.ts
   88:45  error  Unnecessary escape character: \.  no-useless-escape
   88:47  error  Unnecessary escape character: \:  no-useless-escape
   88:49  error  Unnecessary escape character: \)  no-useless-escape
   97:45  error  Unnecessary escape character: \.  no-useless-escape
   97:47  error  Unnecessary escape character: \:  no-useless-escape
   97:49  error  Unnecessary escape character: \)  no-useless-escape
  117:45  error  Unnecessary escape character: \.  no-useless-escape
  117:47  error  Unnecessary escape character: \:  no-useless-escape
  117:49  error  Unnecessary escape character: \)  no-useless-escape
  136:50  error  Unnecessary escape character: \.  no-useless-escape
  136:52  error  Unnecessary escape character: \:  no-useless-escape
  136:54  error  Unnecessary escape character: \)  no-useless-escape
  159:59  error  Unnecessary escape character: \.  no-useless-escape
  159:61  error  Unnecessary escape character: \:  no-useless-escape
  159:63  error  Unnecessary escape character: \)  no-useless-escape
  159:90  error  Unnecessary escape character: \.  no-useless-escape
  159:92  error  Unnecessary escape character: \:  no-useless-escape
  159:94  error  Unnecessary escape character: \)  no-useless-escape

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\openRouter\prompts\characterPrompt.ts
  9:44   error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  9:140  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\performance\canvasOptimization.ts
  304:42  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\services\performance\performanceProfiler.ts
  210:38  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\stores\base\createStandardStore.ts
  148:29  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  148:39  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  148:49  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  194:8   error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  195:8   error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  196:8   error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  237:56  error  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\utils\bundleOptimization.ts
   52:54  error  Unexpected any. Specify a different type      @typescript-eslint/no-explicit-any
  115:11  error  Unexpected lexical declaration in case block  no-case-declarations

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\utils\performance.ts
  181:3   warning  React Hook useEffect contains a call to 'setRenderTime'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [] as a second argument to the useEffect Hook  react-hooks/exhaustive-deps
  361:10  warning  React Hook useCallback has a missing dependency: 'handler'. Either include it or remove the dependency array                                                                                             react-hooks/exhaustive-deps

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\utils\test-utils.tsx
   6:7  warning  Fast refresh only works when a file only exports components. Move your component(s) to a separate file  react-refresh/only-export-components
  14:1  warning  This rule can't verify that `export *` only exports components                                          react-refresh/only-export-components

F:\AI App Builds\My_Github_Projects\chat-craft-trainer-pro-80\src\utils\throttleFn.ts
  20:7  error  Unexpected aliasing of 'this' to local variable  @typescript-eslint/no-this-alias

Γ£û 168 problems (86 errors, 82 warnings)
  0 errors and 4 warnings potentially fixable with the `--fix` option.

