/**
 * Error Reporter Component
 * 
 * Displays current errors from the error store and provides
 * debugging information for development.
 */

import React, { useState } from 'react';
import { useErrorStore } from '@/stores/useErrorStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Bug, 
  Clock,
  AlertCircle,
  XCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorReporterProps {
  showInDevelopment?: boolean;
  maxErrors?: number;
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
}

export const ErrorReporter: React.FC<ErrorReporterProps> = ({
  showInDevelopment = true,
  maxErrors = 5,
  position = 'bottom-right',
}) => {
  const { errors, removeError, clearErrors } = useErrorStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedError, setSelectedError] = useState<string | null>(null);

  // Only show in development if specified
  if (!showInDevelopment && process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Don't render if no errors
  if (errors.length === 0) {
    return null;
  }

  const recentErrors = errors.slice(-maxErrors);
  const criticalErrors = errors.filter(e => e.severity === 'critical');
  const highErrors = errors.filter(e => e.severity === 'high');

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Bug className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-left': 'bottom-4 left-4',
  };

  return (
    <div className={cn(
      'fixed z-50 max-w-md',
      positionClasses[position]
    )}>
      <Card className="shadow-lg border-2 border-destructive/20">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-destructive" />
              <span>Errors ({errors.length})</span>
              {criticalErrors.length > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {criticalErrors.length} Critical
                </Badge>
              )}
              {highErrors.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {highErrors.length} High
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0"
              >
                {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearErrors}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        
        {isExpanded && (
          <CardContent className="pt-0">
            <ScrollArea className="h-64">
              <div className="space-y-2">
                {recentErrors.map((error) => (
                  <div
                    key={error.id}
                    className={cn(
                      'p-2 rounded border text-xs cursor-pointer transition-colors',
                      getSeverityColor(error.severity),
                      selectedError === error.id && 'ring-2 ring-primary'
                    )}
                    onClick={() => setSelectedError(selectedError === error.id ? null : error.id)}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex items-start gap-2 flex-1 min-w-0">
                        {getSeverityIcon(error.severity)}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">
                            {error.message}
                          </div>
                          <div className="text-xs opacity-70 flex items-center gap-2 mt-1">
                            <Clock className="h-3 w-3" />
                            {new Date(error.timestamp).toLocaleTimeString()}
                            {error.context && (
                              <span className="truncate">• {error.context}</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeError(error.id);
                        }}
                        className="h-4 w-4 p-0 opacity-50 hover:opacity-100"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    {selectedError === error.id && (
                      <div className="mt-2 pt-2 border-t border-current/20">
                        <div className="space-y-1 text-xs">
                          <div><strong>Category:</strong> {error.category}</div>
                          <div><strong>Severity:</strong> {error.severity}</div>
                          {error.stack && (
                            <details className="mt-2">
                              <summary className="cursor-pointer font-medium">Stack Trace</summary>
                              <pre className="mt-1 text-xs whitespace-pre-wrap font-mono bg-black/10 p-2 rounded">
                                {error.stack}
                              </pre>
                            </details>
                          )}
                          {error.metadata && Object.keys(error.metadata).length > 0 && (
                            <details className="mt-2">
                              <summary className="cursor-pointer font-medium">Metadata</summary>
                              <pre className="mt-1 text-xs whitespace-pre-wrap font-mono bg-black/10 p-2 rounded">
                                {JSON.stringify(error.metadata, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            {errors.length > maxErrors && (
              <div className="mt-2 text-xs text-muted-foreground text-center">
                Showing {maxErrors} of {errors.length} errors
              </div>
            )}
            
            <div className="mt-2 flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearErrors}
                className="flex-1 text-xs"
              >
                Clear All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const errorData = {
                    errors: errors,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                  };
                  console.log('📋 Error Report:', errorData);
                  navigator.clipboard?.writeText(JSON.stringify(errorData, null, 2));
                }}
                className="flex-1 text-xs"
              >
                Copy Report
              </Button>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
};

export default ErrorReporter;
