// Enforce minimum test coverage (95%)
const { execSync } = require('child_process');

try {
  const output = execSync('npm run test:coverage -- --json-summary --coverageReporters=json-summary', { encoding: 'utf8' });
  const summary = require('../../coverage/coverage-summary.json');
  const thresholds = { lines: 95, statements: 95, functions: 95, branches: 95 };
  let failed = false;
  for (const key in thresholds) {
    const pct = summary.total[key].pct;
    if (pct < thresholds[key]) {
      console.error(`Coverage for ${key} is ${pct}%, below required ${thresholds[key]}%`);
      failed = true;
    }
  }
  if (failed) process.exit(1);
  console.log('Test coverage meets requirements.');
} catch (e) {
  console.error('Error running coverage check:', e.message);
  process.exit(1);
}
