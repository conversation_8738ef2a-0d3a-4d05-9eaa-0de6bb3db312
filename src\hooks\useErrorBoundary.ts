/**
 * Error Boundary Hook
 * 
 * Provides utilities for working with error boundaries and error handling.
 */

import { useCallback, useEffect } from 'react';
import { useErrorStore } from '@/stores/useErrorStore';

interface UseErrorBoundaryOptions {
  context?: string;
  onError?: (error: Error) => void;
  reportToStore?: boolean;
}

export const useErrorBoundary = (options: UseErrorBoundaryOptions = {}) => {
  const { context = 'Component', onError, reportToStore = true } = options;
  const { addError } = useErrorStore();

  // Function to manually trigger error boundary
  const throwError = useCallback((error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    console.error(`🚨 Manual error thrown in ${context}:`, errorObj);
    throw errorObj;
  }, [context]);

  // Function to handle errors gracefully without throwing
  const handleError = useCallback((error: Error | string, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    console.error(`🚨 Error handled in ${context}:`, errorObj);
    
    if (reportToStore) {
      addError({
        message: errorObj.message,
        severity,
        category: 'system',
        context,
        stack: errorObj.stack,
        metadata: {
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        },
      });
    }

    if (onError) {
      try {
        onError(errorObj);
      } catch (handlerError) {
        console.error('🚨 Error in custom error handler:', handlerError);
      }
    }
  }, [context, onError, reportToStore, addError]);

  // Function to wrap async operations with error handling
  const wrapAsync = useCallback(<T>(
    asyncFn: () => Promise<T>,
    errorMessage?: string
  ): Promise<T | null> => {
    return asyncFn().catch((error) => {
      const message = errorMessage || `Async operation failed in ${context}`;
      handleError(error instanceof Error ? error : new Error(message), 'medium');
      return null;
    });
  }, [context, handleError]);

  // Set up global error listeners for this component
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      console.log(`🚨 Global error captured in ${context}:`, event.error);
      handleError(event.error || new Error(event.message), 'high');
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.log(`🚨 Unhandled promise rejection in ${context}:`, event.reason);
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      handleError(error, 'high');
    };

    // Only add listeners if we want to report to store
    if (reportToStore) {
      window.addEventListener('error', handleGlobalError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);

      return () => {
        window.removeEventListener('error', handleGlobalError);
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }
  }, [context, handleError, reportToStore]);

  return {
    throwError,
    handleError,
    wrapAsync,
  };
};

// Hook for component-level error tracking
export const useComponentErrorTracking = (componentName: string) => {
  const { handleError } = useErrorBoundary({ context: componentName });

  useEffect(() => {
    console.log(`🛡️ Error tracking enabled for component: ${componentName}`);
    
    return () => {
      console.log(`🛡️ Error tracking disabled for component: ${componentName}`);
    };
  }, [componentName]);

  // Function to track component lifecycle errors
  const trackLifecycleError = useCallback((phase: 'mount' | 'update' | 'unmount', error: Error) => {
    console.error(`🚨 ${componentName} ${phase} error:`, error);
    handleError(error, 'medium');
  }, [componentName, handleError]);

  // Function to track render errors
  const trackRenderError = useCallback((error: Error) => {
    console.error(`🚨 ${componentName} render error:`, error);
    handleError(error, 'high');
  }, [componentName, handleError]);

  return {
    trackLifecycleError,
    trackRenderError,
    handleError,
  };
};

// Hook for async operation error handling
export const useAsyncErrorHandler = (context: string = 'AsyncOperation') => {
  const { wrapAsync, handleError } = useErrorBoundary({ context });

  const executeWithErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    options: {
      errorMessage?: string;
      onSuccess?: (result: T) => void;
      onError?: (error: Error) => void;
      retries?: number;
    } = {}
  ): Promise<T | null> => {
    const { errorMessage, onSuccess, onError, retries = 0 } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        console.log(`🔄 ${context} attempt ${attempt + 1}/${retries + 1}`);
        const result = await operation();
        
        if (onSuccess) {
          onSuccess(result);
        }
        
        console.log(`✅ ${context} succeeded on attempt ${attempt + 1}`);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`❌ ${context} failed on attempt ${attempt + 1}:`, lastError);
        
        if (attempt === retries) {
          // Final attempt failed
          const finalError = new Error(errorMessage || `${context} failed after ${retries + 1} attempts`);
          finalError.cause = lastError;
          
          handleError(finalError, 'high');
          
          if (onError) {
            onError(finalError);
          }
          
          return null;
        }
        
        // Wait before retry
        if (attempt < retries) {
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000); // Exponential backoff
          console.log(`⏳ ${context} retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    return null;
  }, [context, handleError]);

  return {
    executeWithErrorHandling,
    wrapAsync,
    handleError,
  };
};
