
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, RotateCcw } from "lucide-react";
import { useAnalysisStore } from "@/stores/useAnalysisStore";
import { useAnalysisResults } from "@/hooks/useAnalysisResults";

interface PlannerHeaderProps {
  className?: string;
}

export const PlannerHeader: React.FC<PlannerHeaderProps> = () => {
  const hasResults = useAnalysisStore((state) => state.analysisResults.length > 0);
  const { handleNewQuery } = useAnalysisResults();

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 mb-8 p-6 bg-gradient-to-r from-primary/5 via-primary/10 to-purple-600/5 rounded-2xl border border-border/50">
      <div className="flex items-start sm:items-center gap-4">
        <div className="flex-shrink-0 p-3 bg-primary/10 rounded-xl">
          <MessageSquare className="h-8 w-8 text-primary" />
        </div>        <div className="space-y-1">
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground">
            AI Conversation Analysis Suite
          </h2>
          <p className="text-base text-muted-foreground max-w-2xl">
            Enterprise-grade conversation analysis with multiple perspectives and AI-powered insights
          </p>
        </div>
      </div>
      
      {hasResults && (
        <div className="flex-shrink-0">
          <Button 
            onClick={handleNewQuery} 
            variant="outline" 
            size="lg"
            className="border-2 border-primary/20 hover:border-primary/40 bg-background/50 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200"
          >
            <RotateCcw className="mr-2 h-4 w-4" />
            New Analysis
          </Button>
        </div>
      )}
    </div>
  );
};
