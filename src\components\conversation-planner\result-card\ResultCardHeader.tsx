
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, MessageSquare, User } from "lucide-react";
import { AnalysisResult } from "@/types/conversation";
import { StarRating } from "./StarRating";
import { Button } from "@/components/ui/button";

interface ResultCardHeaderProps {
  result: AnalysisResult;
  onRate: (rating: number) => void;
  onEnterChatMode?: () => void;
}

export const ResultCardHeader: React.FC<ResultCardHeaderProps> = ({
  result,
  onRate,
  onEnterChatMode,
}) => {
  const getCardTitle = () => {
    if (result.analysisType === 'character' && result.characterPersona) {
      return `A Response from ${result.characterPersona.name}:`;
    }
    return `"${result.question}"`;
  };

  const getCardDescription = () => {
    const badges = [];

    if (result.analysisType === 'character' && result.characterPersona) {
      badges.push(
        <Badge key="character" variant="outline" className="font-medium flex items-center bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100">
          <User className="h-3 w-3 mr-1" />
          {result.characterPersona.name}
        </Badge>
      );
    } else {
      badges.push(
        <Badge key="style" variant="outline" className="font-medium bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
          {result.style}
        </Badge>
      );
    }

    badges.push(
      <Badge key="model" variant="outline" className="font-medium bg-green-50 text-green-700 border-green-200 hover:bg-green-100">
        {result.model}
      </Badge>
    );

    if (result.analysisType) {
      const typeLabel = result.analysisType === 'multiple' ? 'Scenarios' :
                    result.analysisType === 'deep' ? 'Deep Dive' : 'Simulation';
      badges.push(
        <Badge key="type" variant="outline" className="font-medium bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100">
          {typeLabel}
        </Badge>
      );
    }

    const timestamp = typeof result.timestamp === 'string'
      ? new Date(result.timestamp)
      : result.timestamp;

    return (
      <div className="flex items-center gap-3 flex-wrap">
        {badges}
        <span className="text-sm text-slate-500 font-medium">
          {timestamp.toLocaleTimeString()}
        </span>
        {result.rating && (
          <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-50 text-yellow-700 border-yellow-200">
            <Star className="h-3 w-3 fill-current" />
            {result.rating}
          </Badge>
        )}
      </div>
    );
  };

  return (
    <CardHeader className="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-slate-200">
      <CardTitle className="text-xl flex items-center justify-between">
        <span className="font-semibold text-slate-800 leading-tight">{getCardTitle()}</span>
        <div className="flex items-center gap-3">
          {onEnterChatMode && result.analysisType === "character" && (
            <Button
              variant="outline"
              size="sm"
              onClick={onEnterChatMode}
              className="flex items-center bg-white hover:bg-slate-50 border-slate-300 text-slate-700 shadow-sm"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Chat Mode
            </Button>
          )}
          <StarRating rating={result.rating} onRate={onRate} />
        </div>
      </CardTitle>
      <CardDescription className="mt-3">
        {getCardDescription()}
      </CardDescription>
    </CardHeader>
  );
};
