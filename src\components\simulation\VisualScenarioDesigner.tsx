import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  Plus, 
  MessageSquare, 
  User, 
  Bot,
  GitBranch,
  Settings,
  Save,
  Download,
  Upload,
  Trash2,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Brain
} from 'lucide-react';
import type { CharacterPersona } from '@/types/conversation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

export interface ScenarioNode {
  id: string;
  type: 'start' | 'message' | 'response' | 'decision' | 'end';
  position: { x: number; y: number };
  data: {
    content: string;
    persona?: CharacterPersona;
    isUserMessage?: boolean;
    conditions?: string[];
    metadata?: Record<string, unknown>;
  };
  connections: string[]; // IDs of connected nodes
}

export interface ScenarioConnection {
  id: string;
  sourceId: string;
  targetId: string;
  label?: string;
  condition?: string;
  probability?: number;
}

export interface ChatScenario {
  id: string;
  name: string;
  description: string;
  nodes: ScenarioNode[];
  connections: ScenarioConnection[];
  personas: CharacterPersona[];
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: string;
    tags: string[];
  };
}

interface VisualScenarioDesignerProps {
  scenario?: ChatScenario;
  personas: CharacterPersona[];
  onSave?: (scenario: ChatScenario) => void;
  onSimulate?: (scenario: ChatScenario) => void;
  onExport?: (scenario: ChatScenario) => void;
  className?: string;
}

export const VisualScenarioDesigner: React.FC<VisualScenarioDesignerProps> = ({
  scenario,
  personas,
  onSave,
  onSimulate,
  onExport,
  className,
}) => {
  const [currentScenario, setCurrentScenario] = useState<ChatScenario>(
    scenario || {
      id: `scenario_${Date.now()}`,
      name: 'New Scenario',
      description: '',
      nodes: [],
      connections: [],
      personas: [],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0.0',
        tags: [],
      },
    }
  );

  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const [draggedNode, setDraggedNode] = useState<ScenarioNode | null>(null);
  const [showNodeEditor, setShowNodeEditor] = useState(false);

  const canvasRef = useRef<HTMLDivElement>(null);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);

  // Node types configuration
  const nodeTypes = [
    { type: 'start', label: 'Start', icon: Play, color: 'bg-green-100 border-green-300' },
    { type: 'message', label: 'Message', icon: MessageSquare, color: 'bg-blue-100 border-blue-300' },
    { type: 'response', label: 'Response', icon: Bot, color: 'bg-purple-100 border-purple-300' },
    { type: 'decision', label: 'Decision', icon: GitBranch, color: 'bg-yellow-100 border-yellow-300' },
    { type: 'end', label: 'End', icon: Settings, color: 'bg-red-100 border-red-300' },
  ];

  // Add new node
  const addNode = useCallback((type: ScenarioNode['type'], position: { x: number; y: number }) => {
    const newNode: ScenarioNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      position,
      data: {
        content: type === 'start' ? 'Conversation Start' : 
                type === 'end' ? 'Conversation End' : 
                'Enter message content...',
        isUserMessage: type === 'message',
      },
      connections: [],
    };

    setCurrentScenario(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));

    setSelectedNodeId(newNode.id);
    setShowNodeEditor(true);
  }, []);

  // Update node
  const updateNode = useCallback((nodeId: string, updates: Partial<ScenarioNode>) => {
    setCurrentScenario(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      ),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));
  }, []);

  // Delete node
  const deleteNode = useCallback((nodeId: string) => {
    setCurrentScenario(prev => ({
      ...prev,
      nodes: prev.nodes.filter(node => node.id !== nodeId),
      connections: prev.connections.filter(conn => 
        conn.sourceId !== nodeId && conn.targetId !== nodeId
      ),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));
    setSelectedNodeId(null);
  }, []);

  // Start connection
  const startConnection = useCallback((nodeId: string) => {
    setIsConnecting(true);
    setConnectionSource(nodeId);
  }, []);

  // Complete connection
  const completeConnection = useCallback((targetId: string) => {
    if (!connectionSource || connectionSource === targetId) {
      setIsConnecting(false);
      setConnectionSource(null);
      return;
    }

    const newConnection: ScenarioConnection = {
      id: `conn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      sourceId: connectionSource,
      targetId,
      label: 'Next',
    };

    setCurrentScenario(prev => ({
      ...prev,
      connections: [...prev.connections, newConnection],
      nodes: prev.nodes.map(node => 
        node.id === connectionSource 
          ? { ...node, connections: [...node.connections, targetId] }
          : node
      ),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));

    setIsConnecting(false);
    setConnectionSource(null);
  }, [connectionSource]);

  // Handle node drag
  const handleNodeDrag = useCallback((nodeId: string, newPosition: { x: number; y: number }) => {
    updateNode(nodeId, { position: newPosition });
  }, [updateNode]);

  // Handle canvas click
  const handleCanvasClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === canvasRef.current) {
      setSelectedNodeId(null);
      if (isConnecting) {
        setIsConnecting(false);
        setConnectionSource(null);
      }
    }
  }, [isConnecting]);

  // Save scenario
  const handleSave = useCallback(() => {
    onSave?.(currentScenario);
  }, [currentScenario, onSave]);

  // Simulate scenario
  const handleSimulate = useCallback(() => {
    onSimulate?.(currentScenario);
  }, [currentScenario, onSimulate]);

  // Export scenario
  const handleExport = useCallback(() => {
    onExport?.(currentScenario);
  }, [currentScenario, onExport]);

  // Get selected node
  const selectedNode = selectedNodeId 
    ? currentScenario.nodes.find(node => node.id === selectedNodeId)
    : null;

  // Render node
  const renderNode = (node: ScenarioNode) => {
    const nodeConfig = nodeTypes.find(nt => nt.type === node.type);
    if (!nodeConfig) return null;

    const isSelected = selectedNodeId === node.id;
    const isConnectionTarget = isConnecting && connectionSource !== node.id;

    return (
      <div
        key={node.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
        } ${isConnectionTarget ? 'ring-2 ring-green-400 ring-offset-1' : ''}`}
        style={{
          left: node.position.x,
          top: node.position.y,
          transform: `scale(${zoom})`,
          transformOrigin: 'top left',
        }}
        onClick={(e) => {
          e.stopPropagation();
          if (isConnecting) {
            completeConnection(node.id);
          } else {
            setSelectedNodeId(node.id);
          }
        }}
        onDoubleClick={() => setShowNodeEditor(true)}
      >
        <Card className={`w-48 ${nodeConfig.color} shadow-lg hover:shadow-xl transition-shadow`}>
          <CardContent className="p-3">
            <div className="flex items-center gap-2 mb-2">
              <nodeConfig.icon className="h-4 w-4" />
              <Badge variant="outline" className="text-xs">
                {nodeConfig.label}
              </Badge>
              {node.data.persona && (
                <Badge variant="secondary" className="text-xs">
                  {node.data.persona.name}
                </Badge>
              )}
            </div>
            
            <div className="text-sm font-medium mb-1 truncate">
              {node.data.content}
            </div>
            
            {node.data.isUserMessage !== undefined && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {node.data.isUserMessage ? <User className="h-3 w-3" /> : <Bot className="h-3 w-3" />}
                {node.data.isUserMessage ? 'User' : 'Assistant'}
              </div>
            )}

            <div className="flex justify-between items-center mt-2">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    startConnection(node.id);
                  }}
                >
                  <Link className="h-3 w-3" />
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteNode(node.id);
                  }}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>

              <div className="text-xs text-muted-foreground">
                {node.connections.length} connections
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render connection
  const renderConnection = (connection: ScenarioConnection) => {
    const sourceNode = currentScenario.nodes.find(n => n.id === connection.sourceId);
    const targetNode = currentScenario.nodes.find(n => n.id === connection.targetId);
    
    if (!sourceNode || !targetNode) return null;

    const startX = sourceNode.position.x + 96; // Half of node width
    const startY = sourceNode.position.y + 40; // Approximate node height
    const endX = targetNode.position.x + 96;
    const endY = targetNode.position.y + 20;

    const pathString = `M ${startX} ${startY} Q ${(startX + endX) / 2} ${startY - 50} ${endX} ${endY}`;

    return (
      <g key={connection.id}>
        <path
          d={pathString}
          stroke="#6366f1"
          strokeWidth="2"
          fill="none"
          markerEnd="url(#arrowhead)"
        />
        {connection.label && (
          <text
            x={(startX + endX) / 2}
            y={(startY + endY) / 2 - 25}
            textAnchor="middle"
            fontSize="12"
            fill="#6366f1"
            className="pointer-events-none"
          >
            {connection.label}
          </text>
        )}
      </g>
    );
  };

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Toolbar */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <Input
                  value={currentScenario.name}
                  onChange={(e) => setCurrentScenario(prev => ({
                    ...prev,
                    name: e.target.value,
                    metadata: { ...prev.metadata, updatedAt: new Date() },
                  }))}
                  className="font-medium"
                  placeholder="Scenario name..."
                />
              </div>

              <div className="flex items-center gap-2">
                {nodeTypes.map(nodeType => (
                  <Button
                    key={nodeType.type}
                    size="sm"
                    variant="outline"
                    onClick={() => addNode(nodeType.type as 'start' | 'message' | 'response' | 'decision' | 'end', { x: 100, y: 100 })}
                    className="flex items-center gap-2"
                  >
                    <nodeType.icon className="h-4 w-4" />
                    {nodeType.label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>

              <Button onClick={handleSimulate} disabled={currentScenario.nodes.length === 0}>
                <Play className="h-4 w-4 mr-2" />
                Simulate
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden bg-gray-50">
          <div
            ref={canvasRef}
            className="w-full h-full relative cursor-default"
            onClick={handleCanvasClick}
          >
            {/* SVG for connections */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon points="0 0, 10 3.5, 0 7" fill="#6366f1" />
                </marker>
              </defs>
              {currentScenario.connections.map(renderConnection)}
            </svg>

            {/* Nodes */}
            {currentScenario.nodes.map(renderNode)}

            {/* Empty state */}
            {currentScenario.nodes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Start Building Your Scenario</h3>
                  <p className="text-sm mb-4">Add nodes to create conversation flows and decision points</p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => addNode('start', { x: 200, y: 200 })}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Start Node
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Properties Panel */}
        {selectedNode && (
          <Card className="w-80 rounded-none border-y-0 border-r-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Node Properties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Content</label>
                <Textarea
                  value={selectedNode.data.content}
                  onChange={(e) => updateNode(selectedNode.id, {
                    data: { ...selectedNode.data, content: e.target.value }
                  })}
                  placeholder="Enter message content..."
                  className="mt-1"
                />
              </div>

              {(selectedNode.type === 'message' || selectedNode.type === 'response') && (
                <div>
                  <label className="text-sm font-medium">Persona</label>
                  <select
                    value={selectedNode.data.persona?.id || ''}
                    onChange={(e) => {
                      const persona = personas.find(p => p.id === e.target.value);
                      updateNode(selectedNode.id, {
                        data: { ...selectedNode.data, persona }
                      });
                    }}
                    className="w-full mt-1 p-2 border rounded-md"
                  >
                    <option value="">Select persona...</option>
                    {personas.map(persona => (
                      <option key={persona.id} value={persona.id}>
                        {persona.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {selectedNode.type === 'message' && (
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isUserMessage"
                    checked={selectedNode.data.isUserMessage || false}
                    onChange={(e) => updateNode(selectedNode.id, {
                      data: { ...selectedNode.data, isUserMessage: e.target.checked }
                    })}
                  />
                  <label htmlFor="isUserMessage" className="text-sm">User message</label>
                </div>
              )}

              <div className="pt-4 border-t">
                <div className="text-sm font-medium mb-2">Node Info</div>
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div>ID: {selectedNode.id}</div>
                  <div>Type: {selectedNode.type}</div>
                  <div>Connections: {selectedNode.connections.length}</div>
                  <div>Position: ({selectedNode.position.x}, {selectedNode.position.y})</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Status Bar */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Nodes: {currentScenario.nodes.length}</span>
              <span>Connections: {currentScenario.connections.length}</span>
              <span>Personas: {currentScenario.personas.length}</span>
            </div>
            
            <div className="flex items-center gap-4">
              <span>Zoom: {(zoom * 100).toFixed(0)}%</span>
              {isConnecting && (
                <Badge variant="secondary" className="text-xs">
                  <Link className="h-3 w-3 mr-1" />
                  Connecting...
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
