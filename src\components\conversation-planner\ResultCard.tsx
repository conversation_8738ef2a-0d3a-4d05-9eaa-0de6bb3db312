import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { AnalysisResult } from "@/types/conversation";
import { useToast } from "@/hooks/use-toast";
import { ResultCardHeader } from "./result-card/ResultCardHeader";
import { ResultCardContent } from "./result-card/ResultCardContent";
import { ResultCardFooter } from "./result-card/ResultCardFooter";

interface ResultCardProps {
  result: AnalysisResult;
  /** Save the entire result (main Save button) */
  onSaveResult: (result: AnalysisResult) => void;
  /** Delete the result by ID */
  onDelete: (id: string) => void;
  /** Rate the result by ID */
  onRate: (id: string, rating: number) => void;
  /** Refine the result by ID and prompt */
  onRefine: (id: string, prompt: string) => Promise<void>;
  /** Trigger a follow-up question */
  onFollowUpQuestion: (question: string) => void;
  /** Select an answer (for multiple answers) */
  onAnswerSelect?: (answer: string, answerNumber: number) => void;
  /** Add a note to a specific answer */
  onAddNote?: (questionId: string, answerId: string, answerContent: string) => void;
  /** Enter chat mode for a result/answer */
  onEnterChatMode?: (result: AnalysisResult, focusedAnswer?: string) => void;
  /** Is the result currently being refined? */
  isRefining?: boolean;
  /** Save only the selected items (for multi-select) */
  onSaveSelectedItems?: (selectedItems: string[], result: AnalysisResult) => void;
}

export const ResultCard: React.FC<ResultCardProps> = ({
  result,
  onSaveResult,
  onDelete,
  onRate,
  onRefine,
  onFollowUpQuestion,
  onAnswerSelect,
  onAddNote,
  onEnterChatMode,
  isRefining = false,
  onSaveSelectedItems,
}) => {
  const [showRefinement, setShowRefinement] = useState(false);
  const [refinementPrompt, setRefinementPrompt] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const { toast } = useToast();

  const handleToggleItemSelect = (item: string) => {
    setSelectedItems((prev) =>
      prev.includes(item)
        ? prev.filter((i) => i !== item)
        : [...prev, item]
    );
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result.analysis);
      toast({
        title: "Copied to clipboard",
        description: "The analysis has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Unable to copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const handleRefine = async () => {
    if (!refinementPrompt.trim()) {
      toast({
        title: "Refinement prompt required",
        description: "Please enter a refinement instruction.",
        variant: "destructive",
      });
      return;
    }

    await onRefine(result.id, refinementPrompt);
    setRefinementPrompt("");
    setShowRefinement(false);
  };

  const handleRate = (rating: number) => {
    onRate(result.id, rating);
  };

  const handleSaveSelectedItems = () => {
    if (selectedItems.length === 0) {
      toast({
        title: "No items selected",
        description: "Please select at least one item to save.",
        variant: "destructive",
      });
      return;
    }
    if (onSaveSelectedItems) {
      onSaveSelectedItems(selectedItems, result);
      setSelectedItems([]);
      return;
    }
    // fallback: legacy behavior
    const newAnalysisContent = selectedItems.join("\n\n");
    const newResult: AnalysisResult = {
      ...result,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      question: `Selection from: ${result.question}`,
      analysis: newAnalysisContent,
      timestamp: new Date(),
      parsedOutput: undefined,
      refinements: [],
      rating: undefined,
    };
    onSaveResult(newResult);
    setSelectedItems([]);
  };

  const handleItemSelectForChat = (item: string) => {
    if (onEnterChatMode) {
      onEnterChatMode(result, item);
    } else if (onFollowUpQuestion) {
      onFollowUpQuestion(item);
    }
  };

  const handleAnswerSelectEvent = (answer: string, answerNumber: number) => {
    if (onEnterChatMode) {
      onEnterChatMode(result, answer);
    } else if (onAnswerSelect) {
      onAnswerSelect(answer, answerNumber);
    }
  };

  const handleEnterChatMode = () => {
    if (onEnterChatMode) {
      onEnterChatMode(result);
    }
  };

  return (
    <Card className="mb-8 shadow-xl border border-slate-200 rounded-xl overflow-hidden bg-white hover:shadow-2xl transition-shadow duration-300">
      <ResultCardHeader
        result={result}
        onRate={handleRate}
        onEnterChatMode={handleEnterChatMode}
      />

      <ResultCardContent
        result={result}
        showRefinement={showRefinement}
        onToggleRefinement={() => setShowRefinement(!showRefinement)}
        refinementPrompt={refinementPrompt}
        onRefinementPromptChange={setRefinementPrompt}
        onRefine={handleRefine}
        isRefining={isRefining}
        onFollowUpQuestion={onFollowUpQuestion}
        onAnswerSelect={handleAnswerSelectEvent}
        onItemSelectForChat={handleItemSelectForChat}
        selectedItems={selectedItems}
        onItemToggleSelect={handleToggleItemSelect}
      />

      <ResultCardFooter
        result={result}
        onSave={() => onSaveResult(result)}
        onCopy={handleCopy}
        onToggleRefinement={() => setShowRefinement(!showRefinement)}
        onDelete={() => onDelete(result.id)}
        onAddNote={onAddNote}
        onSaveSelected={handleSaveSelectedItems}
        selectedItemsCount={selectedItems.length}
      />
    </Card>
  );
};
