import React from 'react';
import { ParsedEmotionalAnglesAnalysis } from '@/services/openRouter/types/parsedOutput';

interface EmotionalAnglesDisplayProps {
  analysis: ParsedEmotionalAnglesAnalysis;
}

export const EmotionalAnglesDisplay: React.FC<EmotionalAnglesDisplayProps> = ({ analysis }) => {
  return (
    <div>
      <h3>Emotional Angles Analysis</h3>
      {analysis.summary && <p><strong>Summary:</strong> {analysis.summary}</p>}
      {analysis.angles.map((angle, index) => (
        <div key={`angle-${index}`}>
          <h4>Emotion: {angle.emotion}</h4>
          <p>Analysis: {angle.analysis}</p>
          {angle.keywords && (
            <p>Keywords: {angle.keywords.join(', ')}</p>
          )}
        </div>
      ))}
      {analysis.warning && <p style={{ color: 'orange' }}>Warning: {analysis.warning}</p>}
    </div>
  );
};
