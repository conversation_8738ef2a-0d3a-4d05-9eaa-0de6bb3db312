import React from 'react';
import { ParsedSixThinkingHatsAnalysis } from '@/services/openRouter/types/parsedOutput';

interface SixThinkingHatsDisplayProps {
  analysis: ParsedSixThinkingHatsAnalysis;
}

export const SixThinkingHatsDisplay: React.FC<SixThinkingHatsDisplayProps> = ({ analysis }) => {
  return (
    <div>
      <h3>Six Thinking Hats Analysis</h3>
      {analysis.summary && <p><strong>Summary:</strong> {analysis.summary}</p>}
      {analysis.white_hat && <p><strong>White Hat (Facts):</strong> {analysis.white_hat}</p>}
      {analysis.red_hat && <p><strong>Red Hat (Emotions):</strong> {analysis.red_hat}</p>}
      {analysis.black_hat && <p><strong>Black Hat (Cautions):</strong> {analysis.black_hat}</p>}
      {analysis.yellow_hat && <p><strong>Yellow Hat (Benefits):</strong> {analysis.yellow_hat}</p>}
      {analysis.green_hat && <p><strong>Green Hat (Creativity):</strong> {analysis.green_hat}</p>}
      {analysis.blue_hat && <p><strong>Blue Hat (Process):</strong> {analysis.blue_hat}</p>}
      {analysis.warning && <p style={{ color: 'orange' }}>Warning: {analysis.warning}</p>}
    </div>
  );
};
