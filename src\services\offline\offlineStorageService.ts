/**
 * Offline Storage Service
 * 
 * Provides comprehensive offline data storage using IndexedDB with fallback to localStorage.
 * Handles analysis results, user data, application state, and cached resources.
 */

import { AnalysisResult } from '@/types/conversation';

interface StorageConfig {
  dbName: string;
  version: number;
  stores: string[];
  maxStorageSize: number; // in MB
  enableCompression: boolean;
}

interface StoredItem<T = unknown> {
  id: string;
  data: T;
  timestamp: number;
  version: number;
  metadata?: Record<string, unknown>;
}

export class OfflineStorageService {
  private config: StorageConfig;
  private db: IDBDatabase | null = null;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;

  constructor(config: Partial<StorageConfig> = {}) {
    this.config = {
      dbName: 'ChatCraftTrainerPro',
      version: 1,
      stores: [
        'analysisResults',
        'userNotes',
        'canvasStates',
        'designElements',
        'presentations',
        'settings',
        'cache'
      ],
      maxStorageSize: 100, // 100MB
      enableCompression: false, // Disabled for now, can be enabled later
      ...config
    };
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initPromise) return this.initPromise;

    this.initPromise = this.initializeDB();
    await this.initPromise;
  }

  private async initializeDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!window.indexedDB) {
        console.warn('IndexedDB not available, falling back to localStorage');
        this.isInitialized = true;
        resolve();
        return;
      }

      const request = indexedDB.open(this.config.dbName, this.config.version);

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        this.isInitialized = true; // Continue with localStorage fallback
        resolve();
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores
        this.config.stores.forEach(storeName => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, { keyPath: 'id' });
            store.createIndex('timestamp', 'timestamp', { unique: false });
            store.createIndex('version', 'version', { unique: false });
          }
        });
      };
    });
  }

  // Generic storage methods
  async store<T>(storeName: string, id: string, data: T, metadata?: Record<string, any>): Promise<void> {
    await this.initialize();

    const item: StoredItem<T> = {
      id,
      data,
      timestamp: Date.now(),
      version: 1,
      metadata
    };

    if (this.db) {
      return this.storeInIndexedDB(storeName, item);
    } else {
      return this.storeInLocalStorage(storeName, item);
    }
  }

  async retrieve<T>(storeName: string, id: string): Promise<T | null> {
    await this.initialize();

    if (this.db) {
      return this.retrieveFromIndexedDB<T>(storeName, id);
    } else {
      return this.retrieveFromLocalStorage<T>(storeName, id);
    }
  }

  async list<T>(storeName: string, limit?: number): Promise<StoredItem<T>[]> {
    await this.initialize();

    if (this.db) {
      return this.listFromIndexedDB<T>(storeName, limit);
    } else {
      return this.listFromLocalStorage<T>(storeName, limit);
    }
  }

  async remove(storeName: string, id: string): Promise<void> {
    await this.initialize();

    if (this.db) {
      return this.removeFromIndexedDB(storeName, id);
    } else {
      return this.removeFromLocalStorage(storeName, id);
    }
  }

  async clear(storeName: string): Promise<void> {
    await this.initialize();

    if (this.db) {
      return this.clearIndexedDB(storeName);
    } else {
      return this.clearLocalStorage(storeName);
    }
  }

  // IndexedDB implementations
  private async storeInIndexedDB<T>(storeName: string, item: StoredItem<T>): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(item);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async retrieveFromIndexedDB<T>(storeName: string, id: string): Promise<T | null> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.data : null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  private async listFromIndexedDB<T>(storeName: string, limit?: number): Promise<StoredItem<T>[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index('timestamp');
      const request = index.openCursor(null, 'prev'); // Most recent first

      const results: StoredItem<T>[] = [];
      let count = 0;

      request.onsuccess = () => {
        const cursor = request.result;
        if (cursor && (!limit || count < limit)) {
          results.push(cursor.value);
          count++;
          cursor.continue();
        } else {
          resolve(results);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  private async removeFromIndexedDB(storeName: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private async clearIndexedDB(storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // localStorage implementations (fallback)
  private async storeInLocalStorage<T>(storeName: string, item: StoredItem<T>): Promise<void> {
    try {
      const key = `${this.config.dbName}_${storeName}_${item.id}`;
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      throw new Error(`Failed to store in localStorage: ${error}`);
    }
  }

  private async retrieveFromLocalStorage<T>(storeName: string, id: string): Promise<T | null> {
    try {
      const key = `${this.config.dbName}_${storeName}_${id}`;
      const item = localStorage.getItem(key);
      if (item) {
        const parsed: StoredItem<T> = JSON.parse(item);
        return parsed.data;
      }
      return null;
    } catch (error) {
      console.error('Failed to retrieve from localStorage:', error);
      return null;
    }
  }

  private async listFromLocalStorage<T>(storeName: string, limit?: number): Promise<StoredItem<T>[]> {
    try {
      const prefix = `${this.config.dbName}_${storeName}_`;
      const results: StoredItem<T>[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const item = localStorage.getItem(key);
          if (item) {
            results.push(JSON.parse(item));
          }
        }
      }

      // Sort by timestamp (most recent first)
      results.sort((a, b) => b.timestamp - a.timestamp);

      return limit ? results.slice(0, limit) : results;
    } catch (error) {
      console.error('Failed to list from localStorage:', error);
      return [];
    }
  }

  private async removeFromLocalStorage(storeName: string, id: string): Promise<void> {
    const key = `${this.config.dbName}_${storeName}_${id}`;
    localStorage.removeItem(key);
  }

  private async clearLocalStorage(storeName: string): Promise<void> {
    const prefix = `${this.config.dbName}_${storeName}_`;
    const keysToRemove: string[] = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  // Specialized methods for common data types
  async storeAnalysisResult(result: AnalysisResult): Promise<void> {
    await this.store('analysisResults', result.id, result, {
      analysisType: result.analysisType,
      model: result.model,
      style: result.style
    });
  }

  async getAnalysisResults(limit?: number): Promise<AnalysisResult[]> {
    const items = await this.list<AnalysisResult>('analysisResults', limit);
    return items.map(item => item.data);
  }

  async storeCanvasState(id: string, state: Record<string, unknown>): Promise<void> {
    await this.store('canvasStates', id, state, {
      type: 'canvas',
      lastModified: Date.now()
    });
  }

  async getCanvasState(id: string): Promise<Record<string, unknown> | null> {
    return await this.retrieve('canvasStates', id);
  }

  async storeDesignElement(id: string, element: Record<string, unknown>): Promise<void> {
    await this.store('designElements', id, element, {
      type: 'design',
      elementType: (element as { type?: unknown }).type
    });
  }

  async getDesignElements(): Promise<Record<string, unknown>[]> {
    const items = await this.list('designElements');
    return items.map(item => item.data as Record<string, unknown>);
  }

  // Storage management
  async getStorageStats(): Promise<{
    used: number;
    available: number;
    stores: Record<string, number>;
  }> {
    const stats = {
      used: 0,
      available: 0,
      stores: {} as Record<string, number>
    };

    if (this.db) {
      // For IndexedDB, we'd need to estimate usage
      for (const storeName of this.config.stores) {
        const items = await this.list(storeName);
        stats.stores[storeName] = items.length;
      }
    } else {
      // For localStorage, calculate actual usage
      let totalSize = 0;
      const prefix = `${this.config.dbName}_`;

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += key.length + value.length;
            
            // Count by store
            const storeName = key.split('_')[2];
            if (storeName) {
              stats.stores[storeName] = (stats.stores[storeName] || 0) + 1;
            }
          }
        }
      }

      stats.used = totalSize / (1024 * 1024); // Convert to MB
      stats.available = 5 - stats.used; // localStorage typically has ~5MB limit
    }

    return stats;
  }

  async cleanup(): Promise<void> {
    // Remove old items to free up space
    const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days ago

    for (const storeName of this.config.stores) {
      if (storeName === 'settings') continue; // Don't cleanup settings

      const items = await this.list(storeName);
      const oldItems = items.filter(item => item.timestamp < cutoffTime);

      for (const item of oldItems) {
        await this.remove(storeName, item.id);
      }
    }
  }

  // Export/Import functionality
  async exportData(): Promise<string> {
    const exportData: Record<string, any[]> = {};

    for (const storeName of this.config.stores) {
      exportData[storeName] = await this.list(storeName);
    }

    return JSON.stringify(exportData, null, 2);
  }

  async importData(jsonData: string): Promise<void> {
    try {
      const importData = JSON.parse(jsonData);

      for (const [storeName, items] of Object.entries(importData)) {
        if (this.config.stores.includes(storeName) && Array.isArray(items)) {
          for (const item of items as StoredItem[]) {
            await this.store(storeName, item.id, item.data, item.metadata);
          }
        }
      }
    } catch (error) {
      throw new Error(`Failed to import data: ${error}`);
    }
  }
}

// Singleton instance
export const offlineStorageService = new OfflineStorageService();
