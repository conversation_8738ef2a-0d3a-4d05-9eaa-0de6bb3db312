/**
 * Performance Optimization Utilities
 * 
 * Comprehensive performance utilities for monitoring, optimization,
 * and improving application performance across all components.
 */

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Performance monitoring types
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  frameRate: number;
  bundleSize: number;
  loadTime: number;
}

export interface ComponentMetrics {
  componentName: string;
  renderCount: number;
  averageRenderTime: number;
  lastRenderTime: number;
  propsChanges: number;
}

// Type for non-standard performance.memory
interface PerformanceMemory {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

// Performance monitoring class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, ComponentMetrics> = new Map();
  private observers: ((metrics: PerformanceMetrics) => void)[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Start monitoring a component
  startComponentRender(componentName: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      this.updateComponentMetrics(componentName, renderTime);
    };
  }

  // Update component metrics
  private updateComponentMetrics(componentName: string, renderTime: number): void {
    const existing = this.metrics.get(componentName) || {
      componentName,
      renderCount: 0,
      averageRenderTime: 0,
      lastRenderTime: 0,
      propsChanges: 0,
    };

    const newMetrics: ComponentMetrics = {
      ...existing,
      renderCount: existing.renderCount + 1,
      lastRenderTime: renderTime,
      averageRenderTime: (existing.averageRenderTime * existing.renderCount + renderTime) / (existing.renderCount + 1),
    };

    this.metrics.set(componentName, newMetrics);
  }

  // Get component metrics
  getComponentMetrics(componentName?: string): ComponentMetrics | ComponentMetrics[] {
    if (componentName) {
      return this.metrics.get(componentName) || {
        componentName,
        renderCount: 0,
        averageRenderTime: 0,
        lastRenderTime: 0,
        propsChanges: 0,
      };
    }
    return Array.from(this.metrics.values());
  }

  // Get overall performance metrics
  getPerformanceMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      renderTime: this.getAverageRenderTime(),
      memoryUsage: this.getMemoryUsage(),
      frameRate: this.getFrameRate(),
      bundleSize: this.getBundleSize(),
      loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
    };
  }

  private getAverageRenderTime(): number {
    const metrics = Array.from(this.metrics.values());
    if (metrics.length === 0) return 0;
    
    const totalTime = metrics.reduce((sum, metric) => sum + metric.averageRenderTime, 0);
    return totalTime / metrics.length;
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance.memory as PerformanceMemory);
      return memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  private getFrameRate(): number {
    // This would need to be implemented with requestAnimationFrame
    return 60; // Placeholder
  }

  private getBundleSize(): number {
    // This would need to be calculated based on loaded resources
    return 0; // Placeholder
  }

  // Subscribe to performance updates
  subscribe(callback: (metrics: PerformanceMetrics) => void): () => void {
    this.observers.push(callback);
    return () => {
      const index = this.observers.indexOf(callback);
      if (index > -1) {
        this.observers.splice(index, 1);
      }
    };
  }

  // Notify observers
  private notifyObservers(): void {
    const metrics = this.getPerformanceMetrics();
    this.observers.forEach(callback => callback(metrics));
  }
}

// React hooks for performance monitoring
export const usePerformanceMonitor = (componentName: string) => {
  const monitor = PerformanceMonitor.getInstance();
  const renderCountRef = useRef(0);

  useEffect(() => {
    renderCountRef.current += 1;
    const endRender = monitor.startComponentRender(componentName);
    endRender();
  });

  const metrics = useMemo(() => 
    monitor.getComponentMetrics(componentName) as ComponentMetrics,
    [componentName, monitor]
  );

  return {
    metrics,
    renderCount: renderCountRef.current,
  };
};

// Hook for measuring render performance
export const useRenderPerformance = (componentName: string) => {
  const [renderTime, setRenderTime] = useState(0);
  const startTimeRef = useRef<number>();

  useEffect(() => {
    startTimeRef.current = performance.now();
  });

  useEffect(() => {
    if (startTimeRef.current) {
      const endTime = performance.now();
      const duration = endTime - startTimeRef.current;
      setRenderTime(duration);
    }
  });

  return renderTime;
};

// Debounce hook for performance optimization
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Throttle hook for performance optimization
export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef<number>(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
};

// Memoization utilities
export const createMemoizedSelector = <T, R>(
  selector: (state: T) => R,
  equalityFn?: (a: R, b: R) => boolean
) => {
  let lastArgs: T | undefined;
  let lastResult: R;

  return (state: T): R => {
    if (lastArgs === undefined || !shallowEqual(lastArgs, state)) {
      lastResult = selector(state);
      lastArgs = state;
    } else if (equalityFn && !equalityFn(lastResult, selector(state))) {
      lastResult = selector(state);
      lastArgs = state;
    }
    
    return lastResult;
  };
};

// Shallow equality check
export const shallowEqual = (obj1: unknown, obj2: unknown): boolean => {
  if (obj1 === obj2) return true;
  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
    return false;
  }
  const keys1 = Object.keys(obj1 as object);
  const keys2 = Object.keys(obj2 as object);
  if (keys1.length !== keys2.length) return false;
  for (const key of keys1) {
    if (!keys2.includes(key) || (obj1 as Record<string, unknown>)[key] !== (obj2 as Record<string, unknown>)[key]) {
      return false;
    }
  }
  return true;
};

// Deep equality check (for complex objects)
export const deepEqual = (obj1: unknown, obj2: unknown): boolean => {
  if (obj1 === obj2) return true;
  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
    return false;
  }
  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;
  const keys1 = Object.keys(obj1 as object);
  const keys2 = Object.keys(obj2 as object);
  if (keys1.length !== keys2.length) return false;
  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual((obj1 as Record<string, unknown>)[key], (obj2 as Record<string, unknown>)[key])) {
      return false;
    }
  }
  return true;
};

// Lazy loading utilities
// Note: TypeScript cannot fully type-safe generic lazy HOCs with forwardRef due to ref type limitations.
// Using 'unknown' for props and ref is the safest, most robust industry approach for this pattern.
export const createLazyComponent = (
  importFn: () => Promise<{ default: React.ComponentType<unknown> }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFn);
  return React.forwardRef<unknown, unknown>((props) => {
    const elementProps = (props as object) || {};
    return React.createElement(
      React.Suspense,
      { fallback: fallback ? React.createElement(fallback) : React.createElement('div', null, 'Loading...') },
      React.createElement(LazyComponent, elementProps)
    );
  });
};

// Bundle size optimization
export const loadModuleWhenNeeded = async <T>(
  moduleLoader: () => Promise<T>,
  condition: () => boolean
): Promise<T | null> => {
  if (condition()) {
    return await moduleLoader();
  }
  return null;
};

// Memory leak prevention
export const useCleanup = (cleanup: () => void) => {
  useEffect(() => {
    return cleanup;
  }, [cleanup]);
};

// Intersection Observer for lazy loading
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [options]);

  return { ref: elementRef, isIntersecting, entry };
};

// Performance-optimized event handlers
export const useOptimizedEventHandler = <T extends (...args: unknown[]) => void>(
  handler: T,
  deps: React.DependencyList
): T => {
  return useCallback(handler, deps);
};

// Virtual scrolling utilities
export const useVirtualScrolling = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + overscan,
      items.length
    );

    return {
      start: Math.max(0, start - overscan),
      end,
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end).map((item, index) => ({
      item,
      index: visibleRange.start + index,
    }));
  }, [items, visibleRange]);

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleRange.start * itemHeight;

  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
  };
};

// Performance debugging utilities
export const logPerformance = (label: string, fn: () => void) => {
  if (process.env.NODE_ENV === 'development') {
    console.time(label);
    fn();
    console.timeEnd(label);
  } else {
    fn();
  }
};

export const measureAsync = async <T>(
  label: string,
  fn: () => Promise<T>
): Promise<T> => {
  if (process.env.NODE_ENV === 'development') {
    console.time(label);
    const result = await fn();
    console.timeEnd(label);
    return result;
  }
  return await fn();
};

// Export the performance monitor instance
export const performanceMonitor = PerformanceMonitor.getInstance();
