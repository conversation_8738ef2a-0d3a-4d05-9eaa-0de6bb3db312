import { Canvas, Image as FabricImage } from 'fabric';
import type { Object as FabricObject, Rect, Circle, Line, Polygon, Path, Text, Textbox, Image } from 'fabric';
import { ImportOptions, CanvasState, DrawingObject, ImageObject } from '@/types/figma';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';

export class ImportManager {
  private canvas: Canvas;
  private store: ReturnType<typeof useFigmaCanvasStore>;

  constructor(canvas: Canvas, store: ReturnType<typeof useFigmaCanvasStore>) {
    this.canvas = canvas;
    this.store = store;
  }

  async importImage(
    file: File,
    options: ImportOptions = {}
  ): Promise<string> {
    const {
      preserveAspectRatio = true,
      fitToCanvas = false,
      createNewLayer = false,
    } = options;

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        
        // Use global fabric.Image.fromURL to avoid type issues
        (window['fabric'].Image.fromURL as unknown as (url: string, callback: (img: FabricImage | null) => void, options?: object) => void)(
          imageUrl,
          (img: FabricImage | null) => {
            if (!img) {
              reject(new Error('Failed to load image'));
              return;
            }

            // Calculate positioning and sizing
            let { width, height } = img;
            const canvasWidth = this.canvas.getWidth();
            const canvasHeight = this.canvas.getHeight();

            if (fitToCanvas) {
              const scaleX = canvasWidth / width;
              const scaleY = canvasHeight / height;
              const scale = preserveAspectRatio ? Math.min(scaleX, scaleY) : Math.max(scaleX, scaleY);
              
              width *= scale;
              height *= scale;
              img.scale(scale);
            }

            // Center the image
            img.set({
              left: (canvasWidth - width) / 2,
              top: (canvasHeight - height) / 2,
              selectable: true,
            });

            // Create layer if requested
            let layerId = this.store.activeLayerId;
            if (createNewLayer) {
              layerId = this.store.addLayer(`Image Layer ${Date.now()}`);
              this.store.setActiveLayer(layerId);
            }

            // Create image object in store
            const imageObject: Omit<DrawingObject, 'id' | 'createdAt' | 'updatedAt'> = {
              type: 'image',
              layerId,
              transform: {
                x: img.left || 0,
                y: img.top || 0,
                width: width,
                height: height,
                rotation: img.angle || 0,
                scaleX: img.scaleX || 1,
                scaleY: img.scaleY || 1,
              },
              style: {
                opacity: img.opacity || 1,
              },
              visible: true,
              locked: false,
              name: file.name || 'Imported Image',
              src: imageUrl,
              originalSize: {
                width: img.width || 0,
                height: img.height || 0,
              },
            } as ImageObject;

            const objectId = this.store.addObject(imageObject);

            // Add to fabric canvas
            img.set('data', { id: objectId, type: 'image' });
            this.canvas.add(img);
            this.canvas.renderAll();

            resolve(objectId);
          },
          { crossOrigin: 'anonymous' }
        );
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  }

  async importCanvasData(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string);
          
          // Validate the data format
          if (!this.validateCanvasData(jsonData)) {
            reject(new Error('Invalid canvas data format'));
            return;
          }

          // Clear current canvas
          this.store.clearHistory();
          Object.keys(this.store.objects).forEach(id => {
            this.store.deleteObject(id);
          });

          // Import canvas state
          const canvasState = jsonData.canvasState as CanvasState;
          
          // Import layers
          Object.values(canvasState.layers).forEach(layer => {
            // Create layer if it doesn't exist
            if (!this.store.layers[layer.id]) {
              const layerId = this.store.addLayer(layer.name);
              this.store.updateLayer(layerId, {
                visible: layer.visible,
                locked: layer.locked,
                opacity: layer.opacity,
                blendMode: layer.blendMode,
                order: layer.order,
              });
            }
          });

          // Import objects
          Object.values(canvasState.objects).forEach(obj => {
            this.store.addObject(obj);
          });

          // Update canvas settings
          this.store.setZoom(canvasState.zoom);
          this.store.setPan(canvasState.pan);
          this.store.setGridSize(canvasState.gridSize);
          
          if (canvasState.gridVisible !== undefined) {
            if (canvasState.gridVisible !== this.store.gridVisible) {
              this.store.toggleGrid();
            }
          }
          
          if (canvasState.snapToGrid !== undefined) {
            if (canvasState.snapToGrid !== this.store.snapToGrid) {
              this.store.toggleSnapToGrid();
            }
          }

          resolve();
        } catch (error) {
          reject(new Error(`Failed to parse canvas data: ${error.message}`));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsText(file);
    });
  }

  async importSVG(file: File, options: ImportOptions = {}): Promise<string[]> {
    const {
      preserveAspectRatio = true,
      fitToCanvas = false,
      createNewLayer = false,
    } = options;

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        const svgString = e.target?.result as string;
        
        // Use window['fabric'] with type assertion and correct callback signature
        (window['fabric'] as typeof import('fabric')).loadSVGFromString(svgString, (objects, options) => {
          if (!Array.isArray(objects) || objects.length === 0) {
            reject(new Error('No objects found in SVG'));
            return;
          }

          const objectIds: string[] = [];
          let layerId = this.store.activeLayerId;

          if (createNewLayer) {
            layerId = this.store.addLayer(`SVG Import ${Date.now()}`);
            this.store.setActiveLayer(layerId);
          }

          // Process each SVG object
          objects.forEach((obj, index) => {
            if (!obj) return;

            // Apply scaling if needed
            if (fitToCanvas) {
              const canvasWidth = this.canvas.getWidth();
              const canvasHeight = this.canvas.getHeight();
              const objBounds = obj.getBoundingRect();
              
              const scaleX = canvasWidth / objBounds.width;
              const scaleY = canvasHeight / objBounds.height;
              const scale = preserveAspectRatio ? Math.min(scaleX, scaleY) : Math.max(scaleX, scaleY);
              
              obj.scale(scale);
            }

            // Create object in store
            const drawingObject: Omit<DrawingObject, 'id' | 'createdAt' | 'updatedAt'> = {
              type: this.getSVGObjectType(obj),
              layerId,
              transform: {
                x: obj.left || 0,
                y: obj.top || 0,
                width: obj.width || 0,
                height: obj.height || 0,
                rotation: obj.angle || 0,
                scaleX: obj.scaleX || 1,
                scaleY: obj.scaleY || 1,
              },
              style: {
                fill: obj.fill as string || undefined,
                stroke: obj.stroke as string || undefined,
                strokeWidth: obj.strokeWidth || undefined,
                opacity: obj.opacity || 1,
              },
              visible: true,
              locked: false,
              name: `SVG Object ${index + 1}`,
            } as DrawingObject;

            const objectId = this.store.addObject(drawingObject);
            objectIds.push(objectId);

            // Add to fabric canvas
            obj.set('data', { id: objectId, type: drawingObject.type });
            this.canvas.add(obj);
          });

          this.canvas.renderAll();
          resolve(objectIds);
        });
      };

      reader.onerror = () => {
        reject(new Error('Failed to read SVG file'));
      };

      reader.readAsText(file);
    });
  }

  private validateCanvasData(data: unknown): boolean {
    // Basic validation of canvas data structure
    return (
      typeof data === 'object' && data !== null &&
      'canvasState' in data &&
      typeof (data as { canvasState?: unknown }).canvasState === 'object' &&
      (data as { canvasState: CanvasState }).canvasState.objects &&
      typeof (data as { canvasState: CanvasState }).canvasState.objects === 'object' &&
      (data as { canvasState: CanvasState }).canvasState.layers &&
      typeof (data as { canvasState: CanvasState }).canvasState.layers === 'object'
    );
  }

  // Replace all fabric.* types with imported types
  private getSVGObjectType(obj: FabricObject): string {
    const fabricNS = window['fabric'] as typeof import('fabric');
    if (fabricNS && obj instanceof fabricNS.Rect) return 'rectangle';
    if (fabricNS && obj instanceof fabricNS.Circle) return 'circle';
    if (fabricNS && obj instanceof fabricNS.Line) return 'line';
    if (fabricNS && obj instanceof fabricNS.Polygon) return 'polygon';
    if (fabricNS && obj instanceof fabricNS.Path) return 'pen';
    if (fabricNS && (obj instanceof fabricNS.Text || obj instanceof fabricNS.Textbox)) return 'text';
    if (fabricNS && obj instanceof fabricNS.Image) return 'image';
    return 'unknown';
  }

  // Utility method to handle file drop
  handleFileDrop(files: FileList, options: ImportOptions = {}): Promise<string[]> {
    const importPromises: Promise<string | string[]>[] = [];

    Array.from(files).forEach(file => {
      const fileType = file.type.toLowerCase();
      
      if (fileType.startsWith('image/')) {
        if (fileType === 'image/svg+xml') {
          importPromises.push(this.importSVG(file, options));
        } else {
          importPromises.push(this.importImage(file, options));
        }
      } else if (fileType === 'application/json' || file.name.endsWith('.json')) {
        importPromises.push(
          this.importCanvasData(file).then(() => ['canvas-data-imported'])
        );
      }
    });

    return Promise.all(importPromises).then(results => {
      return results.flat();
    });
  }

  // Create file input for importing
  createFileInput(
    accept: string = 'image/*,.json,.svg',
    multiple: boolean = true,
    options: ImportOptions = {}
  ): HTMLInputElement {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.multiple = multiple;
    input.style.display = 'none';

    input.addEventListener('change', async (e) => {
      const files = (e.target as HTMLInputElement).files;
      if (files && files.length > 0) {
        try {
          await this.handleFileDrop(files, options);
        } catch (error) {
          console.error('Import failed:', error);
          // You might want to show a toast notification here
        }
      }
    });

    return input;
  }
}
