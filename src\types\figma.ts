// Figma Clone Types and Interfaces

export type DrawingTool =
  | 'select'
  | 'rectangle'
  | 'circle'
  | 'line'
  | 'arrow'
  | 'text'
  | 'pen'
  | 'polygon'
  | 'star'
  | 'image'
  | 'bezier-curve'
  | 'path'
  | 'boolean-operation';

export type TransformTool = 
  | 'move'
  | 'resize'
  | 'rotate'
  | 'scale';

export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Transform {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  scaleX: number;
  scaleY: number;
}

export interface StyleProperties {
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  strokeDashArray?: number[];
  opacity?: number;
  shadow?: ShadowStyle;
  gradient?: GradientStyle;
}

export interface ShadowStyle {
  color: string;
  blur: number;
  offsetX: number;
  offsetY: number;
}

export interface GradientStyle {
  type: 'linear' | 'radial';
  stops: GradientStop[];
  angle?: number; // for linear gradients
  centerX?: number; // for radial gradients
  centerY?: number; // for radial gradients
  radius?: number; // for radial gradients
}

export interface GradientStop {
  offset: number; // 0-1
  color: string;
}

export interface TextStyle {
  fontFamily: string;
  fontSize: number;
  fontWeight: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
  fontStyle: 'normal' | 'italic';
  textAlign: 'left' | 'center' | 'right' | 'justify';
  textDecoration: 'none' | 'underline' | 'line-through';
  lineHeight: number;
  letterSpacing: number;
}

export interface BaseDrawingObject {
  id: string;
  type: DrawingTool;
  layerId: string;
  transform: Transform;
  style: StyleProperties;
  visible: boolean;
  locked: boolean;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RectangleObject extends BaseDrawingObject {
  type: 'rectangle';
  cornerRadius?: number;
}

export interface CircleObject extends BaseDrawingObject {
  type: 'circle';
}

export interface LineObject extends BaseDrawingObject {
  type: 'line';
  points: Point[];
}

export interface ArrowObject extends BaseDrawingObject {
  type: 'arrow';
  startPoint: Point;
  endPoint: Point;
  arrowHeadSize: number;
}

export interface TextObject extends BaseDrawingObject {
  type: 'text';
  content: string;
  textStyle: TextStyle;
  autoResize: boolean;
}

export interface PenObject extends BaseDrawingObject {
  type: 'pen';
  points: Point[];
  smoothing: number;
}

export interface PolygonObject extends BaseDrawingObject {
  type: 'polygon';
  sides: number;
  points: Point[];
}

export interface StarObject extends BaseDrawingObject {
  type: 'star';
  points: number;
  innerRadius: number;
  outerRadius: number;
}

export interface ImageObject extends BaseDrawingObject {
  type: 'image';
  src: string;
  originalSize: Size;
  cropArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface BezierPoint extends Point {
  controlPoint1?: Point;
  controlPoint2?: Point;
  type: 'move' | 'line' | 'curve' | 'close';
}

export interface PathCommand {
  type: 'M' | 'L' | 'C' | 'Q' | 'A' | 'Z';
  points: Point[];
  controlPoints?: Point[];
}

export interface BezierCurveObject extends BaseDrawingObject {
  type: 'bezier-curve';
  points: BezierPoint[];
  closed: boolean;
  smoothing: number;
}

export interface PathObject extends BaseDrawingObject {
  type: 'path';
  pathData: string; // SVG path string
  commands: PathCommand[];
}

export interface BooleanOperationObject extends BaseDrawingObject {
  type: 'boolean-operation';
  operation: 'union' | 'subtract' | 'intersect' | 'exclude';
  sourceObject1Id: string;
  sourceObject2Id: string;
  preserveOriginals: boolean;
}

export type DrawingObject =
  | RectangleObject
  | CircleObject
  | LineObject
  | ArrowObject
  | TextObject
  | PenObject
  | PolygonObject
  | StarObject
  | ImageObject
  | BezierCurveObject
  | PathObject
  | BooleanOperationObject;

export interface Layer {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  opacity: number;
  blendMode: BlendMode;
  parentId?: string; // for nested layers/groups
  order: number;
  objects: string[]; // object IDs in this layer
}

export type BlendMode = 
  | 'normal'
  | 'multiply'
  | 'screen'
  | 'overlay'
  | 'soft-light'
  | 'hard-light'
  | 'color-dodge'
  | 'color-burn'
  | 'darken'
  | 'lighten'
  | 'difference'
  | 'exclusion';

export interface CanvasState {
  objects: Record<string, DrawingObject>;
  layers: Record<string, Layer>;
  selectedObjectIds: string[];
  activeLayerId: string;
  activeTool: DrawingTool;
  zoom: number;
  pan: Point;
  gridVisible: boolean;
  snapToGrid: boolean;
  gridSize: number;
  canvasSize: Size;
  backgroundColor: string;
}

export interface ToolSettings {
  [key: string]: unknown;
  // Rectangle tool settings
  rectangleCornerRadius?: number;
  // Line tool settings
  lineStrokeWidth?: number;
  lineStrokeColor?: string;
  // Text tool settings
  textFontFamily?: string;
  textFontSize?: number;
  textColor?: string;
  // Pen tool settings
  penStrokeWidth?: number;
  penStrokeColor?: string;
  penSmoothing?: number;
}

export interface ExportOptions {
  format: 'png' | 'jpg' | 'svg' | 'pdf';
  quality?: number; // for jpg
  scale?: number; // export scale multiplier
  backgroundColor?: string;
  includeBackground?: boolean;
  selectedOnly?: boolean;
}

export interface ImportOptions {
  preserveAspectRatio?: boolean;
  fitToCanvas?: boolean;
  createNewLayer?: boolean;
}

// Events
export interface CanvasEvent {
  type: string;
  timestamp: Date;
  data: Record<string, unknown>;
}

export interface ObjectCreatedEvent extends CanvasEvent {
  type: 'object-created';
  data: {
    objectId: string;
    object: DrawingObject;
  };
}

export interface ObjectUpdatedEvent extends CanvasEvent {
  type: 'object-updated';
  data: {
    objectId: string;
    changes: Partial<DrawingObject>;
  };
}

export interface ObjectDeletedEvent extends CanvasEvent {
  type: 'object-deleted';
  data: {
    objectId: string;
  };
}

export interface SelectionChangedEvent extends CanvasEvent {
  type: 'selection-changed';
  data: {
    selectedObjectIds: string[];
  };
}

export type FigmaCanvasEvent = 
  | ObjectCreatedEvent
  | ObjectUpdatedEvent
  | ObjectDeletedEvent
  | SelectionChangedEvent;
