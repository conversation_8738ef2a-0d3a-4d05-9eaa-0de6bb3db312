export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system'; // Added 'system' role
  content: string;
  timestamp: Date;
  /** For user messages: 0-100% rating. */
  performanceScore?: number;
  /** For user messages: auto-feedback object. */
  autoFeedback?: {
    text: string;
    isLoading: boolean;
    detail?: {
      communication: number;
      social: number;
      psychological: number;
      gender_cultural: number;
    };
    biasAnalysis?: {
      name: string;
      explanation: string;
    };
  };
}

export type FeedbackMode = "general" | "style-specific" | "cultural" | "psychological" | "social" | "bias";

// Define and export ConversationModeType
export type ConversationModeType = "single-question" | "cycle-answers" | "cycle-followups";
