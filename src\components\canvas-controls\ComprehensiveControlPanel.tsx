import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Command, 
  Palette, 
  Layers, 
  Search, 
  Settings,
  Brain,
  Zap,
  BarChart3,
  Users,
  Clock,
  Target,
  Workflow,
  Database,
  Shield,
  Globe,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { AdvancedCanvasControls } from './AdvancedCanvasControls';
import { IntelligentClusteringSystem } from '../figma-canvas/clustering/IntelligentClusteringSystem';
import { AdvancedSearchSystem } from '../library/AdvancedSearchSystem';
import { DrawingObject } from '@/types/figma';
import { SavedAnalysis } from '@/types/conversation';

export interface ControlPanelState {
  activePanel: string;
  panelWidth: number;
  collapsed: boolean;
  docked: 'left' | 'right' | 'bottom' | 'floating';
  transparency: number;
  autoHide: boolean;
  shortcuts: Record<string, string>;
  customizations: Record<string, string | number | boolean>;
}

export interface SystemMetrics {
  performance: {
    fps: number;
    memoryUsage: number;
    renderTime: number;
    objectCount: number;
  };
  usage: {
    activeUsers: number;
    sessionsToday: number;
    analysesCreated: number;
    exportCount: number;
  };
  system: {
    version: string;
    uptime: number;
    lastBackup: Date;
    storageUsed: number;
  };
}

// System action parameters type
type SystemActionParams = string | number | boolean | Record<string, unknown> | undefined;

interface ComprehensiveControlPanelProps {
  objects: Record<string, DrawingObject>;
  analyses: SavedAnalysis[];
  metrics?: SystemMetrics;
  onPanelChange?: (panel: string) => void;
  onSystemAction?: (action: string, params?: SystemActionParams) => void;
  className?: string;
}

export const ComprehensiveControlPanel: React.FC<ComprehensiveControlPanelProps> = ({
  objects,
  analyses,
  metrics,
  onPanelChange,
  onSystemAction,
  className,
}) => {
  const [controlState, setControlState] = useState<ControlPanelState>({
    activePanel: 'canvas',
    panelWidth: 320,
    collapsed: false,
    docked: 'right',
    transparency: 0,
    autoHide: false,
    shortcuts: {
      'ctrl+1': 'canvas',
      'ctrl+2': 'layers',
      'ctrl+3': 'search',
      'ctrl+4': 'clustering',
      'ctrl+5': 'analytics',
    },
    customizations: {},
  });

  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>(
    metrics || {
      performance: { fps: 60, memoryUsage: 45, renderTime: 16, objectCount: Object.keys(objects).length },
      usage: { activeUsers: 1, sessionsToday: 5, analysesCreated: analyses.length, exportCount: 12 },
      system: { version: '2.1.0', uptime: 3600000, lastBackup: new Date(), storageUsed: 256 },
    }
  );

  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    timestamp: Date;
  }>>([]);

  // Panel configurations
  const panelConfigs = [
    {
      id: 'canvas',
      label: 'Canvas',
      icon: Palette,
      description: 'Canvas controls and view settings',
      component: AdvancedCanvasControls,
    },
    {
      id: 'layers',
      label: 'Layers',
      icon: Layers,
      description: 'Layer management and organization',
      component: null,
    },
    {
      id: 'search',
      label: 'Search',
      icon: Search,
      description: 'Advanced search and filtering',
      component: AdvancedSearchSystem,
    },
    {
      id: 'clustering',
      label: 'AI Clustering',
      icon: Brain,
      description: 'Intelligent object clustering',
      component: IntelligentClusteringSystem,
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      description: 'Performance and usage analytics',
      component: null,
    },
    {
      id: 'collaboration',
      label: 'Collaboration',
      icon: Users,
      description: 'Real-time collaboration tools',
      component: null,
    },
    {
      id: 'automation',
      label: 'Automation',
      icon: Zap,
      description: 'Workflow automation and scripts',
      component: null,
    },
    {
      id: 'system',
      label: 'System',
      icon: Settings,
      description: 'System settings and preferences',
      component: null,
    },
  ];

  // Update panel state
  const updatePanel = useCallback((updates: Partial<ControlPanelState>) => {
    setControlState(prev => ({ ...prev, ...updates }));
  }, []);

  // Switch active panel
  const switchPanel = useCallback((panelId: string) => {
    updatePanel({ activePanel: panelId });
    onPanelChange?.(panelId);
  }, [updatePanel, onPanelChange]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const shortcut = `${event.ctrlKey ? 'ctrl+' : ''}${event.key.toLowerCase()}`;
      const panelId = controlState.shortcuts[shortcut];
      if (panelId) {
        event.preventDefault();
        switchPanel(panelId);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [controlState.shortcuts, switchPanel]);

  // Update metrics periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemMetrics(prev => ({
        ...prev,
        performance: {
          ...prev.performance,
          fps: 58 + Math.random() * 4,
          memoryUsage: 40 + Math.random() * 20,
          renderTime: 14 + Math.random() * 4,
          objectCount: Object.keys(objects).length,
        },
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [objects]);

  // Add notification
  const addNotification = useCallback((type: 'info' | 'warning' | 'error' | 'success', message: string) => {
    const notification = {
      id: `notif_${Date.now()}`,
      type,
      message,
      timestamp: new Date(),
    };
    setNotifications(prev => [...prev, notification]);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 5000);
  }, []);

  // Render active panel content
  const renderPanelContent = () => {
    const activeConfig = panelConfigs.find(p => p.id === controlState.activePanel);
    if (!activeConfig) return null;

    switch (controlState.activePanel) {
      case 'canvas':
        return (
          <AdvancedCanvasControls
            objects={objects}
            analyses={analyses}
            onControlChange={(control, value) => console.log('Control changed:', control, value)}
            onObjectFilter={(ids) => console.log('Objects filtered:', ids)}
            onLayerUpdate={(id, updates) => console.log('Layer updated:', id, updates)}
            onSimulationControl={(action) => console.log('Simulation:', action)}
          />
        );

      case 'search':
        return (
          <div className="p-4">
            <AdvancedSearchSystem
              analyses={analyses}
              onSearchResults={(results) => console.log('Search results:', results)}
              enableSemanticSearch={true}
            />
          </div>
        );

      case 'clustering':
        return (
          <div className="p-4">
            <IntelligentClusteringSystem
              objects={objects}
              clusters={[]}
              onClusterCreate={(cluster) => console.log('Cluster created:', cluster)}
              enableAIClustering={true}
            />
          </div>
        );

      case 'analytics':
        return (
          <div className="p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">FPS</div>
                    <div className="font-medium">{systemMetrics.performance.fps.toFixed(1)}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Memory</div>
                    <div className="font-medium">{systemMetrics.performance.memoryUsage.toFixed(0)}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Render Time</div>
                    <div className="font-medium">{systemMetrics.performance.renderTime.toFixed(1)}ms</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Objects</div>
                    <div className="font-medium">{systemMetrics.performance.objectCount}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-muted-foreground">Active Users</div>
                    <div className="font-medium">{systemMetrics.usage.activeUsers}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Sessions Today</div>
                    <div className="font-medium">{systemMetrics.usage.sessionsToday}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Analyses</div>
                    <div className="font-medium">{systemMetrics.usage.analysesCreated}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Exports</div>
                    <div className="font-medium">{systemMetrics.usage.exportCount}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'collaboration':
        return (
          <div className="p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Active Collaborators</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">You (Owner)</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    No other users currently active
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Sharing Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button size="sm" className="w-full">
                  <Users className="h-4 w-4 mr-2" />
                  Invite Collaborators
                </Button>
                <Button size="sm" variant="outline" className="w-full">
                  <Globe className="h-4 w-4 mr-2" />
                  Share Public Link
                </Button>
              </CardContent>
            </Card>
          </div>
        );

      case 'automation':
        return (
          <div className="p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Workflow Automation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button size="sm" className="w-full">
                  <Workflow className="h-4 w-4 mr-2" />
                  Create Workflow
                </Button>
                <Button size="sm" variant="outline" className="w-full">
                  <Zap className="h-4 w-4 mr-2" />
                  Run Script
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Scheduled Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-muted-foreground">
                  No scheduled tasks configured
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'system':
        return (
          <div className="p-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">System Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Version:</span>
                  <span>{systemMetrics.system.version}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Uptime:</span>
                  <span>{Math.floor(systemMetrics.system.uptime / 3600000)}h</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Storage:</span>
                  <span>{systemMetrics.system.storageUsed}MB</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="autoSave"
                    defaultChecked
                  />
                  <label htmlFor="autoSave" className="text-sm">Auto-save</label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="notifications"
                    defaultChecked
                  />
                  <label htmlFor="notifications" className="text-sm">Notifications</label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="analytics"
                    defaultChecked
                  />
                  <label htmlFor="analytics" className="text-sm">Usage Analytics</label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Device Optimization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-3 gap-1">
                  <Button size="sm" variant="outline">
                    <Monitor className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Tablet className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Smartphone className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <div className="p-4">
            <div className="text-center text-muted-foreground">
              <activeConfig.icon className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">{activeConfig.description}</div>
              <div className="text-xs mt-1">Coming soon...</div>
            </div>
          </div>
        );
    }
  };

  if (controlState.collapsed) {
    return (
      <div className={`w-12 h-full bg-white border-l flex flex-col ${className}`}>
        <div className="flex-1 flex flex-col gap-1 p-1">
          {panelConfigs.slice(0, 6).map(panel => (
            <Button
              key={panel.id}
              size="sm"
              variant={controlState.activePanel === panel.id ? "default" : "ghost"}
              className="w-10 h-10 p-0"
              onClick={() => {
                switchPanel(panel.id);
                updatePanel({ collapsed: false });
              }}
              title={panel.label}
            >
              <panel.icon className="h-4 w-4" />
            </Button>
          ))}
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          className="w-10 h-10 p-0 m-1"
          onClick={() => updatePanel({ collapsed: false })}
        >
          <Command className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div 
      className={`h-full bg-white border-l flex flex-col ${className}`}
      style={{ 
        width: controlState.panelWidth,
        opacity: 1 - controlState.transparency / 100,
      }}
    >
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Control Panel</CardTitle>
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => updatePanel({ collapsed: true })}
                className="h-6 w-6 p-0"
              >
                <Command className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Panel Navigation */}
      <div className="border-b">
        <Tabs value={controlState.activePanel} onValueChange={switchPanel}>
          <TabsList className="grid grid-cols-4 w-full h-auto p-1">
            {panelConfigs.slice(0, 8).map(panel => (
              <TabsTrigger
                key={panel.id}
                value={panel.id}
                className="flex flex-col gap-1 h-12 text-xs"
              >
                <panel.icon className="h-4 w-4" />
                <span className="truncate">{panel.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Panel Content */}
      <div className="flex-1 overflow-auto">
        {renderPanelContent()}
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="border-t p-2 space-y-1 max-h-32 overflow-auto">
          {notifications.slice(-3).map(notification => (
            <div
              key={notification.id}
              className={`text-xs p-2 rounded ${
                notification.type === 'error' ? 'bg-red-50 text-red-700' :
                notification.type === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                notification.type === 'success' ? 'bg-green-50 text-green-700' :
                'bg-blue-50 text-blue-700'
              }`}
            >
              {notification.message}
            </div>
          ))}
        </div>
      )}

      {/* Status Bar */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>System Online</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3" />
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
