
// Enhanced error handling for API responses
async function handleAPIResponse(response: Response): Promise<unknown> {
  if (!response.ok) {
    let errorBody = '';
    try {
      errorBody = await response.text();
    } catch (e) {
      errorBody = 'Unable to read error response';
    }

    let errorMessage = `OpenRouter API error (${response.status}): ${response.statusText}`;
    
    // Provide more specific error messages based on status codes
    switch (response.status) {
      case 400:
        errorMessage = 'Invalid request parameters for chat completion.';
        break;
      case 401:
        errorMessage = 'Invalid API key for chat service.';
        break;
      case 403:
        errorMessage = 'Access forbidden. Check API key permissions.';
        break;
      case 429:
        errorMessage = 'Chat rate limit exceeded. Please wait before sending another message.';
        break;
      case 500:
        errorMessage = 'OpenRouter server error. Please try again.';
        break;
      case 502:
      case 503:
      case 504:
        errorMessage = 'Chat service temporarily unavailable. Please try again later.';
        break;
    }

    console.error("OpenRouter Chat API Error:", {
      status: response.status,
      statusText: response.statusText,
      errorBody
    });

    throw new Error(errorMessage);
  }

  try {
    return await response.json();
  } catch (error) {
    throw new Error('Invalid JSON response from OpenRouter chat API');
  }
}

interface FetchAssistantResponseParams {
  apiKey: string;
  model: string;
  systemPrompt: string;
  history: { role: 'user' | 'assistant'; content: string }[];
  userMessage: string;
}

export const fetchAssistantResponse = async ({
  apiKey,
  model,
  systemPrompt,
  history,
  userMessage,
}: FetchAssistantResponseParams): Promise<string> => {
  if (!apiKey?.trim()) {
    throw new Error("API key is required for chat");
  }

  if (!userMessage?.trim()) {
    throw new Error("Message cannot be empty");
  }

  if (!model?.trim()) {
    throw new Error("Model selection is required");
  }

  try {
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
        'HTTP-Referer': window.location.href,
        'X-Title': 'ChatCraft Trainer Pro - Chat'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: "system", content: systemPrompt },
          ...history.map((msg) => ({
            role: msg.role,
            content: msg.content,
          })),
          { role: "user", content: userMessage },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    const data = await handleAPIResponse(response);
    
    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      throw new Error("Invalid response structure from chat API");
    }

    const assistantContent = data.choices[0]?.message?.content;

    if (!assistantContent || typeof assistantContent !== 'string') {
      throw new Error("No valid content in assistant response");
    }

    return assistantContent;
  } catch (error) {
    console.error('Chat assistant response failed:', error);
    throw error;
  }
};

interface FetchAutoFeedbackParams {
  apiKey: string;
  model: string;
  feedbackPrompt: string;
}

export const fetchAutoFeedback = async ({
  apiKey,
  model,
  feedbackPrompt,
}: FetchAutoFeedbackParams): Promise<string> => {
  if (!apiKey?.trim()) {
    throw new Error("API key is required for feedback");
  }

  if (!feedbackPrompt?.trim()) {
    throw new Error("Feedback prompt cannot be empty");
  }

  if (!model?.trim()) {
    throw new Error("Model selection is required for feedback");
  }

  try {
    const feedbackResponse = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
        'HTTP-Referer': window.location.href,
        'X-Title': 'ChatCraft Trainer Pro - Feedback'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: "system",
            content: "You are an expert coach evaluating user conversational skills as specified.",
          },
          { role: "user", content: feedbackPrompt },
        ],
        temperature: 0.3,
        max_tokens: 550,
      }),
    });

    const feedbackData = await handleAPIResponse(feedbackResponse);
    
    if (!feedbackData.choices || !Array.isArray(feedbackData.choices) || feedbackData.choices.length === 0) {
      throw new Error("Invalid response structure from feedback API");
    }

    const feedbackContent = feedbackData.choices[0]?.message?.content;
    
    if (!feedbackContent || typeof feedbackContent !== 'string') {
      throw new Error("No valid feedback content received");
    }

    return feedbackContent;
  } catch (error) {
    console.error('Auto feedback generation failed:', error);
    throw error;
  }
};
