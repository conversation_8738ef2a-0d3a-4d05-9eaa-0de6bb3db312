---
applyTo: '**/*.*'


---
Coding standards, domain knowledge, and preferences that AI should follow.

## Effective Codebase Usage

- **Understand the Project Structure:**  
  - Familiarize yourself with the folder and file organization. Refer to the `README.md` and project documentation for guidance.
  - Follow existing patterns and conventions when adding new files or modules.

- **Reuse Existing Code:**  
  - Before writing new code, search the codebase for existing utilities, components, or functions that can be reused or extended.
  - Avoid duplicating logic—contribute improvements to shared modules when possible.

- **Consistent Naming:**  
  - Use consistent and descriptive names for files, variables, functions, and classes that align with project conventions.

- **Respect Module Boundaries:**  
  - Keep code modular. Place new functionality in the appropriate module or package.
  - Avoid tight coupling between unrelated modules.

- **Document Your Work:**  
  - Update or add documentation when you introduce new features or make significant changes.
  - Use clear commit messages that describe the purpose and impact of your changes.

- **Follow Contribution Guidelines:**  
  - Review and adhere to the project's contribution guidelines and coding standards.
  - Submit pull requests with clear descriptions and reference related issues when applicable.

- **Keep Dependencies Up-to-Date:**  
  - Use the recommended package manager (e.g., npm, yarn, pip) for installing dependencies.
  - Regularly check for and update outdated or vulnerable packages.

- **Run Tests and Linters:**  
  - Always run the full test suite and linter before pushing changes.
  - Ensure your changes do not break existing functionality.

- **Ask for Help:**  
  - If you are unsure about any part of the codebase, ask teammates or consult documentation





## Fortune 500 Company Quality Coding Standards

- **Code Readability:**  
  - Write clear, self-explanatory code with meaningful variable, function, and class names.
  - Use consistent indentation and spacing throughout the codebase.
  - Break complex logic into small, reusable, and testable functions or modules.

- **Documentation:**  
  - Document all public functions, classes, and modules with clear docstrings or comments.
  - Maintain up-to-date README and API documentation.
  - Use inline comments to explain non-obvious code sections.

- **Code Reviews:**  
  - All code changes must be peer-reviewed before merging.
  - Address all review comments and suggestions.
  - Use pull requests with descriptive titles and summaries.

- **Testing:**  
  - Write unit tests for all new features and bug fixes.
  - Ensure high code coverage (aim for 80%+ where practical).
  - Use automated testing tools and run tests before every commit.

- **Error Handling & Logging:**  
  - Handle all errors gracefully and log meaningful error messages.
  - Avoid exposing sensitive information in logs or error messages.
  - Use structured logging where possible.

- **Security:**  
  - Follow secure coding practices (e.g., input validation, output encoding, avoid hardcoded secrets).
  - Regularly update dependencies to patch vulnerabilities.
  - Use code scanning tools to detect security issues.

- **Performance:**  
  - Write efficient code and avoid unnecessary computations.
  - Profile and optimize performance-critical sections.
  - Monitor application performance in production.

- **Version Control:**  
  - Use feature branches for all development work.
  - Write clear, concise commit messages.
  - Rebase or merge main branch regularly to avoid conflicts.

- **Continuous Integration/Continuous Deployment (CI/CD):**  
  - Integrate code with automated build, test, and deployment pipelines.
  - Ensure all checks pass before merging to main.

- **Coding Standards Compliance:**  
  - Adhere to language-specific style guides (e.g., PEP8 for Python, Airbnb for JavaScript).
  - Use automated linters and formatters to enforce standards.

- **Accessibility & Internationalization:**  
  - Ensure UI components are accessible (e.g., ARIA labels, keyboard navigation).
  - Design for localization and support multiple languages




## Linting and Code Style

- Always run the linter before committing code.
- Fix all warnings and errors reported by [your linter].
- Use consistent indentation (e.g., 2 spaces for JS, 4 for Python).
- Prefer single quotes for strings in JavaScript.
- Remove unused variables and imports.

### Debugging

- Use descriptive log messages.
- Remove all `console.log`/`print` statements before merging.
- Use VS Code breakpoints for step debugging.

### Example

```js
// Bad
let foo = "bar"
console.log(foo)

// Good
let foo = 'bar';
// ...use foo appropriately, remove debug logs before commit
```

## Debugging and Troubleshooting Rules

- **Use Breakpoints:** Always use VS Code breakpoints for step-by-step debugging instead of relying on print or log statements.
- **Descriptive Logs:** When logging is necessary, use clear and descriptive messages that include relevant variable values and context.
- **Remove Debug Code:** Ensure all `console.log`, `print`, and other debug statements are removed before committing or merging code.
- **Error Handling:** Always handle errors gracefully and log error details for troubleshooting (avoid exposing sensitive data).
- **Reproduce Bugs:** Before fixing, reliably reproduce bugs and document the steps to reproduce in commit messages or issue trackers.
- **Isolate Issues:** Use feature flags, mocks, or stubs to isolate problematic code sections during debugging.
- **Check Linter Output:** Address all linter warnings and errors, as they often highlight potential bugs or bad practices.
- **Use Source Control:** Commit frequently with clear messages, so you can easily revert to a known good state if needed.
- **Automated Tests:** Write or update unit tests to cover bug fixes and prevent regressions.
- **Document Solutions:** Add comments or documentation for non-obvious fixes or workarounds to help future maintainers.

### Example Debugging Workflow

1. Identify and reproduce the issue.
2. Set breakpoints at suspected problem areas.
3. Step through code and inspect variable values.
4. Make minimal changes to isolate the bug.
5. Fix the issue, remove debug code, and rerun tests.
6. Commit with a clear message describing the fix and how it was resolved.

## Recommended Tools

- Use [ESLint](https://eslint.org/) for JavaScript/TypeScript linting.
- Use [Prettier](https://prettier.io/) for code formatting.
- Use [VS Code ESLint extension](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) for real-time lint feedback.

## Common Pitfalls

- Forgetting to run the linter before pushing code.
- Leaving debug statements (`console.log`, `print`) in production code.
- Not updating or writing tests after bug fixes.

## Project Scripts

- `npm run lint` — Run linter on all source files.
- `npm run format` — Auto-format codebase.
- `npm test` — Run all tests.

## Troubleshooting Checklist

- [ ] Did you run the linter and fix all issues?
- [ ] Did you remove all debug statements?
- [ ] Did you write/update tests for your changes?
- [ ] Did you document any tricky fixes?

