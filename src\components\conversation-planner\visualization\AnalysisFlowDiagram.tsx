import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  MessageSquare, 
  Brain, 
  Target, 
  GitBranch, 
  ArrowRight, 
  ArrowDown,
  Zap,
  CheckCircle,
  Clock,
  AlertTriangle,
  Sparkles,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { AnalysisResult } from '@/types/conversation';
import { getAnalysisTypeColor } from './themes';
import { cn } from '@/lib/utils';
import { ParsedOutput, ParsedMultipleAnswers } from "@/services/openRouter/types/parsedOutput";

interface FlowNodeData {
  content?: string;
  insights?: string[];
  metrics?: Record<string, number>;
  connections?: string[];
  answer?: string;
  number?: number;
  model?: string;
  analysisType?: string;
  timestamp?: Date;
  analysis?: string;
  rating?: number;
  [key: string]: unknown;
}

interface FlowNode {
  id: string;
  type: 'input' | 'processing' | 'analysis' | 'output' | 'branch';
  title: string;
  description: string;
  status: 'pending' | 'active' | 'completed' | 'error';
  data?: FlowNodeData;
  position: { x: number; y: number };
  connections: string[];
}

interface AnalysisFlowDiagramProps {
  result: AnalysisResult;
  onNodeClick?: (nodeId: string, data?: FlowNodeData) => void;
  onStepSelect?: (step: string) => void;
  animated?: boolean;
  interactive?: boolean;
  showProgress?: boolean;
  className?: string;
}

const createFlowNodes = (result: AnalysisResult): FlowNode[] => {
  const nodes: FlowNode[] = [];
  const typeColor = getAnalysisTypeColor(result.analysisType);

  // Input Node
  nodes.push({
    id: 'input',
    type: 'input',
    title: 'Question Input',
    description: result.question,
    status: 'completed',
    data: { question: result.question, context: result.seedContext },
    position: { x: 0, y: 0 },
    connections: ['processing']
  });

  // Processing Node
  nodes.push({
    id: 'processing',
    type: 'processing',
    title: 'AI Processing',
    description: `Processing with ${result.model}`,
    status: 'completed',
    data: { model: result.model, style: result.style },
    position: { x: 1, y: 0 },
    connections: ['analysis']
  });

  // Analysis Node
  nodes.push({
    id: 'analysis',
    type: 'analysis',
    title: `${result.analysisType.charAt(0).toUpperCase() + result.analysisType.slice(1)} Analysis`,
    description: 'Generating structured analysis',
    status: 'completed',
    data: { analysisType: result.analysisType, timestamp: result.timestamp },
    position: { x: 2, y: 0 },
    connections: ['output']
  });

  // Output Node
  nodes.push({
    id: 'output',
    type: 'output',
    title: 'Analysis Result',
    description: 'Generated insights and recommendations',
    status: 'completed',
    data: { analysis: result.analysis, rating: result.rating },
    position: { x: 3, y: 0 },
    connections: []
  });

  // Add branch nodes for specific analysis types
  if (result.parsedOutput?.type === 'multiple' && (result.parsedOutput as ParsedMultipleAnswers).answers) {
    const parsed = result.parsedOutput as ParsedMultipleAnswers;
    parsed.answers.forEach((answer, index) => {
      nodes.push({
        id: `branch-${index}`,
        type: 'branch',
        title: `Option ${index + 1}`,
        description: answer.content.substring(0, 100) + '...',
        status: 'completed',
        data: { answer: answer.content, number: answer.id },
        position: { x: 4, y: index - (parsed.answers.length - 1) / 2 },
        connections: []
      });
    });
    
    // Update output connections
    const outputNode = nodes.find(n => n.id === 'output');
    if (outputNode) {
      outputNode.connections = parsed.answers.map((_, index) => `branch-${index}`);
    }
  }

  return nodes;
};

const FlowNodeComponent: React.FC<{
  node: FlowNode;
  isActive?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  animated?: boolean;
  index: number;
}> = ({ node, isActive, isSelected, onClick, animated = true, index }) => {
  const getNodeIcon = () => {
    switch (node.type) {
      case 'input': return MessageSquare;
      case 'processing': return Zap;
      case 'analysis': return Brain;
      case 'output': return Target;
      case 'branch': return GitBranch;
      default: return MessageSquare;
    }
  };

  const getStatusIcon = () => {
    switch (node.status) {
      case 'pending': return Clock;
      case 'active': return Zap;
      case 'completed': return CheckCircle;
      case 'error': return AlertTriangle;
      default: return Clock;
    }
  };

  const getNodeColor = () => {
    switch (node.type) {
      case 'input': return 'from-blue-500 to-blue-600';
      case 'processing': return 'from-purple-500 to-purple-600';
      case 'analysis': return 'from-green-500 to-green-600';
      case 'output': return 'from-orange-500 to-orange-600';
      case 'branch': return 'from-indigo-500 to-indigo-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const getStatusColor = () => {
    switch (node.status) {
      case 'pending': return 'text-gray-500';
      case 'active': return 'text-yellow-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const IconComponent = getNodeIcon();
  const StatusIcon = getStatusIcon();

  return (
    <motion.div
      initial={animated ? { opacity: 0, scale: 0.8, y: 20 } : false}
      animate={{ opacity: 1, scale: isSelected ? 1.05 : 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: animated ? index * 0.1 : 0,
        type: "spring",
        stiffness: 300,
        damping: 20
      }}
      whileHover={{ scale: 1.02, y: -2 }}
      className="relative cursor-pointer"
      onClick={onClick}
    >
      <Card className={cn(
        "w-64 shadow-lg border-2 transition-all duration-200",
        isSelected ? "border-blue-400 shadow-xl" : "border-gray-200 hover:border-gray-300",
        isActive && "ring-2 ring-blue-400 ring-offset-2"
      )}>
        <CardHeader className={cn(
          "pb-3 bg-gradient-to-r text-white",
          getNodeColor()
        )}>
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <IconComponent className="w-4 h-4" />
              <span>{node.title}</span>
            </div>
            <StatusIcon className={cn("w-4 h-4", getStatusColor())} />
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <p className="text-xs text-gray-600 leading-relaxed mb-3">
            {node.description}
          </p>
          
          {node.data && (
            <div className="space-y-1">
              {node.data.model && (
                <Badge variant="outline" className="text-xs">
                  {String(node.data.model)}
                </Badge>
              )}
              {node.data.analysisType && (
                <Badge variant="secondary" className="text-xs ml-1">
                  {String(node.data.analysisType)}
                </Badge>
              )}
              {node.data.timestamp && node.data.timestamp instanceof Date && (
                <div className="text-xs text-gray-500 mt-2">
                  {node.data.timestamp.toLocaleTimeString()}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status indicator */}
      {node.status === 'active' && (
        <motion.div
          className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
        />
      )}
    </motion.div>
  );
};

const FlowConnection: React.FC<{
  from: { x: number; y: number };
  to: { x: number; y: number };
  animated?: boolean;
  type?: 'flow' | 'branch';
}> = ({ from, to, animated = true, type = 'flow' }) => {
  const pathData = `M ${from.x} ${from.y} L ${to.x} ${to.y}`;
  
  return (
    <svg className="absolute inset-0 pointer-events-none" style={{ zIndex: 1 }}>
      <defs>
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            fill={type === 'branch' ? '#f59e0b' : '#3b82f6'}
          />
        </marker>
      </defs>
      
      <motion.line
        x1={from.x}
        y1={from.y}
        x2={to.x}
        y2={to.y}
        stroke={type === 'branch' ? '#f59e0b' : '#3b82f6'}
        strokeWidth="2"
        strokeDasharray={type === 'branch' ? '5,5' : undefined}
        markerEnd="url(#arrowhead)"
        initial={animated ? { pathLength: 0, opacity: 0 } : false}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.5 }}
      />
    </svg>
  );
};

export const AnalysisFlowDiagram: React.FC<AnalysisFlowDiagramProps> = ({
  result,
  onNodeClick,
  onStepSelect,
  animated = true,
  interactive = true,
  showProgress = true,
  className
}) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const nodes = useMemo(() => createFlowNodes(result), [result]);
  
  const handleNodeClick = (node: FlowNode) => {
    if (!interactive) return;
    
    setSelectedNodeId(node.id);
    onNodeClick?.(node.id, node.data);
    onStepSelect?.(node.type);
  };

  const handlePlayAnimation = () => {
    setIsPlaying(true);
    setCurrentStep(0);
    
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= nodes.length - 1) {
          setIsPlaying(false);
          clearInterval(interval);
          return prev;
        }
        return prev + 1;
      });
    }, 1000);
  };

  const handleReset = () => {
    setIsPlaying(false);
    setCurrentStep(0);
    setSelectedNodeId(null);
  };

  // Calculate layout positions
  const nodeSpacing = 300;
  const levelSpacing = 150;
  const containerWidth = Math.max(...nodes.map(n => n.position.x)) * nodeSpacing + 300;
  const containerHeight = Math.max(...nodes.map(n => Math.abs(n.position.y))) * levelSpacing * 2 + 300;

  return (
    <div className={cn("relative", className)}>
      {/* Controls */}
      {interactive && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-800">Analysis Flow</h3>
            <Badge variant="secondary" className="text-xs">
              {result.analysisType}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant={isPlaying ? "secondary" : "default"}
              size="sm"
              onClick={isPlaying ? () => setIsPlaying(false) : handlePlayAnimation}
              className="flex items-center gap-2"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              {isPlaying ? 'Pause' : 'Play'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Flow Diagram */}
      <div 
        className="relative bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl p-8 border border-slate-200"
        style={{ 
          width: containerWidth, 
          height: containerHeight,
          minHeight: '400px'
        }}
      >
        {/* Render connections */}
        {nodes.map(node => 
          node.connections.map(connectionId => {
            const targetNode = nodes.find(n => n.id === connectionId);
            if (!targetNode) return null;
            
            const fromPos = {
              x: node.position.x * nodeSpacing + 128, // Half node width
              y: node.position.y * levelSpacing + 200 + 50 // Center of node
            };
            
            const toPos = {
              x: targetNode.position.x * nodeSpacing + 128,
              y: targetNode.position.y * levelSpacing + 200 + 50
            };
            
            return (
              <FlowConnection
                key={`${node.id}-${connectionId}`}
                from={fromPos}
                to={toPos}
                animated={animated}
                type={targetNode.type === 'branch' ? 'branch' : 'flow'}
              />
            );
          })
        )}

        {/* Render nodes */}
        <AnimatePresence>
          {nodes.map((node, index) => (
            <div
              key={node.id}
              className="absolute"
              style={{
                left: node.position.x * nodeSpacing,
                top: node.position.y * levelSpacing + 200,
                zIndex: 10
              }}
            >
              <FlowNodeComponent
                node={node}
                isSelected={selectedNodeId === node.id}
                isActive={isPlaying && index === currentStep}
                onClick={() => handleNodeClick(node)}
                animated={animated}
                index={index}
              />
            </div>
          ))}
        </AnimatePresence>
      </div>

      {/* Progress indicator */}
      {showProgress && isPlaying && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 text-center"
        >
          <div className="inline-flex items-center gap-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
            <Zap className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              Step {currentStep + 1} of {nodes.length}: {nodes[currentStep]?.title}
            </span>
          </div>
        </motion.div>
      )}
    </div>
  );
};
