/**
 * Advanced Node Connection System
 * 
 * A sophisticated node-based system with smart connectors, automatic routing,
 * and professional styling for both analysis data and design elements.
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Plus, 
  Minus, 
  Move, 
  Link, 
  Unlink,
  Settings,
  Eye,
  EyeOff,
  Zap,
  GitBranch,
  Network
} from 'lucide-react';

// Node types and interfaces
export interface NodeData {
  id: string;
  type: NodeType;
  title: string;
  content: unknown;
  metadata: NodeMetadata;
  style: NodeStyle;
  position: Position;
  size: Size;
  ports: NodePort[];
}

export interface NodePort {
  id: string;
  type: 'input' | 'output';
  dataType: string;
  label: string;
  position: 'top' | 'right' | 'bottom' | 'left';
  connected: boolean;
  connectionIds: string[];
}

export interface Connection {
  id: string;
  sourceNodeId: string;
  sourcePortId: string;
  targetNodeId: string;
  targetPortId: string;
  style: ConnectionStyle;
  data?: unknown;
  metadata: ConnectionMetadata;
}

export interface ConnectionStyle {
  color: string;
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
  animated: boolean;
  curvature: number;
  arrowType: 'none' | 'arrow' | 'diamond' | 'circle';
}

export interface ConnectionMetadata {
  createdAt: number;
  dataFlow: string;
  weight: number;
  bidirectional: boolean;
}

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface NodeMetadata {
  createdAt: number;
  category: string;
  tags: string[];
  analysisId?: string;
  designElementId?: string;
}

export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  borderWidth: number;
  borderRadius: number;
  textColor: string;
  shadow: string;
  opacity: number;
}

export type NodeType = 
  | 'analysis-input' 
  | 'analysis-process' 
  | 'analysis-output'
  | 'design-element'
  | 'data-source'
  | 'transformer'
  | 'visualizer'
  | 'export';

interface AdvancedNodeSystemProps {
  nodes: NodeData[];
  connections: Connection[];
  onNodesChange: (nodes: NodeData[]) => void;
  onConnectionsChange: (connections: Connection[]) => void;
  onNodeSelect?: (nodeId: string | null) => void;
  onConnectionSelect?: (connectionId: string | null) => void;
  className?: string;
  readOnly?: boolean;
  showGrid?: boolean;
  snapToGrid?: boolean;
  gridSize?: number;
}

export const AdvancedNodeSystem: React.FC<AdvancedNodeSystemProps> = ({
  nodes,
  connections,
  onNodesChange,
  onConnectionsChange,
  onNodeSelect,
  onConnectionSelect,
  className,
  readOnly = false,
  showGrid = true,
  snapToGrid = true,
  gridSize = 20,
}) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | null>(null);
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);
  const [connectionMode, setConnectionMode] = useState(false);
  const [connectionStart, setConnectionStart] = useState<{nodeId: string; portId: string} | null>(null);
  const [mousePosition, setMousePosition] = useState<Position>({ x: 0, y: 0 });
  const [viewportOffset, setViewportOffset] = useState<Position>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);

  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  // Handle node selection
  const handleNodeSelect = useCallback((nodeId: string | null) => {
    setSelectedNodeId(nodeId);
    onNodeSelect?.(nodeId);
  }, [onNodeSelect]);

  // Handle connection selection
  const handleConnectionSelect = useCallback((connectionId: string | null) => {
    setSelectedConnectionId(connectionId);
    onConnectionSelect?.(connectionId);
  }, [onConnectionSelect]);

  // Smart connection routing algorithm
  const calculateConnectionPath = useCallback((connection: Connection): string => {
    const sourceNode = nodes.find(n => n.id === connection.sourceNodeId);
    const targetNode = nodes.find(n => n.id === connection.targetNodeId);
    
    if (!sourceNode || !targetNode) return '';

    const sourcePort = sourceNode.ports.find(p => p.id === connection.sourcePortId);
    const targetPort = targetNode.ports.find(p => p.id === connection.targetPortId);
    
    if (!sourcePort || !targetPort) return '';

    // Calculate port positions
    const sourcePos = getPortPosition(sourceNode, sourcePort);
    const targetPos = getPortPosition(targetNode, targetPort);

    // Smart routing with obstacle avoidance
    const path = calculateSmartPath(sourcePos, targetPos, sourcePort, targetPort, nodes, connection.style.curvature);
    
    return path;
  }, [nodes]);

  // Get absolute position of a port
  const getPortPosition = (node: NodeData, port: NodePort): Position => {
    const portOffsets = {
      top: { x: node.size.width / 2, y: 0 },
      right: { x: node.size.width, y: node.size.height / 2 },
      bottom: { x: node.size.width / 2, y: node.size.height },
      left: { x: 0, y: node.size.height / 2 }
    };

    const offset = portOffsets[port.position];
    return {
      x: node.position.x + offset.x,
      y: node.position.y + offset.y
    };
  };

  // Smart path calculation with obstacle avoidance
  const calculateSmartPath = (
    start: Position,
    end: Position,
    sourcePort: NodePort,
    targetPort: NodePort,
    obstacles: NodeData[],
    curvature: number
  ): string => {
    // Simple bezier curve for now - could be enhanced with A* pathfinding
    const dx = end.x - start.x;
    const dy = end.y - start.y;
    
    // Control points based on port directions
    const cp1 = getControlPoint(start, sourcePort.position, Math.abs(dx) * curvature);
    const cp2 = getControlPoint(end, getOppositeDirection(targetPort.position), Math.abs(dx) * curvature);

    return `M ${start.x} ${start.y} C ${cp1.x} ${cp1.y}, ${cp2.x} ${cp2.y}, ${end.x} ${end.y}`;
  };

  const getControlPoint = (point: Position, direction: string, distance: number): Position => {
    const directions = {
      top: { x: 0, y: -distance },
      right: { x: distance, y: 0 },
      bottom: { x: 0, y: distance },
      left: { x: -distance, y: 0 }
    };

    const offset = directions[direction as keyof typeof directions] || { x: 0, y: 0 };
    return {
      x: point.x + offset.x,
      y: point.y + offset.y
    };
  };

  const getOppositeDirection = (direction: string): string => {
    const opposites = {
      top: 'bottom',
      right: 'left',
      bottom: 'top',
      left: 'right'
    };
    return opposites[direction as keyof typeof opposites] || direction;
  };

  // Handle node dragging
  const handleNodeDrag = useCallback((nodeId: string, newPosition: Position) => {
    if (readOnly) return;

    const updatedNodes = nodes.map(node => 
      node.id === nodeId 
        ? { 
            ...node, 
            position: snapToGrid 
              ? {
                  x: Math.round(newPosition.x / gridSize) * gridSize,
                  y: Math.round(newPosition.y / gridSize) * gridSize
                }
              : newPosition
          }
        : node
    );
    
    onNodesChange(updatedNodes);
  }, [nodes, onNodesChange, readOnly, snapToGrid, gridSize]);

  // Handle connection creation
  const handlePortClick = useCallback((nodeId: string, portId: string) => {
    if (readOnly) return;

    if (!connectionMode) {
      setConnectionMode(true);
      setConnectionStart({ nodeId, portId });
    } else if (connectionStart) {
      // Create new connection
      const sourceNode = nodes.find(n => n.id === connectionStart.nodeId);
      const targetNode = nodes.find(n => n.id === nodeId);
      const sourcePort = sourceNode?.ports.find(p => p.id === connectionStart.portId);
      const targetPort = targetNode?.ports.find(p => p.id === portId);

      if (sourcePort && targetPort && sourcePort.type !== targetPort.type) {
        const newConnection: Connection = {
          id: `connection-${Date.now()}`,
          sourceNodeId: connectionStart.nodeId,
          sourcePortId: connectionStart.portId,
          targetNodeId: nodeId,
          targetPortId: portId,
          style: {
            color: '#3b82f6',
            width: 2,
            style: 'solid',
            animated: true,
            curvature: 0.5,
            arrowType: 'arrow'
          },
          metadata: {
            createdAt: Date.now(),
            dataFlow: `${sourcePort.dataType} -> ${targetPort.dataType}`,
            weight: 1,
            bidirectional: false
          }
        };

        onConnectionsChange([...connections, newConnection]);
      }

      setConnectionMode(false);
      setConnectionStart(null);
    }
  }, [connectionMode, connectionStart, nodes, connections, onConnectionsChange, readOnly]);

  // Render node component
  const renderNode = (node: NodeData) => {
    const isSelected = selectedNodeId === node.id;
    const isDragged = draggedNodeId === node.id;

    return (
      <motion.div
        key={node.id}
        className={cn(
          "absolute cursor-move select-none",
          isSelected && "ring-2 ring-blue-400 ring-offset-2",
          isDragged && "z-50"
        )}
        style={{
          left: node.position.x,
          top: node.position.y,
          width: node.size.width,
          height: node.size.height,
        }}
        drag={!readOnly}
        dragMomentum={false}
        onDragStart={() => setDraggedNodeId(node.id)}
        onDragEnd={() => setDraggedNodeId(null)}
        onDrag={(_, info) => {
          const newPosition = {
            x: node.position.x + info.offset.x,
            y: node.position.y + info.offset.y
          };
          handleNodeDrag(node.id, newPosition);
        }}
        onClick={() => handleNodeSelect(node.id)}
        whileHover={{ scale: 1.02 }}
        whileDrag={{ scale: 1.05, zIndex: 1000 }}
      >
        <Card 
          className="h-full shadow-lg"
          style={{
            backgroundColor: node.style.backgroundColor,
            borderColor: node.style.borderColor,
            borderWidth: node.style.borderWidth,
            borderRadius: node.style.borderRadius,
            opacity: node.style.opacity
          }}
        >
          <CardContent className="p-3 h-full flex flex-col">
            {/* Node Header */}
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-sm truncate" style={{ color: node.style.textColor }}>
                {node.title}
              </h4>
              <Badge variant="outline" className="text-xs">
                {node.type}
              </Badge>
            </div>

            {/* Node Content */}
            <div className="flex-1 text-xs" style={{ color: node.style.textColor }}>
              {typeof node.content === 'string' ? (
                <p className="line-clamp-3">{node.content}</p>
              ) : (
                <div className="space-y-1">
                  {Object.entries(node.content).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="font-medium">{key}:</span>
                      <span className="truncate ml-1">{String(value)}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Node Ports */}
            {node.ports.map(port => (
              <div
                key={port.id}
                className={cn(
                  "absolute w-3 h-3 rounded-full border-2 cursor-pointer",
                  "hover:scale-125 transition-transform",
                  port.connected ? "bg-green-500 border-green-600" : "bg-gray-300 border-gray-400",
                  connectionMode && connectionStart?.nodeId !== node.id && "ring-2 ring-blue-400"
                )}
                style={{
                  [port.position]: -6,
                  ...(port.position === 'top' || port.position === 'bottom' 
                    ? { left: '50%', transform: 'translateX(-50%)' }
                    : { top: '50%', transform: 'translateY(-50%)' }
                  )
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePortClick(node.id, port.id);
                }}
                title={`${port.label} (${port.dataType})`}
              />
            ))}
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Render connection component
  const renderConnection = (connection: Connection) => {
    const path = calculateConnectionPath(connection);
    const isSelected = selectedConnectionId === connection.id;

    return (
      <g key={connection.id}>
        {/* Connection path */}
        <path
          d={path}
          stroke={connection.style.color}
          strokeWidth={connection.style.width}
          strokeDasharray={connection.style.style === 'dashed' ? '5,5' : connection.style.style === 'dotted' ? '2,2' : 'none'}
          fill="none"
          className={cn(
            "cursor-pointer hover:stroke-width-3 transition-all",
            isSelected && "stroke-width-4",
            connection.style.animated && "animate-pulse"
          )}
          onClick={() => handleConnectionSelect(connection.id)}
        />

        {/* Arrow marker */}
        {connection.style.arrowType === 'arrow' && (
          <defs>
            <marker
              id={`arrow-${connection.id}`}
              viewBox="0 0 10 10"
              refX="9"
              refY="3"
              markerWidth="6"
              markerHeight="6"
              orient="auto"
            >
              <path d="M0,0 L0,6 L9,3 z" fill={connection.style.color} />
            </marker>
          </defs>
        )}
      </g>
    );
  };

  return (
    <div 
      ref={canvasRef}
      className={cn(
        "relative w-full h-full overflow-hidden bg-gray-50",
        showGrid && "bg-grid-pattern",
        className
      )}
      style={{
        backgroundSize: showGrid ? `${gridSize}px ${gridSize}px` : undefined,
        transform: `scale(${zoom}) translate(${viewportOffset.x}px, ${viewportOffset.y}px)`
      }}
    >
      {/* Grid background */}
      {showGrid && (
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(to right, #e5e7eb 1px, transparent 1px),
              linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
            `,
            backgroundSize: `${gridSize}px ${gridSize}px`
          }}
        />
      )}

      {/* SVG for connections */}
      <svg
        ref={svgRef}
        className="absolute inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 1 }}
      >
        {connections.map(renderConnection)}
        
        {/* Temporary connection line during creation */}
        {connectionMode && connectionStart && (
          <line
            x1={getPortPosition(
              nodes.find(n => n.id === connectionStart.nodeId)!,
              nodes.find(n => n.id === connectionStart.nodeId)!.ports.find(p => p.id === connectionStart.portId)!
            ).x}
            y1={getPortPosition(
              nodes.find(n => n.id === connectionStart.nodeId)!,
              nodes.find(n => n.id === connectionStart.nodeId)!.ports.find(p => p.id === connectionStart.portId)!
            ).y}
            x2={mousePosition.x}
            y2={mousePosition.y}
            stroke="#3b82f6"
            strokeWidth="2"
            strokeDasharray="5,5"
            className="pointer-events-none"
          />
        )}
      </svg>

      {/* Nodes */}
      <div className="relative" style={{ zIndex: 2 }}>
        {nodes.map(renderNode)}
      </div>

      {/* Controls */}
      <div className="absolute top-4 right-4 flex flex-col gap-2" style={{ zIndex: 10 }}>
        <Button
          variant={connectionMode ? "default" : "outline"}
          size="sm"
          onClick={() => {
            setConnectionMode(!connectionMode);
            setConnectionStart(null);
          }}
          disabled={readOnly}
        >
          <Link className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setZoom(zoom * 1.2)}
        >
          <Plus className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setZoom(zoom * 0.8)}
        >
          <Minus className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Node factory functions
export const createAnalysisInputNode = (
  id: string,
  question: string,
  position: Position,
  analysisId?: string
): NodeData => ({
  id,
  type: 'analysis-input',
  title: 'Analysis Input',
  content: { question: question.substring(0, 100) + '...' },
  metadata: {
    createdAt: Date.now(),
    category: 'analysis',
    tags: ['input', 'question'],
    analysisId
  },
  style: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#1e40af',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 200, height: 120 },
  ports: [
    {
      id: `${id}-output`,
      type: 'output',
      dataType: 'question',
      label: 'Question',
      position: 'right',
      connected: false,
      connectionIds: []
    }
  ]
});

export const createAnalysisProcessNode = (
  id: string,
  model: string,
  analysisType: string,
  position: Position,
  analysisId?: string
): NodeData => ({
  id,
  type: 'analysis-process',
  title: 'AI Processing',
  content: { model, type: analysisType },
  metadata: {
    createdAt: Date.now(),
    category: 'analysis',
    tags: ['process', 'ai', model],
    analysisId
  },
  style: {
    backgroundColor: '#f3e8ff',
    borderColor: '#8b5cf6',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#6b21a8',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 220, height: 140 },
  ports: [
    {
      id: `${id}-input`,
      type: 'input',
      dataType: 'question',
      label: 'Input',
      position: 'left',
      connected: false,
      connectionIds: []
    },
    {
      id: `${id}-output`,
      type: 'output',
      dataType: 'analysis',
      label: 'Analysis',
      position: 'right',
      connected: false,
      connectionIds: []
    }
  ]
});

export const createAnalysisOutputNode = (
  id: string,
  analysis: string,
  rating: number | undefined,
  position: Position,
  analysisId?: string
): NodeData => ({
  id,
  type: 'analysis-output',
  title: 'Analysis Result',
  content: {
    preview: analysis.substring(0, 80) + '...',
    rating: rating ? `${rating}/10` : 'N/A'
  },
  metadata: {
    createdAt: Date.now(),
    category: 'analysis',
    tags: ['output', 'result'],
    analysisId
  },
  style: {
    backgroundColor: '#dcfce7',
    borderColor: '#22c55e',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#15803d',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 200, height: 120 },
  ports: [
    {
      id: `${id}-input`,
      type: 'input',
      dataType: 'analysis',
      label: 'Analysis',
      position: 'left',
      connected: false,
      connectionIds: []
    },
    {
      id: `${id}-export`,
      type: 'output',
      dataType: 'result',
      label: 'Export',
      position: 'bottom',
      connected: false,
      connectionIds: []
    }
  ]
});

export const createDesignElementNode = (
  id: string,
  elementType: string,
  title: string,
  position: Position,
  designElementId?: string
): NodeData => ({
  id,
  type: 'design-element',
  title: `Design: ${title}`,
  content: { type: elementType, element: title },
  metadata: {
    createdAt: Date.now(),
    category: 'design',
    tags: ['design', elementType],
    designElementId
  },
  style: {
    backgroundColor: '#fef3c7',
    borderColor: '#f59e0b',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#92400e',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 180, height: 100 },
  ports: [
    {
      id: `${id}-input`,
      type: 'input',
      dataType: 'data',
      label: 'Data',
      position: 'left',
      connected: false,
      connectionIds: []
    },
    {
      id: `${id}-output`,
      type: 'output',
      dataType: 'element',
      label: 'Element',
      position: 'right',
      connected: false,
      connectionIds: []
    }
  ]
});

export const createVisualizerNode = (
  id: string,
  chartType: string,
  position: Position
): NodeData => ({
  id,
  type: 'visualizer',
  title: 'Data Visualizer',
  content: { chart: chartType },
  metadata: {
    createdAt: Date.now(),
    category: 'visualization',
    tags: ['chart', chartType]
  },
  style: {
    backgroundColor: '#fce7f3',
    borderColor: '#ec4899',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#be185d',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 160, height: 100 },
  ports: [
    {
      id: `${id}-input`,
      type: 'input',
      dataType: 'data',
      label: 'Data',
      position: 'left',
      connected: false,
      connectionIds: []
    },
    {
      id: `${id}-output`,
      type: 'output',
      dataType: 'visualization',
      label: 'Chart',
      position: 'right',
      connected: false,
      connectionIds: []
    }
  ]
});

export const createExportNode = (
  id: string,
  exportFormat: string,
  position: Position
): NodeData => ({
  id,
  type: 'export',
  title: 'Export',
  content: { format: exportFormat },
  metadata: {
    createdAt: Date.now(),
    category: 'export',
    tags: ['export', exportFormat]
  },
  style: {
    backgroundColor: '#f1f5f9',
    borderColor: '#64748b',
    borderWidth: 2,
    borderRadius: 8,
    textColor: '#334155',
    shadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    opacity: 1
  },
  position,
  size: { width: 140, height: 80 },
  ports: [
    {
      id: `${id}-input`,
      type: 'input',
      dataType: 'any',
      label: 'Input',
      position: 'left',
      connected: false,
      connectionIds: []
    }
  ]
});

export default AdvancedNodeSystem;
