
import React from "react";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
    console.log('🛡️ ErrorBoundary initialized');
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🚨 ErrorBoundary - getDerivedStateFromError:', error);
    return { hasError: true, error: error };
  }

  componentDidCatch(error: Error, info: React.ErrorInfo) {
    // You can log the error to an error reporting service here
    // For now, just log to the console
    console.error("🚨 App ErrorBoundary caught an error:");
    console.error("Error:", error);
    console.error("Error Info:", info);
    console.error("Component Stack:", info.componentStack);
    console.error("Error Stack:", error.stack);
  }

  handleReset = () => {
    // Optionally, reset error state to try to recover
    this.setState({ hasError: false, error: null });
    // Optionally, you could reload or redirect
    // window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-background text-center p-8">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong</h2>
          <p className="text-md text-muted-foreground mb-4">
            {this.state.error?.message || "Unexpected application error."}
          </p>
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={this.handleReset}
          >
            Try Again
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}
