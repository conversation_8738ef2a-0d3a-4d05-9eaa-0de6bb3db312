import { useRef, useCallback, useEffect } from 'react';

interface WorkerMessage {
  id: string;
  type: string;
  data: unknown;
}

interface WorkerResponse {
  id: string;
  type: string;
  data: unknown;
  error?: string;
}

interface PendingTask {
  resolve: (value: unknown) => void;
  reject: (error: Error) => void;
  timestamp: number;
}

/**
 * Hook for using web workers with Promise-based API
 */
export const useWebWorker = (workerPath: string) => {
  const workerRef = useRef<Worker | null>(null);
  const pendingTasksRef = useRef<Map<string, PendingTask>>(new Map());
  const taskIdCounterRef = useRef(0);

  // Initialize worker
  useEffect(() => {
    if (typeof Worker !== 'undefined') {
      try {
        workerRef.current = new Worker(new URL(workerPath, import.meta.url));
        
        workerRef.current.onmessage = (event: MessageEvent<WorkerResponse>) => {
          const { id, data, error } = event.data;
          const pendingTask = pendingTasksRef.current.get(id);
          
          if (pendingTask) {
            pendingTasksRef.current.delete(id);
            
            if (error) {
              pendingTask.reject(new Error(error));
            } else {
              pendingTask.resolve(data);
            }
          }
        };
        
        workerRef.current.onerror = (error) => {
          console.error('Worker error:', error);
          // Reject all pending tasks
          pendingTasksRef.current.forEach(task => {
            task.reject(new Error('Worker error'));
          });
          pendingTasksRef.current.clear();
        };
        
      } catch (error) {
        console.error('Failed to create worker:', error);
      }
    }

    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
        workerRef.current = null;
      }
      // Reject all pending tasks
      pendingTasksRef.current.forEach(task => {
        task.reject(new Error('Worker terminated'));
      });
      pendingTasksRef.current.clear();
    };
  }, [workerPath]);

  // Execute task in worker
  const executeTask = useCallback(<T = unknown>(type: string, data: unknown, timeout = 30000): Promise<T> => {
    return new Promise((resolve, reject) => {
      if (!workerRef.current) {
        reject(new Error('Worker not available'));
        return;
      }

      const id = `task_${++taskIdCounterRef.current}`;
      const timestamp = Date.now();
      
      // Store pending task
      pendingTasksRef.current.set(id, { resolve, reject, timestamp });
      
      // Set timeout
      const timeoutId = setTimeout(() => {
        const task = pendingTasksRef.current.get(id);
        if (task) {
          pendingTasksRef.current.delete(id);
          reject(new Error(`Task timeout: ${type}`));
        }
      }, timeout);
      
      // Clear timeout when task completes
      const originalResolve = resolve;
      const originalReject = reject;
      
      pendingTasksRef.current.set(id, {
        resolve: (value) => {
          clearTimeout(timeoutId);
          originalResolve(value as T);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          originalReject(error);
        },
        timestamp
      });
      
      // Send message to worker
      const message: WorkerMessage = { id, type, data };
      workerRef.current.postMessage(message);
    });
  }, []);

  // Check if worker is available
  const isWorkerAvailable = useCallback(() => {
    return workerRef.current !== null && typeof Worker !== 'undefined';
  }, []);

  // Get pending task count
  const getPendingTaskCount = useCallback(() => {
    return pendingTasksRef.current.size;
  }, []);

  // Clear old pending tasks
  const clearOldTasks = useCallback((maxAge = 60000) => {
    const now = Date.now();
    const tasksToRemove: string[] = [];
    
    pendingTasksRef.current.forEach((task, id) => {
      if (now - task.timestamp > maxAge) {
        task.reject(new Error('Task expired'));
        tasksToRemove.push(id);
      }
    });
    
    tasksToRemove.forEach(id => {
      pendingTasksRef.current.delete(id);
    });
    
    return tasksToRemove.length;
  }, []);

  return {
    executeTask,
    isWorkerAvailable,
    getPendingTaskCount,
    clearOldTasks
  };
};

/**
 * Hook specifically for data processing worker
 */
export const useDataProcessingWorker = () => {
  const { executeTask, isWorkerAvailable, getPendingTaskCount } = useWebWorker('/src/workers/dataProcessingWorker.ts');

  const processAnalysisResults = useCallback((results: unknown[]) => {
    return executeTask('processAnalysisResults', results);
  }, [executeTask]);

  const calculateSimilarities = useCallback((results: unknown[]) => {
    return executeTask('calculateSimilarities', results);
  }, [executeTask]);

  const calculateForceDirectedLayout = useCallback((nodes: unknown[], connections: unknown[], parameters: unknown) => {
    return executeTask('calculateForceDirectedLayout', { nodes, connections, parameters });
  }, [executeTask]);

  const processSearch = useCallback((query: string, items: unknown[], fields: string[]) => {
    return executeTask('processSearch', { query, items, fields });
  }, [executeTask]);

  return {
    processAnalysisResults,
    calculateSimilarities,
    calculateForceDirectedLayout,
    processSearch,
    isWorkerAvailable,
    getPendingTaskCount
  };
};

/**
 * Hook for batch processing with progress tracking
 */
export const useBatchProcessor = () => {
  const { executeTask, isWorkerAvailable } = useWebWorker('/src/workers/dataProcessingWorker.ts');

  const processBatch = useCallback(async <T>(
    items: T[],
    batchSize: number,
    processor: (batch: T[]) => Promise<unknown>,
    onProgress?: (progress: number, completed: number, total: number) => void
  ): Promise<unknown[]> => {
    const results: unknown[] = [];
    const totalBatches = Math.ceil(items.length / batchSize);
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResult = await processor(batch);
      results.push(batchResult);
      
      if (onProgress) {
        const completed = Math.min(i + batchSize, items.length);
        const progress = (completed / items.length) * 100;
        onProgress(progress, completed, items.length);
      }
      
      // Small delay to prevent blocking
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    return results;
  }, []);

  const processWithWorker = useCallback(async <T>(
    type: string,
    items: T[],
    batchSize: number = 100,
    onProgress?: (progress: number) => void
  ): Promise<unknown[]> => {
    if (!isWorkerAvailable()) {
      throw new Error('Web worker not available');
    }

    return processBatch(
      items,
      batchSize,
      (batch) => executeTask(type, batch),
      onProgress
    );
  }, [executeTask, isWorkerAvailable, processBatch]);

  return {
    processBatch,
    processWithWorker,
    isWorkerAvailable
  };
};

/**
 * Hook for performance-aware processing
 */
export const usePerformanceAwareProcessor = () => {
  const { executeTask, isWorkerAvailable, getPendingTaskCount } = useWebWorker('/src/workers/dataProcessingWorker.ts');

  const processWithFallback = useCallback(async <T>(
    type: string,
    data: unknown,
    fallbackProcessor?: (data: unknown) => T,
    threshold: number = 1000
  ): Promise<T> => {
    // Use worker for large datasets or when main thread is busy
    const shouldUseWorker = isWorkerAvailable() && (
      (Array.isArray(data) && data.length > threshold) ||
      getPendingTaskCount() < 3
    );

    if (shouldUseWorker) {
      try {
        return await executeTask(type, data, 10000); // 10 second timeout
      } catch (error) {
        console.warn('Worker processing failed, falling back to main thread:', error);
      }
    }

    // Fallback to main thread processing
    if (fallbackProcessor) {
      return fallbackProcessor(data);
    }

    throw new Error('No processing method available');
  }, [executeTask, isWorkerAvailable, getPendingTaskCount]);

  return {
    processWithFallback,
    isWorkerAvailable,
    getPendingTaskCount
  };
};
