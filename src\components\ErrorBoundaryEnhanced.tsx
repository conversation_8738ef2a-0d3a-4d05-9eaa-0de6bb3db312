/**
 * Enhanced Error Boundary with Comprehensive Logging
 * 
 * Provides detailed error capture, logging, and integration with the error store.
 * Includes fallback UI and recovery mechanisms.
 */

import React, { Component, ReactNode } from 'react';
import { useErrorStore } from '@/stores/useErrorStore';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  context?: string;
  level?: 'page' | 'component' | 'feature';
  showDetails?: boolean;
  maxRetries?: number;
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo: React.ErrorInfo | null;
  retry: () => void;
  goHome: () => void;
  context?: string;
  retryCount: number;
  maxRetries: number;
}

class ErrorBoundaryEnhanced extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private errorStore: ReturnType<typeof useErrorStore.getState>;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };

    // Get error store instance
    this.errorStore = useErrorStore.getState();
    
    console.log(`🛡️ ErrorBoundaryEnhanced initialized for context: ${props.context || 'unknown'}`);
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    console.error('🚨 ErrorBoundaryEnhanced - getDerivedStateFromError:', error);
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { context = 'Unknown', level = 'component', onError } = this.props;
    
    console.group('🚨 ErrorBoundaryEnhanced - Error Caught');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Error Stack:', error.stack);
    console.error('Context:', context);
    console.error('Level:', level);
    console.error('Props:', this.props);
    console.error('State:', this.state);
    console.error('Retry Count:', this.state.retryCount);
    console.groupEnd();

    // Add to error store
    const errorId = this.errorStore.addError({
      message: error.message,
      severity: level === 'page' ? 'critical' : level === 'feature' ? 'high' : 'medium',
      category: 'system',
      context: `ErrorBoundary: ${context}`,
      stack: error.stack,
      metadata: {
        componentStack: errorInfo.componentStack,
        level,
        retryCount: this.state.retryCount,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        props: this.props,
      },
    });

    this.setState({
      errorInfo,
      errorId,
    });

    // Call custom error handler if provided
    if (onError) {
      try {
        onError(error, errorInfo);
      } catch (handlerError) {
        console.error('🚨 Error in custom error handler:', handlerError);
      }
    }

    // Report to external services in production
    if (process.env.NODE_ENV === 'production') {
      this.reportErrorToService(error, errorInfo, context);
    }
  }

  private reportErrorToService = (error: Error, errorInfo: React.ErrorInfo, context: string) => {
    // This would integrate with error reporting services like Sentry, LogRocket, etc.
    console.log('📊 Would report error to external service:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      context,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const newRetryCount = this.state.retryCount + 1;
    
    console.log(`🔄 ErrorBoundary retry attempt ${newRetryCount}/${maxRetries}`);
    
    if (newRetryCount <= maxRetries) {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: newRetryCount,
      });
    } else {
      console.warn('🚨 Max retry attempts reached');
    }
  };

  private handleGoHome = () => {
    console.log('🏠 ErrorBoundary - navigating to home');
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const { fallback: CustomFallback, context, maxRetries = 3, showDetails = true } = this.props;
      
      if (CustomFallback) {
        return (
          <CustomFallback
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            retry={this.handleRetry}
            goHome={this.handleGoHome}
            context={context}
            retryCount={this.state.retryCount}
            maxRetries={maxRetries}
          />
        );
      }

      return (
        <DefaultErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          retry={this.handleRetry}
          goHome={this.handleGoHome}
          context={context}
          retryCount={this.state.retryCount}
          maxRetries={maxRetries}
          showDetails={showDetails}
        />
      );
    }

    return this.props.children;
  }
}

// Default error fallback component
const DefaultErrorFallback: React.FC<ErrorFallbackProps & { showDetails?: boolean }> = ({
  error,
  errorInfo,
  retry,
  goHome,
  context,
  retryCount,
  maxRetries,
  showDetails = true,
}) => {
  const canRetry = retryCount < maxRetries;

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Something went wrong
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p className="text-muted-foreground">
              {context ? `An error occurred in ${context}` : 'An unexpected error occurred'}
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Error: {error.message}
            </p>
          </div>

          {showDetails && process.env.NODE_ENV === 'development' && (
            <details className="text-xs">
              <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                <Bug className="inline h-3 w-3 mr-1" />
                Technical Details
              </summary>
              <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
                <div><strong>Error:</strong> {error.message}</div>
                <div><strong>Context:</strong> {context || 'Unknown'}</div>
                <div><strong>Retry Count:</strong> {retryCount}/{maxRetries}</div>
                {error.stack && (
                  <div className="mt-2">
                    <strong>Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">{error.stack}</pre>
                  </div>
                )}
                {errorInfo?.componentStack && (
                  <div className="mt-2">
                    <strong>Component Stack:</strong>
                    <pre className="whitespace-pre-wrap text-xs">{errorInfo.componentStack}</pre>
                  </div>
                )}
              </div>
            </details>
          )}

          <div className="flex gap-2">
            {canRetry && (
              <Button onClick={retry} variant="default" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again ({maxRetries - retryCount} left)
              </Button>
            )}
            <Button onClick={goHome} variant="outline" size="sm">
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export { ErrorBoundaryEnhanced, type ErrorBoundaryProps, type ErrorFallbackProps };
export default ErrorBoundaryEnhanced;
