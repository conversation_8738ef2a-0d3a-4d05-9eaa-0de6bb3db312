import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  Save, 
  Star, 
  Clock, 
  Tag, 
  Brain,
  Zap,
  X,
  Plus,
  BookOpen,
  TrendingUp
} from 'lucide-react';
import { SavedAnalysis, AnalysisResult } from '@/types/conversation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export interface SearchFilter {
  id: string;
  name: string;
  type: 'text' | 'date' | 'rating' | 'tag' | 'analysisType' | 'style';
  value: unknown;
  operator: 'equals' | 'contains' | 'greater' | 'less' | 'between' | 'in';
}

export interface SavedSearch {
  id: string;
  name: string;
  query: string;
  filters: SearchFilter[];
  createdAt: Date;
  lastUsed: Date;
  useCount: number;
  isStarred: boolean;
}

export interface SearchResult {
  analysis: SavedAnalysis;
  relevanceScore: number;
  matchedFields: string[];
  highlights: Record<string, string>;
}

interface AdvancedSearchSystemProps {
  analyses: SavedAnalysis[];
  onSearchResults?: (results: SearchResult[]) => void;
  onSaveSearch?: (search: Omit<SavedSearch, 'id'>) => void;
  savedSearches?: SavedSearch[];
  enableSemanticSearch?: boolean;
}

export const AdvancedSearchSystem: React.FC<AdvancedSearchSystemProps> = ({
  analyses,
  onSearchResults,
  onSaveSearch,
  savedSearches = [],
  enableSemanticSearch = true,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>([]);
  const [searchMode, setSearchMode] = useState<'simple' | 'advanced' | 'semantic'>('simple');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');

  // Extract all unique tags and values for faceted filtering
  const facetData = useMemo(() => {
    const tags = new Set<string>();
    const analysisTypes = new Set<string>();
    const styles = new Set<string>();
    const models = new Set<string>();

    analyses.forEach(analysis => {
      analysis.results.forEach(result => {
        if (result.analysisType) analysisTypes.add(result.analysisType);
        if (result.style) styles.add(result.style);
        if (result.model) models.add(result.model);
        // Extract tags from analysis content (simple keyword extraction)
        const words = result.analysis.toLowerCase().match(/\b\w{4,}\b/g) || [];
        words.slice(0, 10).forEach(word => tags.add(word));
      });
    });

    return {
      tags: Array.from(tags).slice(0, 20), // Limit to top 20 tags
      analysisTypes: Array.from(analysisTypes),
      styles: Array.from(styles),
      models: Array.from(models),
    };
  }, [analyses]);

  // Perform semantic search using simple keyword matching and relevance scoring
  const performSemanticSearch = useCallback((query: string, items: SavedAnalysis[]): SearchResult[] => {
    if (!query.trim()) return [];

    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
    const results: SearchResult[] = [];

    items.forEach(analysis => {
      let totalScore = 0;
      const matchedFields: string[] = [];
      const highlights: Record<string, string> = {};

      // Search in title
      const titleScore = calculateFieldScore(analysis.title, queryTerms);
      if (titleScore > 0) {
        totalScore += titleScore * 3; // Title has higher weight
        matchedFields.push('title');
        highlights.title = highlightMatches(analysis.title, queryTerms);
      }

      // Search in analysis results
      analysis.results.forEach((result, index) => {
        // Search in question
        const questionScore = calculateFieldScore(result.question, queryTerms);
        if (questionScore > 0) {
          totalScore += questionScore * 2;
          matchedFields.push(`question_${index}`);
          highlights[`question_${index}`] = highlightMatches(result.question, queryTerms);
        }

        // Search in analysis content
        const analysisScore = calculateFieldScore(result.analysis, queryTerms);
        if (analysisScore > 0) {
          totalScore += analysisScore;
          matchedFields.push(`analysis_${index}`);
          highlights[`analysis_${index}`] = highlightMatches(result.analysis, queryTerms, 150);
        }

        // Search in follow-up questions
        if (result.followUpQuestions) {
          result.followUpQuestions.forEach((followUp, fIndex) => {
            const followUpScore = calculateFieldScore(followUp, queryTerms);
            if (followUpScore > 0) {
              totalScore += followUpScore * 0.5;
              matchedFields.push(`followUp_${index}_${fIndex}`);
            }
          });
        }
      });

      if (totalScore > 0) {
        results.push({
          analysis,
          relevanceScore: totalScore,
          matchedFields,
          highlights,
        });
      }
    });

    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }, []);

  // Calculate relevance score for a field
  const calculateFieldScore = (text: string, queryTerms: string[]): number => {
    if (!text) return 0;
    
    const lowerText = text.toLowerCase();
    let score = 0;

    queryTerms.forEach(term => {
      const termCount = (lowerText.match(new RegExp(term, 'g')) || []).length;
      if (termCount > 0) {
        // Boost score for exact matches and frequency
        score += termCount * (term.length / 10) * (lowerText.includes(term) ? 2 : 1);
      }
    });

    return score;
  };

  // Highlight matching terms in text
  const highlightMatches = (text: string, queryTerms: string[], maxLength?: number): string => {
    let highlightedText = text;
    
    queryTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
    });

    if (maxLength && highlightedText.length > maxLength) {
      // Find the first highlight and show context around it
      const markIndex = highlightedText.indexOf('<mark>');
      if (markIndex !== -1) {
        const start = Math.max(0, markIndex - 50);
        const end = Math.min(highlightedText.length, markIndex + maxLength - 50);
        highlightedText = '...' + highlightedText.substring(start, end) + '...';
      } else {
        highlightedText = highlightedText.substring(0, maxLength) + '...';
      }
    }

    return highlightedText;
  };

  // Apply filters to search results
  const applyFilters = useCallback((items: SavedAnalysis[]): SavedAnalysis[] => {
    if (activeFilters.length === 0) return items;

    return items.filter(analysis => {
      return activeFilters.every(filter => {
        switch (filter.type) {
          case 'analysisType':
            return analysis.results.some(result => result.analysisType === filter.value);
          case 'style':
            return analysis.results.some(result => result.style === filter.value);
          case 'rating': {
            const avgRating = analysis.results.reduce((sum, r) => sum + (r.rating || 0), 0) / analysis.results.length;
            if (typeof filter.value === 'number') {
              return filter.operator === 'greater' ? avgRating >= filter.value : avgRating <= filter.value;
            }
            return true;
          }
          case 'date': {
            const analysisDate = new Date(analysis.createdAt);
            if (typeof filter.value === 'string' || typeof filter.value === 'number' || filter.value instanceof Date) {
              const filterDate = new Date(filter.value);
              return filter.operator === 'greater' ? analysisDate >= filterDate : analysisDate <= filterDate;
            }
            return true;
          }
          case 'text':
            if (typeof filter.value === 'string') {
              const searchText = `${analysis.title} ${analysis.results.map(r => r.question + ' ' + r.analysis).join(' ')}`.toLowerCase();
              return searchText.includes(filter.value.toLowerCase());
            }
            return true;
          default:
            return true;
        }
      });
    });
  }, [activeFilters]);

  // Perform search
  const performSearch = useCallback(async () => {
    setIsSearching(true);

    try {
      // Apply filters first
      const filteredAnalyses = applyFilters(analyses);

      let results: SearchResult[];

      if (searchMode === 'semantic' && enableSemanticSearch && searchQuery.trim()) {
        results = performSemanticSearch(searchQuery, filteredAnalyses);
      } else if (searchQuery.trim()) {
        // Simple text search
        const simpleResults = filteredAnalyses.filter(analysis => {
          const searchText = `${analysis.title} ${analysis.results.map(r => r.question + ' ' + r.analysis).join(' ')}`.toLowerCase();
          return searchText.includes(searchQuery.toLowerCase());
        });

        results = simpleResults.map(analysis => ({
          analysis,
          relevanceScore: 1,
          matchedFields: ['title'],
          highlights: {},
        }));
      } else {
        // No query, just return filtered results
        results = filteredAnalyses.map(analysis => ({
          analysis,
          relevanceScore: 1,
          matchedFields: [],
          highlights: {},
        }));
      }

      setSearchResults(results);
      onSearchResults?.(results);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery, searchMode, enableSemanticSearch, applyFilters, analyses, performSemanticSearch, onSearchResults]);

  // Auto-search when query or filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, activeFilters, searchMode, performSearch]);

  // Add filter
  const addFilter = useCallback((filter: Omit<SearchFilter, 'id'>) => {
    const newFilter: SearchFilter = {
      ...filter,
      id: `filter_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    };
    setActiveFilters(prev => [...prev, newFilter]);
  }, []);

  // Remove filter
  const removeFilter = useCallback((filterId: string) => {
    setActiveFilters(prev => prev.filter(f => f.id !== filterId));
  }, []);

  // Save current search
  const saveCurrentSearch = useCallback(() => {
    if (!saveSearchName.trim()) return;

    const savedSearch: Omit<SavedSearch, 'id'> = {
      name: saveSearchName,
      query: searchQuery,
      filters: activeFilters,
      createdAt: new Date(),
      lastUsed: new Date(),
      useCount: 1,
      isStarred: false,
    };

    onSaveSearch?.(savedSearch);
    setShowSaveDialog(false);
    setSaveSearchName('');
  }, [saveSearchName, searchQuery, activeFilters, onSaveSearch]);

  // Load saved search
  const loadSavedSearch = useCallback((savedSearch: SavedSearch) => {
    setSearchQuery(savedSearch.query);
    setActiveFilters(savedSearch.filters);
  }, []);

  return (
    <div className="space-y-4">
      {/* Search Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Search
            {enableSemanticSearch && <Badge variant="secondary">AI-Powered</Badge>}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search analyses, questions, and content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowSaveDialog(true)}
              disabled={!searchQuery && activeFilters.length === 0}
            >
              <Save className="h-4 w-4" />
            </Button>
          </div>

          {/* Search Mode Tabs */}
          <Tabs value={searchMode} onValueChange={(value) => setSearchMode(value as 'simple' | 'advanced' | 'semantic')}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="simple">Simple</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
              {enableSemanticSearch && (
                <TabsTrigger value="semantic">
                  <Brain className="h-4 w-4 mr-1" />
                  Semantic
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="advanced" className="space-y-4">
              {/* Faceted Filters */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium">Analysis Type</label>
                  <select
                    className="w-full mt-1 p-2 border rounded-md text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        addFilter({
                          name: 'Analysis Type',
                          type: 'analysisType',
                          value: e.target.value,
                          operator: 'equals',
                        });
                        e.target.value = '';
                      }
                    }}
                  >
                    <option value="">Select type...</option>
                    {facetData.analysisTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Style</label>
                  <select
                    className="w-full mt-1 p-2 border rounded-md text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        addFilter({
                          name: 'Style',
                          type: 'style',
                          value: e.target.value,
                          operator: 'equals',
                        });
                        e.target.value = '';
                      }
                    }}
                  >
                    <option value="">Select style...</option>
                    {facetData.styles.map(style => (
                      <option key={style} value={style}>{style}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Min Rating</label>
                  <select
                    className="w-full mt-1 p-2 border rounded-md text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        addFilter({
                          name: 'Min Rating',
                          type: 'rating',
                          value: parseInt(e.target.value),
                          operator: 'greater',
                        });
                        e.target.value = '';
                      }
                    }}
                  >
                    <option value="">Select rating...</option>
                    {[1, 2, 3, 4, 5].map(rating => (
                      <option key={rating} value={rating}>{rating}+ stars</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="text-sm font-medium">Date Range</label>
                  <input
                    type="date"
                    className="w-full mt-1 p-2 border rounded-md text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        addFilter({
                          name: 'After Date',
                          type: 'date',
                          value: e.target.value,
                          operator: 'greater',
                        });
                      }
                    }}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Active Filters */}
          {activeFilters.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {activeFilters.map(filter => (
                <Badge key={filter.id} variant="secondary" className="flex items-center gap-1">
                  <Filter className="h-3 w-3" />
                  {filter.name}: {typeof filter.value === 'string' || typeof filter.value === 'number' ? String(filter.value) : ''}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => removeFilter(filter.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}

          {/* Search Results Summary */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>
              {isSearching ? 'Searching...' : `${searchResults.length} results found`}
            </span>
            {searchMode === 'semantic' && (
              <Badge variant="outline" className="text-xs">
                <Brain className="h-3 w-3 mr-1" />
                Semantic search active
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Saved Searches */}
      {savedSearches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Saved Searches
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {savedSearches.slice(0, 6).map(savedSearch => (
                <Button
                  key={savedSearch.id}
                  variant="outline"
                  size="sm"
                  className="justify-start"
                  onClick={() => loadSavedSearch(savedSearch)}
                >
                  {savedSearch.isStarred && <Star className="h-3 w-3 mr-1 text-yellow-500" />}
                  <span className="truncate">{savedSearch.name}</span>
                  <Badge variant="secondary" className="ml-auto text-xs">
                    {savedSearch.useCount}
                  </Badge>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Input
                placeholder="Enter search name..."
                value={saveSearchName}
                onChange={(e) => setSaveSearchName(e.target.value)}
                className="flex-1"
              />
              <Button onClick={saveCurrentSearch} disabled={!saveSearchName.trim()}>
                Save
              </Button>
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
