import React, { useState, useCallback, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  Pause, 
  Square, 
  SkipForward,
  Rewind,
  MessageSquare,
  User,
  Bot,
  Clock,
  Bar<PERSON>hart3,
  Download,
  <PERSON>tings,
  Zap,
  Eye,
  TrendingUp
} from 'lucide-react';
import { ChatScenario, ScenarioNode } from './VisualScenarioDesigner';

export interface SimulationStep {
  id: string;
  nodeId: string;
  timestamp: Date;
  content: string;
  persona?: CharacterPersona;
  isUserMessage: boolean;
  metadata: {
    responseTime: number;
    confidence: number;
    alternatives?: string[];
  };
}

export interface SimulationResult {
  id: string;
  scenarioId: string;
  steps: SimulationStep[];
  metrics: SimulationMetrics;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'paused' | 'error';
}

export interface SimulationMetrics {
  totalSteps: number;
  averageResponseTime: number;
  conversationLength: number;
  branchingPoints: number;
  completionRate: number;
  userSatisfactionScore?: number;
  pathsExplored: string[];
}

interface SimulationRunnerProps {
  scenario: ChatScenario;
  onComplete?: (result: SimulationResult) => void;
  onStepComplete?: (step: SimulationStep) => void;
  autoPlay?: boolean;
  playbackSpeed?: number;
  className?: string;
}

export const SimulationRunner: React.FC<SimulationRunnerProps> = ({
  scenario,
  onComplete,
  onStepComplete,
  autoPlay = false,
  playbackSpeed = 1,
  className,
}) => {
  const [simulationResult, setSimulationResult] = useState<SimulationResult>({
    id: `sim_${Date.now()}`,
    scenarioId: scenario.id,
    steps: [],
    metrics: {
      totalSteps: 0,
      averageResponseTime: 0,
      conversationLength: 0,
      branchingPoints: 0,
      completionRate: 0,
      pathsExplored: [],
    },
    startTime: new Date(),
    status: 'running',
  });

  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [currentSpeed, setCurrentSpeed] = useState(playbackSpeed);
  const [showMetrics, setShowMetrics] = useState(false);
  const [highlightedPath, setHighlightedPath] = useState<string[]>([]);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Find start node
  const startNode = scenario.nodes.find(node => node.type === 'start');

  // Initialize simulation
  useEffect(() => {
    if (startNode && !currentNodeId) {
      setCurrentNodeId(startNode.id);
    }
  }, [startNode, currentNodeId]);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [simulationResult.steps]);

  // Simulation step execution
  const executeStep = useCallback(async (nodeId: string): Promise<void> => {
    const node = scenario.nodes.find(n => n.id === nodeId);
    if (!node) return;

    const startTime = Date.now();
    
    // Simulate processing time based on content length
    const processingTime = Math.max(500, node.data.content.length * 20);
    await new Promise(resolve => setTimeout(resolve, processingTime / currentSpeed));

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // Create simulation step
    const step: SimulationStep = {
      id: `step_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      nodeId,
      timestamp: new Date(),
      content: node.data.content,
      persona: node.data.persona,
      isUserMessage: node.data.isUserMessage || false,
      metadata: {
        responseTime,
        confidence: Math.random() * 0.3 + 0.7, // Simulate confidence 70-100%
        alternatives: generateAlternatives(node.data.content),
      },
    };

    // Update simulation result
    setSimulationResult(prev => {
      const newSteps = [...prev.steps, step];
      const newMetrics = calculateMetrics(newSteps, scenario);
      
      return {
        ...prev,
        steps: newSteps,
        metrics: newMetrics,
      };
    });

    // Update highlighted path
    setHighlightedPath(prev => [...prev, nodeId]);

    // Notify step completion
    onStepComplete?.(step);

    // Find next node
    const nextNodeId = getNextNode(node, scenario);
    if (nextNodeId) {
      setCurrentNodeId(nextNodeId);
    } else {
      // Simulation complete
      setSimulationResult(prev => ({
        ...prev,
        endTime: new Date(),
        status: 'completed',
      }));
      setIsPlaying(false);
      onComplete?.({
        ...simulationResult,
        endTime: new Date(),
        status: 'completed',
      });
    }
  }, [scenario, currentSpeed, onStepComplete, onComplete, simulationResult]);

  // Generate alternative responses
  const generateAlternatives = (content: string): string[] => {
    // Simple alternative generation (in real implementation, this would use AI)
    const alternatives = [
      content.replace(/\./g, '!'),
      content.toLowerCase(),
      content + ' (Alternative response)',
    ];
    return alternatives.slice(0, 2);
  };

  // Calculate simulation metrics
  const calculateMetrics = (steps: SimulationStep[], scenario: ChatScenario): SimulationMetrics => {
    const totalSteps = steps.length;
    const averageResponseTime = steps.reduce((sum, step) => sum + step.metadata.responseTime, 0) / totalSteps || 0;
    const conversationLength = steps.reduce((sum, step) => sum + step.content.length, 0);
    const branchingPoints = scenario.nodes.filter(node => node.connections.length > 1).length;
    const completionRate = totalSteps / scenario.nodes.length;
    const pathsExplored = [...new Set(steps.map(step => step.nodeId))];

    return {
      totalSteps,
      averageResponseTime,
      conversationLength,
      branchingPoints,
      completionRate,
      pathsExplored,
    };
  };

  // Get next node in the flow
  const getNextNode = (currentNode: ScenarioNode, scenario: ChatScenario): string | null => {
    if (currentNode.connections.length === 0) return null;
    
    // For decision nodes, randomly select a path (in real implementation, this would be based on conditions)
    if (currentNode.type === 'decision' && currentNode.connections.length > 1) {
      const randomIndex = Math.floor(Math.random() * currentNode.connections.length);
      return currentNode.connections[randomIndex];
    }
    
    return currentNode.connections[0];
  };

  // Play/pause simulation
  const togglePlayback = useCallback(() => {
    setIsPlaying(prev => !prev);
  }, []);

  // Step forward manually
  const stepForward = useCallback(() => {
    if (currentNodeId && simulationResult.status === 'running') {
      executeStep(currentNodeId);
    }
  }, [currentNodeId, simulationResult.status, executeStep]);

  // Reset simulation
  const resetSimulation = useCallback(() => {
    setSimulationResult({
      id: `sim_${Date.now()}`,
      scenarioId: scenario.id,
      steps: [],
      metrics: {
        totalSteps: 0,
        averageResponseTime: 0,
        conversationLength: 0,
        branchingPoints: 0,
        completionRate: 0,
        pathsExplored: [],
      },
      startTime: new Date(),
      status: 'running',
    });
    setCurrentNodeId(startNode?.id || null);
    setHighlightedPath([]);
    setIsPlaying(false);
  }, [scenario.id, startNode?.id]);

  // Auto-play effect
  useEffect(() => {
    if (isPlaying && currentNodeId && simulationResult.status === 'running') {
      intervalRef.current = setTimeout(() => {
        executeStep(currentNodeId);
      }, 1000 / currentSpeed);
    }

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
    };
  }, [isPlaying, currentNodeId, simulationResult.status, executeStep, currentSpeed]);

  // Progress calculation
  const progress = (simulationResult.steps.length / scenario.nodes.length) * 100;

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Control Panel */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={togglePlayback}
                  disabled={simulationResult.status === 'completed'}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                
                <Button size="sm" variant="outline" onClick={stepForward}>
                  <SkipForward className="h-4 w-4" />
                </Button>
                
                <Button size="sm" variant="outline" onClick={resetSimulation}>
                  <Rewind className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Speed:</span>
                <select
                  value={currentSpeed}
                  onChange={(e) => setCurrentSpeed(Number(e.target.value))}
                  className="text-sm border rounded px-2 py-1"
                >
                  <option value={0.5}>0.5x</option>
                  <option value={1}>1x</option>
                  <option value={2}>2x</option>
                  <option value={4}>4x</option>
                </select>
              </div>

              <Badge variant={
                simulationResult.status === 'completed' ? 'default' :
                simulationResult.status === 'running' ? 'secondary' :
                simulationResult.status === 'error' ? 'destructive' : 'outline'
              }>
                {simulationResult.status}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowMetrics(!showMetrics)}
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Metrics
              </Button>
              
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
              <span>Simulation Progress</span>
              <span>{simulationResult.steps.length} / {scenario.nodes.length} steps</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Conversation View */}
        <div className="flex-1 flex flex-col">
          <Card className="flex-1 rounded-none border-x-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Conversation Simulation
                <Badge variant="outline">{scenario.name}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto p-4">
              <div className="space-y-4">
                {simulationResult.steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex ${step.isUserMessage ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[70%] p-3 rounded-lg ${
                        step.isUserMessage
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        {step.isUserMessage ? (
                          <User className="h-4 w-4" />
                        ) : (
                          <Bot className="h-4 w-4" />
                        )}
                        {step.persona && (
                          <span className="text-xs font-medium">{step.persona.name}</span>
                        )}
                        <span className="text-xs opacity-70">
                          {step.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      
                      <div className="text-sm">{step.content}</div>
                      
                      <div className="flex items-center gap-2 mt-2 text-xs opacity-70">
                        <Clock className="h-3 w-3" />
                        {step.metadata.responseTime}ms
                        <span className="ml-2">
                          Confidence: {(step.metadata.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Current step indicator */}
                {isPlaying && simulationResult.status === 'running' && (
                  <div className="flex justify-center">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <div className="animate-pulse w-2 h-2 bg-blue-600 rounded-full" />
                      Processing next step...
                    </div>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Metrics Panel */}
        {showMetrics && (
          <Card className="w-80 rounded-none border-y-0 border-r-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Simulation Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {simulationResult.metrics.totalSteps}
                  </div>
                  <div className="text-xs text-muted-foreground">Total Steps</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {simulationResult.metrics.averageResponseTime.toFixed(0)}ms
                  </div>
                  <div className="text-xs text-muted-foreground">Avg Response</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {simulationResult.metrics.conversationLength}
                  </div>
                  <div className="text-xs text-muted-foreground">Total Chars</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {(simulationResult.metrics.completionRate * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Completion</div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">Paths Explored</div>
                <div className="space-y-1">
                  {simulationResult.metrics.pathsExplored.map(nodeId => {
                    const node = scenario.nodes.find(n => n.id === nodeId);
                    return (
                      <div key={nodeId} className="text-xs p-2 bg-gray-50 rounded">
                        {node?.type}: {node?.data.content.substring(0, 30)}...
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">Performance</div>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>Branching Points:</span>
                    <span>{simulationResult.metrics.branchingPoints}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span>
                      {simulationResult.endTime 
                        ? Math.round((simulationResult.endTime.getTime() - simulationResult.startTime.getTime()) / 1000)
                        : Math.round((Date.now() - simulationResult.startTime.getTime()) / 1000)
                      }s
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
