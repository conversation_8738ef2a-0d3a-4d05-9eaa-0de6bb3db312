/**
 * Component Standardization Utilities
 *
 * Provides utilities and patterns for standardizing component architecture
 * across the entire application. This ensures consistency in props, styling,
 * state management, and behavior patterns.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { designTokens } from '@/design-system/tokens';

// Standard component props interface that all components should extend
export interface StandardComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Standard interactive component props
export interface StandardInteractiveProps extends StandardComponentProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
  tabIndex?: number;
}

// Standard form component props
type OnChangeHandler<T> = (value: T) => void;
type OnValidateHandler<T> = (value: T) => string | null;

export interface StandardFormProps<T = unknown> extends StandardInteractiveProps {
  name?: string;
  value?: T;
  defaultValue?: T;
  onChange?: OnChangeHandler<T>;
  onValidate?: OnValidateHandler<T>;
  error?: string;
  required?: boolean;
  placeholder?: string;
}

// Standard layout component props
export interface StandardLayoutProps extends StandardComponentProps {
  padding?: keyof typeof designTokens.spacing;
  margin?: keyof typeof designTokens.spacing;
  gap?: keyof typeof designTokens.spacing;
  direction?: 'row' | 'column';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
}

// Standard animation props
export interface StandardAnimationProps {
  animate?: boolean;
  animationDuration?: 'fast' | 'normal' | 'slow';
  animationDelay?: number;
  animationEasing?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  reduceMotion?: boolean;
}

// Standard responsive props
export interface StandardResponsiveProps {
  responsive?: boolean;
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  hideOn?: Array<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>;
  showOn?: Array<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>;
}

// Utility function to create standardized component classes
export const createStandardClasses = (
  baseClasses: string,
  props: Partial<StandardLayoutProps & StandardResponsiveProps>,
  additionalClasses?: string
): string => {
  const classes = [baseClasses];

  // Add spacing classes
  if (props.padding) {
    const paddingValue = designTokens.spacing[props.padding];
    classes.push(`p-[${paddingValue}]`);
  }

  if (props.margin) {
    const marginValue = designTokens.spacing[props.margin];
    classes.push(`m-[${marginValue}]`);
  }

  if (props.gap) {
    const gapValue = designTokens.spacing[props.gap];
    classes.push(`gap-[${gapValue}]`);
  }

  // Add layout classes
  if (props.direction) {
    classes.push(props.direction === 'row' ? 'flex-row' : 'flex-col');
  }

  if (props.align) {
    const alignMap = {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
    };
    classes.push(alignMap[props.align]);
  }

  if (props.justify) {
    const justifyMap = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    };
    classes.push(justifyMap[props.justify]);
  }

  if (props.wrap) {
    classes.push('flex-wrap');
  }

  // Add responsive classes
  if (props.hideOn) {
    props.hideOn.forEach(breakpoint => {
      classes.push(`${breakpoint}:hidden`);
    });
  }

  if (props.showOn) {
    props.showOn.forEach(breakpoint => {
      classes.push(`hidden ${breakpoint}:block`);
    });
  }

  // Add additional classes
  if (additionalClasses) {
    classes.push(additionalClasses);
  }

  return cn(...classes);
};

// Utility function to create standardized animation classes
export const createAnimationClasses = (
  props: StandardAnimationProps,
  baseAnimation?: string
): string => {
  if (!props.animate) return '';

  const classes = [baseAnimation || 'transition-all'];

  // Duration
  const durationMap = {
    fast: 'duration-150',
    normal: 'duration-300',
    slow: 'duration-500',
  };
  classes.push(durationMap[props.animationDuration || 'normal']);

  // Easing
  const easingMap = {
    ease: 'ease',
    'ease-in': 'ease-in',
    'ease-out': 'ease-out',
    'ease-in-out': 'ease-in-out',
  };
  classes.push(easingMap[props.animationEasing || 'ease-out']);

  // Delay
  if (props.animationDelay) {
    classes.push(`delay-[${props.animationDelay}ms]`);
  }

  // Reduced motion
  if (props.reduceMotion) {
    classes.push('motion-reduce:transition-none');
  }

  return cn(...classes);
};

// Utility function to create standardized state classes
export const createStateClasses = (
  disabled?: boolean,
  loading?: boolean,
  error?: boolean,
  success?: boolean
): string => {
  const classes = [];

  if (disabled) {
    classes.push('opacity-50 cursor-not-allowed pointer-events-none');
  }

  if (loading) {
    classes.push('cursor-wait');
  }

  if (error) {
    classes.push('border-red-500 text-red-600');
  }

  if (success) {
    classes.push('border-green-500 text-green-600');
  }

  return cn(...classes);
};

// Utility function to create standardized focus classes
export const createFocusClasses = (variant: 'default' | 'primary' | 'secondary' = 'default'): string => {
  const focusMap = {
    default: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    primary: 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
    secondary: 'focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2',
  };

  return focusMap[variant];
};

// Utility function to create standardized hover classes
export const createHoverClasses = (variant: 'default' | 'primary' | 'secondary' = 'default'): string => {
  const hoverMap = {
    default: 'hover:bg-gray-50 hover:border-gray-300',
    primary: 'hover:bg-primary/10 hover:border-primary/30',
    secondary: 'hover:bg-secondary/10 hover:border-secondary/30',
  };

  return hoverMap[variant];
};

// Standard component factory function
export const createStandardComponent = <T extends StandardComponentProps>(
  displayName: string,
  baseClasses: string,
  defaultProps?: Partial<T>
) => {
  return React.forwardRef<HTMLElement, T>((props, ref) => {
    const mergedProps = { ...defaultProps, ...props };
    const className = createStandardClasses(
      baseClasses,
      mergedProps as T,
      mergedProps.className
    );

    return React.createElement('div', {
      ...mergedProps,
      ref,
      className,
    });
  });
};

// Validation utilities
export const validateStandardProps = (props: StandardComponentProps): string[] => {
  const errors: string[] = [];

  // Check for required accessibility props
  if (!props['aria-label'] && !props['aria-describedby'] && !props.children) {
    errors.push('Component should have aria-label, aria-describedby, or descriptive children');
  }

  // Check for test ID in development
  if (process.env.NODE_ENV === 'development' && !props['data-testid']) {
    console.warn('Component missing data-testid for testing');
  }

  return errors;
};

// Performance optimization utilities
export const memoizeStandardComponent = <T extends StandardComponentProps>(
  Component: React.ComponentType<T>
) => {
  return React.memo(Component, (prevProps, nextProps) => {
    // Standard shallow comparison for common props
    const standardKeys: (keyof StandardComponentProps)[] = [
      'className',
      'children',
      'id',
      'data-testid',
      'aria-label',
      'aria-describedby',
    ];

    for (const key of standardKeys) {
      if (prevProps[key] !== nextProps[key]) {
        return false;
      }
    }

    return true;
  });
};

// Export React for the factory function
