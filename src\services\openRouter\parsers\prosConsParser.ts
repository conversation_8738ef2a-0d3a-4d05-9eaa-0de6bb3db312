import { ParsedProsConsAnalysis, ParsedFallback } from '../types/parsedOutput';

export class ProsConsParser {
  static parse(rawText: string): ParsedProsConsAnalysis | ParsedFallback {
    // Basic placeholder implementation
    const prosMatch = rawText.match(/Pros:([\s\S]*?)Cons:/i);
    const consMatch = rawText.match(/Cons:([\s\S]*)/i);

    if (prosMatch && consMatch) {
      const pros = prosMatch[1].trim().split('\n').map(s => s.trim()).filter(Boolean);
      const cons = consMatch[1].trim().split('\n').map(s => s.trim()).filter(Boolean);
      return {
        type: 'pros-cons',
        pros,
        cons,
      };
    }

    return {
      type: 'fallback',
      isFallback: true,
      content: rawText,
      warning: 'Could not parse Pros & Cons structure.',
    };
  }
}
