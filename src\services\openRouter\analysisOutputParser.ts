import { ParsedOutput, ParsedDeepAnalysis, ParsedCharacterAnalysis, ParsedProsConsAnalysis, ParsedSixThinkingHatsAnalysis, ParsedEmotionalAnglesAnalysis } from './types/parsedOutput';
import { MultipleAnswersParser } from './parsers/multipleAnswersParser';
import { FallbackParser } from './parsers/fallbackParser';
import { ProsConsParser } from './parsers/prosConsParser';
import { SixThinkingHatsParser } from './parsers/sixThinkingHatsParser';
import { EmotionalAnglesParser } from './parsers/emotionalAnglesParser';

const parseList = (text: string | undefined): string[] | undefined => {
  if (!text) return undefined;
  return text.split('\n')
    .map(s => s.replace(/^\s*[-*]?\s*\d*\.\s*/, '').trim())
    .filter(Boolean);
};

export class AnalysisOutputParser {
  private static parseDeepAnalysis(rawText: string): ParsedDeepAnalysis {
    const sections: { [key: string]: string } = {
      summary: '',
      key_concepts: '',
      main_argument: '',
      counterarguments: '',
      assumptions: '',
      possibleResponses: '',
      followUpQuestions: ''
    };

    const sectionPatterns = {
      summary: /(?:^|\n)(?:##?\s*)?(?:Executive\s+Summary|Summary)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Key\s+Concepts|Main\s+Argument|Potential\s+Counter|Assumptions|Possible\s+Responses|Follow[- ]?up|$))/i,
      key_concepts: /(?:^|\n)(?:##?\s*)?(?:Key\s+Concepts?(?:\s*&\s*Definitions?)?|Definitions?)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Main\s+Argument|Executive\s+Summary|Potential\s+Counter|Assumptions|Possible\s+Responses|Follow[- ]?up|$))/i,
      main_argument: /(?:^|\n)(?:##?\s*)?(?:Main\s+Argument(?:\/Analysis)?|Analysis|Argument)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Potential\s+Counter|Key\s+Concepts|Executive\s+Summary|Assumptions|Possible\s+Responses|Follow[- ]?up|$))/i,
      counterarguments: /(?:^|\n)(?:##?\s*)?(?:Potential\s+Counter(?:arguments?)?|Counter(?:arguments?)?|Alternative\s+Views?)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Assumptions|Main\s+Argument|Key\s+Concepts|Executive\s+Summary|Possible\s+Responses|Follow[- ]?up|$))/i,
      assumptions: /(?:^|\n)(?:##?\s*)?(?:Assumptions?\s+Made?|Key\s+Assumptions?)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Possible\s+Responses|Follow[- ]?up|Potential\s+Counter|Main\s+Argument|Key\s+Concepts|Executive\s+Summary|$))/i,
      possibleResponses: /(?:^|\n)(?:##?\s*)?(?:Possible\s+Responses(?: You Might Hear)?)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Follow[- ]?up|Assumptions|Potential\s+Counter|Main\s+Argument|Key\s+Concepts|Executive\s+Summary|$))/i,
      followUpQuestions: /(?:^|\n)(?:##?\s*)?(?:Suggested\s+Follow[- ]?up|Follow[- ]?up|Next\s+Steps?|Further\s+Inquiries|Questions\s+to\s+ask)(?:\s+Questions?)?(?:\s*:?)?\s*\n([\s\S]*?)$/i
    };

    let sectionsFound = 0;

    for (const [key, pattern] of Object.entries(sectionPatterns)) {
      const match = rawText.match(pattern);
      if (match && match[1]) {
        sections[key as keyof typeof sections] = match[1].trim();
        sectionsFound++;
      }
    }

    if (sectionsFound === 0) {
      return {
        type: 'deep',
        summary: rawText.trim(),
        isFallback: true,
        warning: 'Could not parse AI response structure for deep analysis.'
      };
    }

    return {
      type: 'deep',
      summary: sections.summary,
      key_concepts: sections.key_concepts,
      main_argument: sections.main_argument,
      counterarguments: sections.counterarguments,
      assumptions: sections.assumptions,
      possibleResponses: parseList(sections.possibleResponses),
      followUpQuestions: parseList(sections.followUpQuestions),
    };
  }

  private static parseCharacterAnalysis(rawText: string): ParsedCharacterAnalysis {
    const sections: { [key: string]: string } = {
      response: '',
      possibleRetorts: '',
      furtherInquiries: ''
    };
    
    const firstSectionMatch = rawText.match(/\n(?:##?\s*)?(?:Possible\s+Retorts|Further\s+Inquiries)/i);
    const splitIndex = firstSectionMatch ? firstSectionMatch.index : rawText.length;
    sections.response = rawText.substring(0, splitIndex).trim();
    
    const remainingText = rawText.substring(splitIndex);

    const retortsMatch = remainingText.match(/(?:^|\n)(?:##?\s*)?(?:Possible\s+Retorts)(?:\s*:?)?\s*\n([\s\S]*?)(?=\n(?:##?\s*)?(?:Further\s+Inquiries|Follow[- ]?up)|$)/i);
    if (retortsMatch) sections.possibleRetorts = retortsMatch[1].trim();
    
    const inquiriesMatch = remainingText.match(/(?:^|\n)(?:##?\s*)?(?:Further\s+Inquiries|Follow[- ]?up\s+Questions?)(?:\s*:?)?\s*\n([\s\S]*?)$/i);
    if (inquiriesMatch) sections.furtherInquiries = inquiriesMatch[1].trim();

    if (!sections.response && !sections.possibleRetorts && !sections.furtherInquiries) {
        return {
            type: 'character',
            response: rawText,
        };
    }

    return {
      type: 'character',
      response: sections.response,
      possibleResponses: parseList(sections.possibleRetorts),
      followUpQuestions: parseList(sections.furtherInquiries),
    };
  }

  /**
   * Core parsing function that transforms raw AI text into structured objects
   */
  static parseAnalysisOutput(
    rawText: string, 
    analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles'
  ): ParsedOutput {
    try {
      switch (analysisType) {
        case 'multiple':
          return MultipleAnswersParser.parse(rawText);
        case 'deep':
          return this.parseDeepAnalysis(rawText); // Keep existing deep analysis for now
        case 'pros-cons':
          return ProsConsParser.parse(rawText);
        case 'six-hats':
          return SixThinkingHatsParser.parse(rawText);
        case 'emotional-angles':
          return EmotionalAnglesParser.parse(rawText);
        case 'character':
          return this.parseCharacterAnalysis(rawText);
        default:
          return FallbackParser.create(rawText, 'Unknown analysis type');
      }
    } catch (error) {
      console.error('Error parsing analysis output:', error);
      return FallbackParser.create(rawText, 'Parsing error occurred');
    }
  }
}

// Re-export types for backward compatibility
export * from './types/parsedOutput';
