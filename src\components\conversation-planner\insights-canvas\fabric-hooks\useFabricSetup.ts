import { useState, useLayoutEffect } from 'react';
import { Canvas } from 'fabric';
import { usePanAndZoomHandler } from './handlers/usePanAndZoomHandler'; // Import the hook

export const useFabricSetup = (
  canvasEl: React.RefObject<HTMLCanvasElement>,
  canvasWrapperEl: React.RefObject<HTMLDivElement>,
  onZoomChange?: (zoom: number) => void, // Added
  onPanChange?: (pan: { x: number; y: number }) => void // Added
) => {
  const [canvas, setCanvas] = useState<Canvas | null>(null);
  // Initialize the pan and zoom handler
  const panAndZoomHandler = usePanAndZoomHandler({ canvas, onZoomChange, onPanChange });

  useLayoutEffect(() => {
    const wrapper = canvasWrapperEl.current;
    if (!wrapper || !canvasEl.current) return;    const fabricCanvas = new Canvas(canvasEl.current, {
      selection: true,
      backgroundColor: 'hsl(var(--background))',
      stopContextMenu: true,
      viewportTransform: [1, 0, 0, 1, 0, 0], // Initialize with identity matrix
    });

    setCanvas(fabricCanvas);

    // Attach pan and zoom event listeners
    fabricCanvas.on('mouse:wheel', panAndZoomHandler.handleMouseWheel);
    fabricCanvas.on('mouse:down', (opt) => {
      if (panAndZoomHandler.isPanAttempt(opt.e)) {
        panAndZoomHandler.handlePanStart(opt.e);
      }
    });
    fabricCanvas.on('mouse:move', panAndZoomHandler.handlePanMove);
    fabricCanvas.on('mouse:up', panAndZoomHandler.handlePanEnd);
    
    // Keyboard listeners for pan activation (Alt key)
    // Ensure these are added and removed correctly
    const handleKeyDown = (e: KeyboardEvent) => panAndZoomHandler.handleKeyDown(e);
    const handleKeyUp = (e: KeyboardEvent) => panAndZoomHandler.handleKeyUp(e);
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    const resizeObserver = new ResizeObserver(entries => {
      const { width, height } = entries[0].contentRect;
      fabricCanvas.setWidth(width);
      fabricCanvas.setHeight(height);
      fabricCanvas.renderAll();
    });
    resizeObserver.observe(wrapper);

    return () => {
      resizeObserver.disconnect();
      // Remove event listeners
      if (fabricCanvas) {
        fabricCanvas.off('mouse:wheel', panAndZoomHandler.handleMouseWheel);
        fabricCanvas.off('mouse:down');
        fabricCanvas.off('mouse:move', panAndZoomHandler.handlePanMove);
        fabricCanvas.off('mouse:up', panAndZoomHandler.handlePanEnd);
      }
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      fabricCanvas.dispose();
    };
  }, [canvasEl, canvasWrapperEl, panAndZoomHandler]); // Add panAndZoomHandler to dependencies

  return canvas;
};
