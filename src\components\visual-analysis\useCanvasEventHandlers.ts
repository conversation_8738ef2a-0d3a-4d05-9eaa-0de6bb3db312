import { useEffect, useRef } from 'react';

/**
 * Custom hook to encapsulate mouse and keyboard event handlers for LivingDataCanvas.
 * Accepts handler functions and refs as arguments.
 */
export function useCanvasEventHandlers({
  mountRef,
  onMouseMove,
  onMouseDown,
  onMouseUp,
  onClick,
  onContextMenu,
  handleClickOutside,
  handleKeyDown
}: {
  mountRef: React.RefObject<HTMLDivElement>;
  onMouseMove: (e: MouseEvent) => void;
  onMouseDown: (e: MouseEvent) => void;
  onMouseUp: (e: MouseEvent) => void;
  onClick: (e: MouseEvent) => void;
  onContextMenu: (e: MouseEvent) => void;
  handleClickOutside: (e: MouseEvent) => void;
  handleKeyDown: (e: KeyboardEvent) => void;
}) {
  // Throttle mousemove for performance
  const throttledOnMouseMove = useRef<(e: MouseEvent) => void>();
  useEffect(() => {
    throttledOnMouseMove.current = onMouseMove;
  }, [onMouseMove]);

  useEffect(() => {
    const mount = mountRef.current;
    if (!mount) return;
    mount.addEventListener('mousemove', (e) => throttledOnMouseMove.current?.(e));
    mount.addEventListener('mousedown', onMouseDown);
    mount.addEventListener('click', onClick);
    mount.addEventListener('contextmenu', onContextMenu);
    document.addEventListener('mouseup', onMouseUp);
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      mount.removeEventListener('mousemove', (e) => throttledOnMouseMove.current?.(e));
      mount.removeEventListener('mousedown', onMouseDown);
      mount.removeEventListener('click', onClick);
      mount.removeEventListener('contextmenu', onContextMenu);
      document.removeEventListener('mouseup', onMouseUp);
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [mountRef, onMouseDown, onMouseUp, onClick, onContextMenu, handleClickOutside, handleKeyDown]);
}
