
import { useRef }from 'react';
import { Canvas, TPointerEventInfo, Rect } from 'fabric';
import { CustomFabricObject, CustomLineObject } from '../../fabric-helpers';

interface HoverHandlerProps {
    canvas: Canvas | null;
    selectedNodes: string[];
}

export const useHoverHandler = ({ canvas, selectedNodes }: HoverHandlerProps) => {
    const highlightedObjects = useRef<{
        node: CustomFabricObject | null;
        connections: CustomLineObject[];
        connectedNodes: CustomFabricObject[];
    }>({ node: null, connections: [], connectedNodes: [] });
    
    const resetHighlights = () => {
        if (!canvas) return;
        const { node, connections, connectedNodes } = highlightedObjects.current;

        if (node) {
            const isSelected = selectedNodes.includes(node.data!.id);
            (node.item(0) as Rect).set({
                stroke: isSelected ? 'hsl(var(--primary))' : 'hsl(var(--border))',
                strokeWidth: isSelected ? 2 : 1,
            });
        }

        connections.forEach(conn => {
            const isFromSelected = selectedNodes.includes(conn.data!.fromId);
            const isToSelected = selectedNodes.includes(conn.data!.toId);
            const isHighlighted = isFromSelected || isToSelected;
            conn.set({
                stroke: isHighlighted ? 'hsl(var(--primary))' : 'hsl(var(--border))',
                strokeWidth: isHighlighted ? 2.5 : 1.5,
            });
        });

        connectedNodes.forEach(connNode => {
            const isSelected = selectedNodes.includes(connNode.data!.id);
            (connNode.item(0) as Rect).set({
                stroke: isSelected ? 'hsl(var(--primary))' : 'hsl(var(--border))',
                strokeWidth: isSelected ? 2 : 1,
            });
        });
        
        highlightedObjects.current = { node: null, connections: [], connectedNodes: [] };
        canvas.requestRenderAll();
    };
    
    const handleMouseOver = (opt: TPointerEventInfo) => {
        if (!canvas) return;
        const target = opt.target as CustomFabricObject | undefined;
        if (!target?.data?.id || target.type === 'line' || highlightedObjects.current.node?.data?.id === target.data.id) return;
        
        resetHighlights();

        const hoveredNodeId = target.data.id;
        const allObjects = canvas.getObjects();
        const nodeObjects = allObjects.filter(obj => (obj as CustomFabricObject).data?.id && obj.type !== 'line') as CustomFabricObject[];
        const lineObjects = allObjects.filter(obj => (obj as CustomLineObject).data?.type === 'connection') as CustomLineObject[];
        const hoveredNode = nodeObjects.find(n => n.data!.id === hoveredNodeId);
        
        if (!hoveredNode) return;

        highlightedObjects.current.node = hoveredNode;
        (hoveredNode.item(0) as Rect).set({ stroke: 'hsl(var(--ring))', strokeWidth: 3 });
        
        const connectedLines = lineObjects.filter(line => line.data!.fromId === hoveredNodeId || line.data!.toId === hoveredNodeId);
        highlightedObjects.current.connections = connectedLines;
        
        const connectedNodeIds = new Set<string>();
        connectedLines.forEach(line => {
            line.set({ stroke: 'hsl(var(--primary))', strokeWidth: 3 });
            if (line.data!.fromId === hoveredNodeId) connectedNodeIds.add(line.data!.toId);
            else connectedNodeIds.add(line.data!.fromId);
        });

        const connectedNodes = nodeObjects.filter(n => connectedNodeIds.has(n.data!.id));
        highlightedObjects.current.connectedNodes = connectedNodes;
        
        connectedNodes.forEach(node => {
            if (node.data!.id !== hoveredNodeId) (node.item(0) as Rect).set({ stroke: 'hsl(var(--primary))', strokeWidth: 2 });
        });
        
        canvas.requestRenderAll();
    };    const handleMouseOut = (opt: TPointerEventInfo & { nextTarget?: unknown }) => {
        if (opt.target && !opt.nextTarget) {
            resetHighlights();
        }
    };

    return { handleMouseOver, handleMouseOut };
};
