import { T<PERSON>ointerEvent, Canvas, FabricObject, ActiveSelection, Group } from 'fabric';
import { CustomFabricObject } from '../../fabric-helpers';

interface SelectionHandlerProps {
    canvas: Canvas | null;
    onSelectionChange: (selectedIds: string[], isMultiSelect: boolean) => void;
}

interface AnimationProperties {
    scaleX?: number;
    scaleY?: number;
    strokeWidth?: number;
    [key: string]: number | undefined;
}

const animate = (object: FabricObject, properties: AnimationProperties, duration = 100) => {
    if (!object.canvas) return;
    object.animate(properties, {
        duration,
        onChange: object.canvas.renderAll.bind(object.canvas),
        onComplete: () => {
            object.setCoords(); // Update controls position
            object.canvas?.renderAll.bind(object.canvas);
        },
        easing: (t, b, c, d) => -c * (t /= d) * (t - 2) + b, // easeOutQuad
    });
};

const applySelectedStyle = (obj: FabricObject) => {
    // The node is a group, the first object is the main rectangle
    const rect = (obj as Group)._objects?.[0];
    if (rect) {
        rect.set({
            stroke: 'hsl(var(--primary))',
        });
        animate(obj, { scaleX: 1.03, scaleY: 1.03, strokeWidth: 2 });
    }
};

const applyDeselectedStyle = (obj: FabricObject) => {
    // The node is a group, the first object is the main rectangle
    const rect = (obj as Group)._objects?.[0];
    if (rect) {
        rect.set({
            stroke: 'hsl(var(--border))',
        });
        animate(obj, { scaleX: 1, scaleY: 1, strokeWidth: 1 });
    }
};


export const useSelectionHandler = ({ canvas, onSelectionChange }: SelectionHandlerProps) => {
    const handleSelectionCreated = (e: { selected: FabricObject[] }) => {
        if (!canvas) return;
        
        const selectedIds = e.selected.map((obj: CustomFabricObject) => obj.data?.id).filter(Boolean);
        onSelectionChange(selectedIds, e.selected.length > 1);
    };

    const handleSelectionUpdated = (e: { selected?: FabricObject[], deselected?: FabricObject[], e?: TPointerEvent }) => {
        if (!canvas) return;
        
        const activeSelection = canvas.getActiveObject();
        if (!activeSelection) {
             onSelectionChange([], false);
             return;
        }
        
        const selectedObjects = (activeSelection.type === 'activeSelection')
            ? (activeSelection as ActiveSelection).getObjects()
            : [activeSelection];

        const selectedIds = selectedObjects.map((obj: CustomFabricObject) => obj.data?.id).filter(Boolean);
        const isMultiSelect = !!(e.e && (e.e.ctrlKey || e.e.metaKey));
        onSelectionChange(selectedIds, isMultiSelect || selectedIds.length > 1);
    };

    const handleSelectionCleared = (e: { deselected?: FabricObject[] }) => {
        onSelectionChange([], false);
    };

    return {
        handleSelectionCreated,
        handleSelectionUpdated,
        handleSelectionCleared,
    };
};
