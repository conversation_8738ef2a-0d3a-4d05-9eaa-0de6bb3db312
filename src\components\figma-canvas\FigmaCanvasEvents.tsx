import React, { useEffect } from 'react';
import { Canvas, Point } from 'fabric';
import type * as fabric from 'fabric';

interface FigmaCanvasEventsProps {
  canvas: Canvas | null;
  onZoomChange?: (zoom: number) => void;
  onPanChange?: (pan: Point) => void;
}

export const FigmaCanvasEvents: React.FC<FigmaCanvasEventsProps> = ({
  canvas,
  onZoomChange,
  onPanChange,
}) => {
  // Handle zoom and pan events
  useEffect(() => {
    if (!canvas) return;

    let isPanning = false;
    let lastPanPoint: Point | null = null;

    const handleMouseWheel = (opt: fabric.TEvent) => {
      const e = opt.e as WheelEvent;
      e.preventDefault();
      e.stopPropagation();

      const delta = e.deltaY;
      let zoom = canvas.getZoom();
      
      // Zoom with mouse wheel
      if (e.ctrlKey || e.metaKey) {
        zoom *= 0.999 ** delta;
        zoom = Math.max(0.1, Math.min(10, zoom)); // Limit zoom range
        
        const point = canvas.getPointer(e);
        canvas.zoomToPoint(point, zoom);
        onZoomChange?.(zoom);
      } else {
        // Pan with mouse wheel
        const vpt = canvas.viewportTransform;
        if (vpt) {
          if (e.shiftKey) {
            // Horizontal scroll when shift is held
            vpt[4] -= delta;
          } else {
            // Vertical scroll
            vpt[5] -= delta;
          }
          canvas.setViewportTransform(vpt);
          onPanChange?.(new Point(vpt[4], vpt[5]));
        }
      }
      
      canvas.renderAll();
    };

    const handleMouseDown = (opt: fabric.TEvent) => {
      const e = opt.e as MouseEvent;
      
      // Start panning with middle mouse button or space + left click
      if (e.button === 1 || (e.button === 0 && e.altKey)) {
        isPanning = true;
        canvas.selection = false;
        lastPanPoint = canvas.getPointer(e);
        canvas.setCursor('grab');
        e.preventDefault();
        e.stopPropagation();
      }
    };

    const handleMouseMove = (opt: fabric.TEvent) => {
      if (!isPanning || !lastPanPoint) return;

      const e = opt.e as MouseEvent;
      const currentPoint = canvas.getPointer(e);
      const vpt = canvas.viewportTransform;
      
      if (vpt) {
        vpt[4] += currentPoint.x - lastPanPoint.x;
        vpt[5] += currentPoint.y - lastPanPoint.y;
        canvas.setViewportTransform(vpt);
        onPanChange?.(new Point(vpt[4], vpt[5]));
      }
      
      lastPanPoint = currentPoint;
      canvas.setCursor('grabbing');
      canvas.renderAll();
    };

    const handleMouseUp = (opt: fabric.TEvent) => {
      if (isPanning) {
        isPanning = false;
        lastPanPoint = null;
        canvas.selection = true;
        canvas.setCursor('default');
      }
    };

    // Handle keyboard shortcuts for zoom and pan
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target !== document.body) return; // Only handle when canvas is focused

      switch (e.key) {
        case '0':
          if (e.ctrlKey || e.metaKey) {
            // Reset zoom to 100%
            e.preventDefault();
            canvas.setZoom(1);
            canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
            onZoomChange?.(1);
            onPanChange?.(new Point(0, 0));
            canvas.renderAll();
          }
          break;
        case '=':
        case '+':
          if (e.ctrlKey || e.metaKey) {
            // Zoom in
            e.preventDefault();
            const zoom = Math.min(10, canvas.getZoom() * 1.1);
            const center = canvas.getCenter();
            canvas.zoomToPoint(new Point(center.left, center.top), zoom);
            onZoomChange?.(zoom);
            canvas.renderAll();
          }
          break;
        case '-':
          if (e.ctrlKey || e.metaKey) {
            // Zoom out
            e.preventDefault();
            const zoom = Math.max(0.1, canvas.getZoom() * 0.9);
            const center = canvas.getCenter();
            canvas.zoomToPoint(new Point(center.left, center.top), zoom);
            onZoomChange?.(zoom);
            canvas.renderAll();
          }
          break;
        case '1':
          if (e.ctrlKey || e.metaKey) {
            // Fit to screen
            e.preventDefault();
            fitToScreen();
          }
          break;
        case 'ArrowUp':
          if (e.shiftKey) {
            // Pan up
            e.preventDefault();
            panCanvas(0, 10);
          }
          break;
        case 'ArrowDown':
          if (e.shiftKey) {
            // Pan down
            e.preventDefault();
            panCanvas(0, -10);
          }
          break;
        case 'ArrowLeft':
          if (e.shiftKey) {
            // Pan left
            e.preventDefault();
            panCanvas(10, 0);
          }
          break;
        case 'ArrowRight':
          if (e.shiftKey) {
            // Pan right
            e.preventDefault();
            panCanvas(-10, 0);
          }
          break;
      }
    };

    const panCanvas = (deltaX: number, deltaY: number) => {
      const vpt = canvas.viewportTransform;
      if (vpt) {
        vpt[4] += deltaX;
        vpt[5] += deltaY;
        canvas.setViewportTransform(vpt);
        onPanChange?.(new Point(vpt[4], vpt[5]));
        canvas.renderAll();
      }
    };

    const fitToScreen = () => {
      const objects = canvas.getObjects();
      if (objects.length === 0) return;

      // Calculate bounding box of all objects
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
      
      objects.forEach(obj => {
        const bound = obj.getBoundingRect();
        minX = Math.min(minX, bound.left);
        minY = Math.min(minY, bound.top);
        maxX = Math.max(maxX, bound.left + bound.width);
        maxY = Math.max(maxY, bound.top + bound.height);
      });

      const objectsWidth = maxX - minX;
      const objectsHeight = maxY - minY;
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();

      // Calculate zoom to fit with some padding
      const padding = 50;
      const zoomX = (canvasWidth - padding * 2) / objectsWidth;
      const zoomY = (canvasHeight - padding * 2) / objectsHeight;
      const zoom = Math.min(zoomX, zoomY, 1); // Don't zoom in beyond 100%

      // Calculate center point
      const centerX = (minX + maxX) / 2;
      const centerY = (minY + maxY) / 2;
      
      // Set zoom and pan
      canvas.setZoom(zoom);
      const vpt = canvas.viewportTransform;
      if (vpt) {
        vpt[4] = canvasWidth / 2 - centerX * zoom;
        vpt[5] = canvasHeight / 2 - centerY * zoom;
        canvas.setViewportTransform(vpt);
        onPanChange?.(new Point(vpt[4], vpt[5]));
      }
      
      onZoomChange?.(zoom);
      canvas.renderAll();
    };

    // Add event listeners
    canvas.on('mouse:wheel', handleMouseWheel);
    canvas.on('mouse:down', handleMouseDown);
    canvas.on('mouse:move', handleMouseMove);
    canvas.on('mouse:up', handleMouseUp);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      canvas.off('mouse:wheel', handleMouseWheel);
      canvas.off('mouse:down', handleMouseDown);
      canvas.off('mouse:move', handleMouseMove);
      canvas.off('mouse:up', handleMouseUp);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canvas, onZoomChange, onPanChange]);

  // Handle selection events
  useEffect(() => {
    if (!canvas) return;

    const handleSelectionCreated = (e: Partial<fabric.TEvent> & { selected?: fabric.Object[] }) => {
      // Handle selection created
      console.log('Selection created:', e);
    };

    const handleSelectionUpdated = (e: Partial<fabric.TEvent> & { selected?: fabric.Object[]; deselected?: fabric.Object[] }) => {
      // Handle selection updated
      console.log('Selection updated:', e);
    };

    const handleSelectionCleared = (e: Partial<fabric.TEvent> & { deselected?: fabric.Object[] }) => {
      // Handle selection cleared
      console.log('Selection cleared:', e);
    };

    const handleObjectModified = (e: Partial<fabric.TEvent> & { target?: fabric.Object }) => {
      // Handle object modified (moved, resized, rotated)
      console.log('Object modified:', e);
    };

    canvas.on('selection:created', handleSelectionCreated);
    canvas.on('selection:updated', handleSelectionUpdated);
    canvas.on('selection:cleared', handleSelectionCleared);
    canvas.on('object:modified', handleObjectModified);

    return () => {
      canvas.off('selection:created', handleSelectionCreated);
      canvas.off('selection:updated', handleSelectionUpdated);
      canvas.off('selection:cleared', handleSelectionCleared);
      canvas.off('object:modified', handleObjectModified);
    };
  }, [canvas]);

  return null; // This component doesn't render anything directly
};
