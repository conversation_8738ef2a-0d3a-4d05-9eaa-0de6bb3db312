// Chat Analysis Record Cluster Definition
export interface AnalysisCluster {
  id: string;
  name: string;
  description?: string;
  nodeIds: string[]; // IDs of nodes in this cluster
  color?: number;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
}

/**
 * API service for cluster CRUD operations
 */
export class ClusterApi {
  private static baseUrl = '/api/clusters';

  /**
   * Get all clusters
   */
  static async getAllClusters(): Promise<AnalysisCluster[]> {
    try {
      const response = await fetch(`${this.baseUrl}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch clusters: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching clusters:', error);
      // Return mock data for development
      return this.getMockClusters();
    }
  }

  /**
   * Get cluster by ID
   */
  static async getCluster(id: string): Promise<AnalysisCluster | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch cluster: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching cluster:', error);
      return null;
    }
  }

  /**
   * Create new cluster
   */
  static async createCluster(cluster: Omit<AnalysisCluster, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnalysisCluster> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cluster),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create cluster: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating cluster:', error);
      // Return mock created cluster for development
      const mockCluster: AnalysisCluster = {
        ...cluster,
        id: `cluster_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      return mockCluster;
    }
  }

  /**
   * Update cluster
   */
  static async updateCluster(id: string, updates: Partial<AnalysisCluster>): Promise<AnalysisCluster> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          updatedAt: new Date().toISOString(),
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update cluster: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating cluster:', error);
      throw error;
    }
  }

  /**
   * Delete cluster
   */
  static async deleteCluster(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete cluster: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting cluster:', error);
      throw error;
    }
  }

  /**
   * Add nodes to cluster
   */
  static async addNodesToCluster(clusterId: string, nodeIds: string[]): Promise<AnalysisCluster> {
    try {
      const response = await fetch(`${this.baseUrl}/${clusterId}/nodes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeIds }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to add nodes to cluster: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error adding nodes to cluster:', error);
      throw error;
    }
  }

  /**
   * Remove nodes from cluster
   */
  static async removeNodesFromCluster(clusterId: string, nodeIds: string[]): Promise<AnalysisCluster> {
    try {
      const response = await fetch(`${this.baseUrl}/${clusterId}/nodes`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeIds }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to remove nodes from cluster: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error removing nodes from cluster:', error);
      throw error;
    }
  }

  /**
   * Get clusters containing specific node
   */
  static async getClustersForNode(nodeId: string): Promise<AnalysisCluster[]> {
    try {
      const response = await fetch(`${this.baseUrl}/by-node/${nodeId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch clusters for node: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching clusters for node:', error);
      return [];
    }
  }

  /**
   * Duplicate cluster
   */
  static async duplicateCluster(id: string, newName?: string): Promise<AnalysisCluster> {
    const original = await this.getCluster(id);
    if (!original) {
      throw new Error('Cluster not found');
    }

    const duplicate = {
      ...original,
      name: newName || `${original.name} (Copy)`,
      // Remove fields that should be regenerated
    };
    
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...rest } = duplicate;
    
    return this.createCluster(rest);
  }

  /**
   * Get cluster statistics
   */
  static async getClusterStatistics(): Promise<{
    total: number;
    averageSize: number;
    largestCluster: { id: string; name: string; size: number };
    recentActivity: { date: string; created: number; modified: number }[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/statistics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch cluster statistics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching cluster statistics:', error);
      // Return mock statistics
      return {
        total: 5,
        averageSize: 3.2,
        largestCluster: {
          id: 'cluster_1',
          name: 'AI Research',
          size: 8
        },
        recentActivity: [
          { date: '2024-01-15', created: 1, modified: 2 },
          { date: '2024-01-14', created: 0, modified: 1 },
          { date: '2024-01-13', created: 2, modified: 0 }
        ]
      };
    }
  }

  /**
   * Mock data for development
   */
  private static getMockClusters(): AnalysisCluster[] {
    return [
      {
        id: 'cluster_1',
        name: 'AI Research',
        description: 'Analysis related to artificial intelligence and machine learning',
        nodeIds: ['mock_1', 'mock_3'],
        color: 0x3b82f6,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-15'),
        tags: ['ai', 'research', 'technology']
      },
      {
        id: 'cluster_2',
        name: 'Sustainability',
        description: 'Environmental and sustainability focused analyses',
        nodeIds: ['mock_2'],
        color: 0x10b981,
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-14'),
        tags: ['environment', 'sustainability', 'energy']
      }
    ];
  }
}
