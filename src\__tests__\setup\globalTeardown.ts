/**
 * Global Test Teardown
 * 
 * Cleans up the test environment after all tests have completed,
 * including stopping services, cleaning up data, and generating reports.
 */

import { FullConfig } from '@playwright/test';
import path from 'path';
import fs from 'fs/promises';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  // Generate test reports
  await generateTestReports();

  // Cleanup test data
  await cleanupTestData();

  // Stop mock services
  await stopMockServices();

  // Archive test artifacts
  await archiveTestArtifacts();

  // Generate coverage reports
  await generateCoverageReports();

  // Cleanup temporary files
  await cleanupTempFiles();

  console.log('✅ Global test teardown completed');
}

async function generateTestReports() {
  try {
    console.log('📊 Generating test reports...');

    // Collect test results from different sources
    const testResults = await collectTestResults();

    // Generate summary report
    const summary = generateTestSummary(testResults);
    
    await fs.writeFile(
      path.join('test-results', 'test-summary.json'),
      JSON.stringify(summary, null, 2)
    );

    // Generate HTML report
    const htmlReport = generateHTMLReport(summary);
    await fs.writeFile(
      path.join('test-results', 'test-summary.html'),
      htmlReport
    );

    console.log('✅ Test reports generated');
  } catch (error) {
    console.warn('Failed to generate test reports:', error);
  }
}

async function collectTestResults() {
  const results = {
    unit: null,
    integration: null,
    e2e: null,
    performance: null,
    accessibility: null,
  };

  try {
    // Collect unit test results
    const unitResultsPath = path.join('test-results', 'results.json');
    if (await fileExists(unitResultsPath)) {
      const unitData = await fs.readFile(unitResultsPath, 'utf-8');
      results.unit = JSON.parse(unitData);
    }

    // Collect e2e test results
    const e2eResultsPath = path.join('test-results', 'e2e-results.json');
    if (await fileExists(e2eResultsPath)) {
      const e2eData = await fs.readFile(e2eResultsPath, 'utf-8');
      results.e2e = JSON.parse(e2eData);
    }

    // Collect performance results
    const perfResultsPath = path.join('test-results', 'performance.json');
    if (await fileExists(perfResultsPath)) {
      const perfData = await fs.readFile(perfResultsPath, 'utf-8');
      results.performance = JSON.parse(perfData);
    }

    // Collect accessibility results
    const a11yResultsPath = path.join('test-results', 'accessibility.json');
    if (await fileExists(a11yResultsPath)) {
      const a11yData = await fs.readFile(a11yResultsPath, 'utf-8');
      results.accessibility = JSON.parse(a11yData);
    }
  } catch (error) {
    console.warn('Failed to collect some test results:', error);
  }

  return results;
}

// Types for test results and summary
interface TestResult {
  numTotalTests?: number;
  numPassedTests?: number;
  numFailedTests?: number;
  numPendingTests?: number;
  testResults?: Array<{ perfStats?: { runtime?: number } }>;
  stats?: {
    total?: number;
    passed?: number;
    failed?: number;
    skipped?: number;
    duration?: number;
  };
}
interface PerformanceResult {
  averageLoadTime?: number;
  slowestTest?: string | null;
  memoryUsage?: number | null;
}
interface AccessibilityResult {
  violations?: number;
  passes?: number;
  incomplete?: number;
}
interface TestResults {
  unit?: TestResult | null;
  integration?: TestResult | null;
  e2e?: TestResult | null;
  performance?: PerformanceResult | null;
  accessibility?: AccessibilityResult | null;
  [key: string]: unknown;
}
interface TestSummary {
  timestamp: string;
  environment: {
    nodeVersion: string;
    platform: string;
    ci: boolean;
  };
  totals: {
    tests: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
  };
  coverage: null;
  performance: PerformanceResult | null;
  accessibility: AccessibilityResult | null;
  categories: Record<string, {
    tests: number;
    passed: number;
    failed: number;
    skipped: number;
    duration: number;
  }>;
}

function generateTestSummary(results: TestResults): TestSummary {
  const summary: TestSummary = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      ci: !!process.env.CI,
    },
    totals: {
      tests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
    },
    coverage: null,
    performance: null,
    accessibility: null,
    categories: {},
  };

  // Process unit test results
  if (results.unit) {
    summary.categories.unit = {
      tests: results.unit.numTotalTests || 0,
      passed: results.unit.numPassedTests || 0,
      failed: results.unit.numFailedTests || 0,
      skipped: results.unit.numPendingTests || 0,
      duration: results.unit.testResults?.reduce((acc: number, result: { perfStats?: { runtime?: number } }) => 
        acc + (result.perfStats?.runtime || 0), 0) || 0,
    };

    // Add to totals
    summary.totals.tests += summary.categories.unit.tests;
    summary.totals.passed += summary.categories.unit.passed;
    summary.totals.failed += summary.categories.unit.failed;
    summary.totals.skipped += summary.categories.unit.skipped;
    summary.totals.duration += summary.categories.unit.duration;
  }

  // Process e2e test results
  if (results.e2e) {
    const e2eStats = results.e2e.stats || {};
    summary.categories.e2e = {
      tests: e2eStats.total || 0,
      passed: e2eStats.passed || 0,
      failed: e2eStats.failed || 0,
      skipped: e2eStats.skipped || 0,
      duration: e2eStats.duration || 0,
    };

    // Add to totals
    summary.totals.tests += summary.categories.e2e.tests;
    summary.totals.passed += summary.categories.e2e.passed;
    summary.totals.failed += summary.categories.e2e.failed;
    summary.totals.skipped += summary.categories.e2e.skipped;
    summary.totals.duration += summary.categories.e2e.duration;
  }

  // Process performance results
  if (results.performance) {
    summary.performance = {
      averageLoadTime: results.performance.averageLoadTime || 0,
      slowestTest: results.performance.slowestTest || null,
      memoryUsage: results.performance.memoryUsage || null,
    };
  }

  // Process accessibility results
  if (results.accessibility) {
    summary.accessibility = {
      violations: results.accessibility.violations || 0,
      passes: results.accessibility.passes || 0,
      incomplete: results.accessibility.incomplete || 0,
    };
  }

  return summary;
}

function generateHTMLReport(summary: TestSummary) {
  const passRate = summary.totals.tests > 0 
    ? ((summary.totals.passed / summary.totals.tests) * 100).toFixed(1)
    : '0';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Test Summary Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .passed { color: #22c55e; }
        .failed { color: #ef4444; }
        .skipped { color: #f59e0b; }
        .total { color: #3b82f6; }
        .category { margin-bottom: 20px; }
        .category h3 { border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        th { background: #f9fafb; font-weight: 600; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Test Summary Report</h1>
        <p>Generated: ${summary.timestamp}</p>
        <p>Environment: Node ${summary.environment.nodeVersion} on ${summary.environment.platform}</p>
        <p>Pass Rate: ${passRate}%</p>
      </div>

      <div class="stats">
        <div class="stat-card">
          <div class="stat-value total">${summary.totals.tests}</div>
          <div>Total Tests</div>
        </div>
        <div class="stat-card">
          <div class="stat-value passed">${summary.totals.passed}</div>
          <div>Passed</div>
        </div>
        <div class="stat-card">
          <div class="stat-value failed">${summary.totals.failed}</div>
          <div>Failed</div>
        </div>
        <div class="stat-card">
          <div class="stat-value skipped">${summary.totals.skipped}</div>
          <div>Skipped</div>
        </div>
      </div>

      ${Object.entries(summary.categories).map(([category, stats]: [string, { tests: number; passed: number; failed: number; skipped: number; duration: number }]) => `
        <div class="category">
          <h3>${category.charAt(0).toUpperCase() + category.slice(1)} Tests</h3>
          <table>
            <tr>
              <th>Metric</th>
              <th>Value</th>
            </tr>
            <tr>
              <td>Total</td>
              <td>${stats.tests}</td>
            </tr>
            <tr>
              <td>Passed</td>
              <td class="passed">${stats.passed}</td>
            </tr>
            <tr>
              <td>Failed</td>
              <td class="failed">${stats.failed}</td>
            </tr>
            <tr>
              <td>Skipped</td>
              <td class="skipped">${stats.skipped}</td>
            </tr>
            <tr>
              <td>Duration</td>
              <td>${(stats.duration / 1000).toFixed(2)}s</td>
            </tr>
          </table>
        </div>
      `).join('')}

      ${summary.performance ? `
        <div class="category">
          <h3>Performance</h3>
          <table>
            <tr>
              <th>Metric</th>
              <th>Value</th>
            </tr>
            <tr>
              <td>Average Load Time</td>
              <td>${summary.performance.averageLoadTime}ms</td>
            </tr>
            ${summary.performance.slowestTest ? `
              <tr>
                <td>Slowest Test</td>
                <td>${summary.performance.slowestTest}</td>
              </tr>
            ` : ''}
          </table>
        </div>
      ` : ''}

      ${summary.accessibility ? `
        <div class="category">
          <h3>Accessibility</h3>
          <table>
            <tr>
              <th>Metric</th>
              <th>Value</th>
            </tr>
            <tr>
              <td>Violations</td>
              <td class="failed">${summary.accessibility.violations}</td>
            </tr>
            <tr>
              <td>Passes</td>
              <td class="passed">${summary.accessibility.passes}</td>
            </tr>
            <tr>
              <td>Incomplete</td>
              <td class="skipped">${summary.accessibility.incomplete}</td>
            </tr>
          </table>
        </div>
      ` : ''}
    </body>
    </html>
  `;
}

async function cleanupTestData() {
  try {
    console.log('🗑️ Cleaning up test data...');

    // Remove test data directory
    const testDataPath = path.join(process.cwd(), 'test-data');
    await fs.rm(testDataPath, { recursive: true, force: true });

    // Clean up any temporary test files
    const tempFiles = [
      'auth.json',
      'test-session.json',
      'mock-data.json',
    ];

    for (const file of tempFiles) {
      try {
        await fs.unlink(file);
      } catch (error) {
        // File doesn't exist, ignore
      }
    }

    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.warn('Failed to cleanup test data:', error);
  }
}

async function stopMockServices() {
  try {
    console.log('🛑 Stopping mock services...');

    // Stop any running mock servers
    if (process.env.MOCK_API_ENABLED === 'true') {
      // This would stop the mock API server
      console.log('Stopping mock API server...');
    }

    // Clear service worker mocks
    delete process.env.MOCK_SERVICE_WORKER;
    delete process.env.MOCK_OPENROUTER_API;

    console.log('✅ Mock services stopped');
  } catch (error) {
    console.warn('Failed to stop mock services:', error);
  }
}

async function archiveTestArtifacts() {
  try {
    console.log('📦 Archiving test artifacts...');

    // Create archive directory with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const archiveDir = path.join('test-results', 'archives', timestamp);
    await fs.mkdir(archiveDir, { recursive: true });

    // Archive important artifacts
    const artifactsToArchive = [
      'screenshots',
      'videos',
      'traces',
      'coverage',
      'test-summary.json',
      'test-summary.html',
    ];

    for (const artifact of artifactsToArchive) {
      const sourcePath = path.join('test-results', artifact);
      const targetPath = path.join(archiveDir, artifact);

      try {
        await fs.cp(sourcePath, targetPath, { recursive: true });
      } catch (error) {
        // Artifact doesn't exist, skip
      }
    }

    console.log(`✅ Test artifacts archived to ${archiveDir}`);
  } catch (error) {
    console.warn('Failed to archive test artifacts:', error);
  }
}

async function generateCoverageReports() {
  try {
    console.log('📊 Generating coverage reports...');

    // Check if coverage data exists
    const coveragePath = path.join('coverage', 'coverage-final.json');
    if (await fileExists(coveragePath)) {
      // Coverage reports would be generated by the test runner
      console.log('✅ Coverage reports available in coverage/ directory');
    } else {
      console.log('ℹ️ No coverage data found');
    }
  } catch (error) {
    console.warn('Failed to generate coverage reports:', error);
  }
}

async function cleanupTempFiles() {
  try {
    console.log('🧹 Cleaning up temporary files...');

    // Remove temporary directories
    const tempDirs = [
      '.nyc_output',
      'node_modules/.cache',
      'dist',
      '.vite',
    ];

    for (const dir of tempDirs) {
      try {
        await fs.rm(dir, { recursive: true, force: true });
      } catch (error) {
        // Directory doesn't exist, ignore
      }
    }

    console.log('✅ Temporary files cleaned up');
  } catch (error) {
    console.warn('Failed to cleanup temporary files:', error);
  }
}

// Utility functions
async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

export default globalTeardown;
