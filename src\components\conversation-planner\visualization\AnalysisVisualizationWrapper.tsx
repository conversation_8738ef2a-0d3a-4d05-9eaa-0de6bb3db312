import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ViewMode, VisualizationConfig, createVisualizationChain } from '@/types/visualization';
import {
  LazyVisualizationChain,
  MemoizedVisualization,
  useIntersectionObserver,
  useDebouncedState,
  OptimizedAnimation
} from './PerformanceOptimizer';

interface AnalysisVisualizationWrapperProps {
  results: AnalysisResult[];
  onFollowUpQuestion?: (question: string) => void;
  onResultSelect?: (result: AnalysisResult) => void;
  className?: string;
  // Props for traditional card view (passed through)
  children?: React.ReactNode;
}

const defaultConfig: VisualizationConfig = {
  showAnimations: true,
  showMetadata: true,
  compactMode: false,
  theme: 'default',
  layout: 'horizontal'
};

export const AnalysisVisualizationWrapper: React.FC<AnalysisVisualizationWrapperProps> = ({
  results,
  onFollowUpQuestion,
  onResultSelect,
  className,
  children
}) => {
  const [currentView, setCurrentView] = useState<ViewMode>('cards');
  const [config, setConfig] = useState<VisualizationConfig>(defaultConfig);
  const [selectedResultId, setSelectedResultId] = useState<string | null>(null);

  // Performance optimizations
  const [viewMode, debouncedViewMode, setViewMode] = useDebouncedState(currentView, 200);
  const { ref: containerRef, isIntersecting } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '100px'
  });

  // Memoize expensive computations
  const memoizedResults = useMemo(() => results, [results]);
  const shouldRenderAnimations = useMemo(() =>
    config.showAnimations && results.length < 10, // Disable animations for large datasets
    [config.showAnimations, results.length]
  );

  // Load saved configuration on mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('visualization-config');
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig({ ...defaultConfig, ...parsedConfig });
      } catch (error) {
        console.warn('Failed to parse saved visualization config:', error);
      }
    }

    const savedView = localStorage.getItem('analysis-view-mode');
    if (savedView && ['cards', 'columns', 'chain', 'hybrid'].includes(savedView)) {
      setCurrentView(savedView as ViewMode);
    }
  }, []);

  const handleConfigChange = (newConfig: Partial<VisualizationConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  };

  const handleResultSelect = (result: AnalysisResult) => {
    setSelectedResultId(result.id);
    onResultSelect?.(result);
  };

  const renderVisualizationContent = () => {
    switch (currentView) {
      case 'cards':
        return (
          <motion.div
            key="cards-view"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {children}
          </motion.div>
        );

      case 'columns':
        return (
          <motion.div
            key="columns-view"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="h-[700px]"
          >
            <Enhanced3ColumnView
              results={results}
              onResultSelect={handleResultSelect}
              onFollowUpQuestion={onFollowUpQuestion}
              selectedResultId={selectedResultId}
              showAnimations={config.showAnimations}
              compactMode={config.compactMode}
            />
          </motion.div>
        );

      case 'chain':
        return (
          <motion.div
            key="chain-view"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <AnalysisFlowChain
              results={results}
              selectedResultId={selectedResultId}
              onResultSelect={handleResultSelect}
              onFollowUpQuestion={onFollowUpQuestion}
              autoPlay={config.showAnimations}
              showControls={true}
              compactMode={config.compactMode}
            />
          </motion.div>
        );

      case 'hybrid':
        return (
          <motion.div
            key="hybrid-view"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Split view: Enhanced Columns on top, Flow Chain on bottom */}
            <div className="grid grid-rows-2 gap-6 h-[900px]">
              <div className="border rounded-lg p-4 bg-white shadow-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <span>Enhanced Analysis Overview</span>
                </h3>
                <Enhanced3ColumnView
                  results={results}
                  onResultSelect={handleResultSelect}
                  onFollowUpQuestion={onFollowUpQuestion}
                  selectedResultId={selectedResultId}
                  showAnimations={config.showAnimations}
                  compactMode={true}
                />
              </div>

              <div className="border rounded-lg p-4 bg-white shadow-sm overflow-auto">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <span>Interactive Flow Visualization</span>
                </h3>
                <AnalysisFlowChain
                  results={results}
                  selectedResultId={selectedResultId}
                  onResultSelect={handleResultSelect}
                  onFollowUpQuestion={onFollowUpQuestion}
                  autoPlay={config.showAnimations}
                  showControls={true}
                  compactMode={true}
                />
              </div>
            </div>
          </motion.div>
        );

      default:
        return children;
    }
  };

  if (results.length === 0) {
    return (
      <div className={cn("space-y-4", className)}>
        <ViewToggleManager
          currentView={currentView}
          onViewChange={setCurrentView}
          config={config}
          onConfigChange={handleConfigChange}
          compact={true}
        />
        {children}
      </div>
    );
  }

  return (
    <div ref={containerRef}>
      <ResponsiveVisualizationWrapper className={cn("space-y-6", className)}>
        {/* View Toggle Controls */}
        <ViewToggleManager
          currentView={currentView}
          onViewChange={(view) => {
            setCurrentView(view);
            setViewMode(view);
          }}
          config={config}
          onConfigChange={handleConfigChange}
          compact={config.compactMode}
        />

        {/* Main Content Area */}
        <div className="relative">
          <AnimatePresence mode="wait">
            {renderVisualizationContent()}
          </AnimatePresence>
        </div>

        {/* Results Summary */}
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center text-sm text-gray-500 py-2"
          >
            Displaying {results.length} analysis result{results.length !== 1 ? 's' : ''} in {debouncedViewMode} view
            {selectedResultId && (
              <span className="ml-2 text-blue-600">
                • Selected: {results.find(r => r.id === selectedResultId)?.question.substring(0, 50)}...
              </span>
            )}
          </motion.div>
        )}
      </ResponsiveVisualizationWrapper>
    </div>
  );
};
