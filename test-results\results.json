{"numTotalTestSuites": 39, "numPassedTestSuites": 0, "numFailedTestSuites": 39, "numPendingTestSuites": 0, "numTotalTests": 61, "numPassedTests": 0, "numFailedTests": 61, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1750273772895, "success": false, "testResults": [{"assertionResults": [], "startTime": 1750273772895, "endTime": 1750273772895, "status": "failed", "message": "Failed to resolve import \"three/examples/jsm/controls/OrbitControls\" from \"src/components/visual-analysis/LivingDataCanvas.tsx\". Does the file exist?", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/tests/LivingDataCanvas.test.tsx"}, {"assertionResults": [], "startTime": 1750273772895, "endTime": 1750273772895, "status": "failed", "message": "Failed to resolve import \"three/examples/jsm/controls/OrbitControls\" from \"src/components/visual-analysis/LivingDataCanvas.tsx\". Does the file exist?", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/tests/integration/LivingDataCanvas.integration.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["Living Data Canvas Performance Tests", "Data Processing Performance"], "fullName": "Living Data Canvas Performance Tests Data Processing Performance should process large datasets within acceptable time limits", "status": "failed", "title": "should process large datasets within acceptable time limits", "duration": 2340.3841999999995, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Data Processing Performance"], "fullName": "Living Data Canvas Performance Tests Data Processing Performance should maintain performance with incremental updates", "status": "failed", "title": "should maintain performance with incremental updates", "duration": 1153.7041, "failureMessages": ["AssertionError: expected 231 to be less than 226.5\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:92:31\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "AssertionError: expected 228 to be less than 228\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:92:31\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 62, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Data Processing Performance"], "fullName": "Living Data Canvas Performance Tests Data Processing Performance should handle memory efficiently with large datasets", "status": "failed", "title": "should handle memory efficiently with large datasets", "duration": 354.58739999999943, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 96, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Similarity Calculation Performance"], "fullName": "Living Data Canvas Performance Tests Similarity Calculation Performance should calculate similarities efficiently", "status": "failed", "title": "should calculate similarities efficiently", "duration": 23.325200000000223, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 139, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Similarity Calculation Performance"], "fullName": "Living Data Canvas Performance Tests Similarity Calculation Performance should scale similarity calculations appropriately", "status": "failed", "title": "should scale similarity calculations appropriately", "duration": 108.89120000000003, "failureMessages": ["AssertionError: expected 29 to be less than 20\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:176:29\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "AssertionError: expected 26 to be less than 20\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:176:29\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "AssertionError: expected 47 to be less than 20\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:176:29\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)\n    at runSuite (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1729:8)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 156, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "<PERSON>vas Rendering Performance"], "fullName": "Living Data Canvas Performance Tests Canvas Rendering Performance should maintain target frame rate with many nodes", "status": "failed", "title": "should maintain target frame rate with many nodes", "duration": 8.597200000000157, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 181, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "<PERSON>vas Rendering Performance"], "fullName": "Living Data Canvas Performance Tests Canvas Rendering Performance should handle level-of-detail optimization", "status": "failed", "title": "should handle level-of-detail optimization", "duration": 3.8124000000007072, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 214, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Web Worker Performance"], "fullName": "Living Data Canvas Performance Tests Web Worker Performance should offload heavy processing to workers", "status": "failed", "title": "should offload heavy processing to workers", "duration": 5.075600000000122, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 257, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Performance Monitoring"], "fullName": "Living Data Canvas Performance Tests Performance Monitoring should track performance metrics accurately", "status": "failed", "title": "should track performance metrics accurately", "duration": 951.7835999999998, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 294, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Performance Monitoring"], "fullName": "Living Data Canvas Performance Tests Performance Monitoring should generate performance recommendations", "status": "failed", "title": "should generate performance recommendations", "duration": 4.350699999999961, "failureMessages": ["AssertionError: expected [ Array(1) ] to include 'Consider breaking down long-running o…'\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1191:15)\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/chai/chai.js:1618:25)\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:329:39\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "AssertionError: expected [ Array(1) ] to include 'Consider breaking down long-running o…'\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1191:15)\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/chai/chai.js:1618:25)\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:329:39\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "AssertionError: expected [ Array(1) ] to include 'Consider breaking down long-running o…'\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1191:15)\n    at Proxy.<anonymous> (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/expect/dist/index.js:1029:14)\n    at Proxy.methodWrapper (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/chai/chai.js:1618:25)\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\tests\\performance\\LivingDataCanvas.performance.test.ts:329:39\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:752:26\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1574:12)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 316, "column": 5}, "meta": {}}, {"ancestorTitles": ["Living Data Canvas Performance Tests", "Stress Testing"], "fullName": "Living Data Canvas Performance Tests Stress Testing should handle extreme dataset sizes", "status": "failed", "title": "should handle extreme dataset sizes", "duration": 1764.8891999999996, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 339, "column": 5}, "meta": {}}], "startTime": 1750273774964, "endTime": 1750273781684.8892, "status": "failed", "message": "", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/tests/performance/LivingDataCanvas.performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["FigmaIntegratedCanvas", "Rendering"], "fullName": "FigmaIntegratedCanvas Rendering renders with default props", "status": "failed", "title": "renders with default props", "duration": 53.10919999999987, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Rendering"], "fullName": "FigmaIntegratedCanvas Rendering applies custom className", "status": "failed", "title": "applies custom className", "duration": 13.903099999999995, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Rendering"], "fullName": "FigmaIntegratedCanvas Rendering shows mode toggle when enabled", "status": "failed", "title": "shows mode toggle when enabled", "duration": 18.127000000000407, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 83, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Rendering"], "fullName": "FigmaIntegratedCanvas Rendering hides mode toggle when disabled", "status": "failed", "title": "hides mode toggle when disabled", "duration": 7.116599999999835, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Mode Switching"], "fullName": "FigmaIntegratedCanvas Mode Switching starts with initial mode", "status": "failed", "title": "starts with initial mode", "duration": 7.999699999999848, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 95, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Mode Switching"], "fullName": "FigmaIntegratedCanvas Mode Switching switches to analysis mode", "status": "failed", "title": "switches to analysis mode", "duration": 13.51379999999972, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 103, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Mode Switching"], "fullName": "FigmaIntegratedCanvas Mode Switching switches to hybrid mode", "status": "failed", "title": "switches to hybrid mode", "duration": 19.126200000000154, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 115, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Tool Selection"], "fullName": "FigmaIntegratedCanvas Tool Selection renders all design tools", "status": "failed", "title": "renders all design tools", "duration": 13.290199999999913, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 129, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Tool Selection"], "fullName": "FigmaIntegratedCanvas Tool Selection selects tool when clicked", "status": "failed", "title": "selects tool when clicked", "duration": 26.617499999999836, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 140, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Tool Selection"], "fullName": "FigmaIntegratedCanvas Tool Selection deselects previous tool when new tool is selected", "status": "failed", "title": "deselects previous tool when new tool is selected", "duration": 15.90139999999974, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Panel Management"], "fullName": "FigmaIntegratedCanvas Panel Management shows layers panel by default", "status": "failed", "title": "shows layers panel by default", "duration": 14.23109999999997, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Panel Management"], "fullName": "FigmaIntegratedCanvas Panel Management toggles layers panel", "status": "failed", "title": "toggles layers panel", "duration": 16.688000000000102, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 168, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Panel Management"], "fullName": "FigmaIntegratedCanvas Panel Management shows properties panel by default", "status": "failed", "title": "shows properties panel by default", "duration": 13.703100000000177, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 180, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Panel Management"], "fullName": "FigmaIntegratedCanvas Panel Management shows analysis panel in analysis mode", "status": "failed", "title": "shows analysis panel in analysis mode", "duration": 11.939300000000003, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Panel Management"], "fullName": "FigmaIntegratedCanvas Panel Management shows analysis panel in hybrid mode", "status": "failed", "title": "shows analysis panel in hybrid mode", "duration": 12.575200000000223, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 194, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Canvas Interactions"], "fullName": "FigmaIntegratedCanvas Canvas Interactions handles Figma canvas selection changes", "status": "failed", "title": "handles Figma canvas selection changes", "duration": 8.705800000000181, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 205, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Canvas Interactions"], "fullName": "FigmaIntegratedCanvas Canvas Interactions handles design element creation", "status": "failed", "title": "handles design element creation", "duration": 15.868199999999888, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Responsive Behavior"], "fullName": "FigmaIntegratedCanvas Responsive Behavior adapts to mobile viewport", "status": "failed", "title": "adapts to mobile viewport", "duration": 11.643499999999676, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 238, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Responsive Behavior"], "fullName": "FigmaIntegratedCanvas Responsive Behavior shows full interface on desktop", "status": "failed", "title": "shows full interface on desktop", "duration": 11.641599999999926, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 252, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Erro<PERSON>"], "fullName": "FigmaIntegratedCanvas Error Handling handles canvas loading errors gracefully", "status": "failed", "title": "handles canvas loading errors gracefully", "duration": 13.947700000000168, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 267, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Erro<PERSON>"], "fullName": "FigmaIntegratedCanvas Error Handling shows loading state while components load", "status": "failed", "title": "shows loading state while components load", "duration": 13.32990000000018, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 279, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Accessibility"], "fullName": "FigmaIntegratedCanvas Accessibility has proper ARIA labels", "status": "failed", "title": "has proper ARIA labels", "duration": 11.771400000000085, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 287, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Accessibility"], "fullName": "FigmaIntegratedCanvas Accessibility supports keyboard navigation", "status": "failed", "title": "supports keyboard navigation", "duration": 10.166899999999714, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Accessibility"], "fullName": "FigmaIntegratedCanvas Accessibility has proper heading structure", "status": "failed", "title": "has proper heading structure", "duration": 5.696000000000367, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 314, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Performance"], "fullName": "FigmaIntegratedCanvas Performance lazy loads canvas components", "status": "failed", "title": "lazy loads canvas components", "duration": 10.131000000000313, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["FigmaIntegratedCanvas", "Performance"], "fullName": "FigmaIntegratedCanvas Performance does not render hidden panels", "status": "failed", "title": "does not render hidden panels", "duration": 10.017100000000028, "failureMessages": ["ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)", "ReferenceError: cn is not defined\n    at FigmaIntegratedCanvas (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\components\\visual-analysis\\FigmaIntegratedCanvas.tsx:221:18)\n    at renderWithHooks (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:21)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:5)"], "location": {"line": 333, "column": 5}, "meta": {}}], "startTime": 1750273775434, "endTime": 1750273775817.017, "status": "failed", "message": "", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/components/FigmaIntegratedCanvas.test.tsx"}, {"assertionResults": [], "startTime": 1750273772895, "endTime": 1750273772895, "status": "failed", "message": "React is not defined", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/integration/VisualCanvasIntegration.test.tsx"}, {"assertionResults": [{"ancestorTitles": ["DrawingToolsFactory", "createRectangle"], "fullName": "DrawingToolsFactory createRectangle should create a rectangle with correct dimensions", "status": "failed", "title": "should create a rectangle with correct dimensions", "duration": 4.809999999999491, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 8, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createRectangle"], "fullName": "DrawingToolsFactory createRectangle should handle negative dimensions", "status": "failed", "title": "should handle negative dimensions", "duration": 1.051900000000387, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 22, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createRectangle"], "fullName": "DrawingToolsFactory createRectangle should apply custom styles", "status": "failed", "title": "should apply custom styles", "duration": 1.1051000000006752, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 34, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createCircle"], "fullName": "DrawingToolsFactory createCircle should create a circle with correct radius", "status": "failed", "title": "should create a circle with correct radius", "duration": 1.073899999999412, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 47, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createCircle"], "fullName": "DrawingToolsFactory createCircle should apply custom styles", "status": "failed", "title": "should apply custom styles", "duration": 0.9274000000004889, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 60, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createLine"], "fullName": "DrawingToolsFactory createLine should create a line with correct points", "status": "failed", "title": "should create a line with correct points", "duration": 2.384299999999712, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 73, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createLine"], "fullName": "DrawingToolsFactory createLine should normalize points relative to bounding box", "status": "failed", "title": "should normalize points relative to bounding box", "duration": 1.5325000000011642, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 90, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createArrow"], "fullName": "DrawingToolsFactory createArrow should create an arrow with correct dimensions", "status": "failed", "title": "should create an arrow with correct dimensions", "duration": 1.0177999999996246, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 104, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createArrow"], "fullName": "DrawingToolsFactory createArrow should handle minimum dimensions", "status": "failed", "title": "should handle minimum dimensions", "duration": 0.6905000000006112, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 118, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createText"], "fullName": "DrawingToolsFactory createText should create a text object with correct properties", "status": "failed", "title": "should create a text object with correct properties", "duration": 1.091099999999642, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 130, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createText"], "fullName": "DrawingToolsFactory createText should apply custom styles", "status": "failed", "title": "should apply custom styles", "duration": 4.502200000000812, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 145, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createPen"], "fullName": "DrawingToolsFactory createPen should create a pen object with correct points", "status": "failed", "title": "should create a pen object with correct points", "duration": 1.6775999999990745, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createPen"], "fullName": "DrawingToolsFactory createPen should throw error for empty points", "status": "failed", "title": "should throw error for empty points", "duration": 1.51720000000023, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 175, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createPen"], "fullName": "DrawingToolsFactory createPen should handle single point", "status": "failed", "title": "should handle single point", "duration": 0.7163999999993393, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 181, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createPolygon"], "fullName": "DrawingToolsFactory createPolygon should create a polygon with correct number of sides", "status": "failed", "title": "should create a polygon with correct number of sides", "duration": 1.3752999999996973, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 192, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createPolygon"], "fullName": "DrawingToolsFactory createPolygon should generate correct points for triangle", "status": "failed", "title": "should generate correct points for triangle", "duration": 0.9644000000007509, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 208, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "createStar"], "fullName": "DrawingToolsFactory createStar should create a star with correct properties", "status": "failed", "title": "should create a star with correct properties", "duration": 0.929599999999482, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at runTest (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1597:11)"], "location": {"line": 223, "column": 5}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "getDefaultStyle"], "fullName": "DrawingToolsFactory Utility Functions getDefaultStyle should return correct default styles for each tool", "status": "failed", "title": "should return correct default styles for each tool", "duration": 0.8850000000002183, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 242, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "snapToGrid"], "fullName": "DrawingToolsFactory Utility Functions snapToGrid should snap point to grid when enabled", "status": "failed", "title": "should snap point to grid when enabled", "duration": 0.6797999999998865, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "snapToGrid"], "fullName": "DrawingToolsFactory Utility Functions snapToGrid should not snap when disabled", "status": "failed", "title": "should not snap when disabled", "duration": 0.6431000000011409, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 267, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "calculateDistance"], "fullName": "DrawingToolsFactory Utility Functions calculateDistance should calculate correct distance between points", "status": "failed", "title": "should calculate correct distance between points", "duration": 0.5980999999992491, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 279, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "calculateAngle"], "fullName": "DrawingToolsFactory Utility Functions calculateAngle should calculate correct angle between points", "status": "failed", "title": "should calculate correct angle between points", "duration": 0.5987999999997555, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "calculateAngle"], "fullName": "DrawingToolsFactory Utility Functions calculateAngle should calculate correct angle for vertical line", "status": "failed", "title": "should calculate correct angle for vertical line", "duration": 0.5905000000002474, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 299, "column": 7}, "meta": {}}, {"ancestorTitles": ["DrawingToolsFactory", "Utility Functions", "rotatePoint"], "fullName": "DrawingToolsFactory Utility Functions rotatePoint should rotate point around center", "status": "failed", "title": "should rotate point around center", "duration": 0.6893999999992957, "failureMessages": ["ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)", "ReferenceError: cleanup is not defined\n    at F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\testSetup.ts:173:3\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:155:11\n    at file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1897:20\n    at new Promise (<anonymous>)\n    at runWithTimeout (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1863:10)\n    at runHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1436:51)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1442:25)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)\n    at callSuiteHook (file:///F:/AI%20App%20Builds/My_Github_Projects/chat-craft-trainer-pro-80/node_modules/@vitest/runner/dist/chunk-hooks.js:1449:27)"], "location": {"line": 310, "column": 7}, "meta": {}}], "startTime": 1750273782930, "endTime": 1750273782962.6895, "status": "failed", "message": "", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/components/figma-canvas/__tests__/DrawingToolsFactory.test.ts"}, {"assertionResults": [], "startTime": 1750273772895, "endTime": 1750273772895, "status": "failed", "message": "create is not defined", "name": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/components/figma-canvas/__tests__/FigmaCanvasStore.test.ts"}]}