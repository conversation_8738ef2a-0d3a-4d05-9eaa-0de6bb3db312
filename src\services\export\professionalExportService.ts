import { AnalysisResult } from '@/types/conversation';
import { DesignElement } from '@/services/designIntegration/analysisToDesignConverter';
import { Presentation } from '@/services/designIntegration/presentationGenerator';

/**
 * Professional Export Service
 * 
 * Provides high-quality export capabilities for analysis results, visual designs,
 * and presentations with professional layouts and branding options.
 */


export interface ExportOptions {
  format: ExportFormat;
  quality: 'draft' | 'standard' | 'high' | 'print';
  layout: ExportLayout;
  branding: BrandingOptions;
  metadata: ExportMetadata;
}

export interface ExportLayout {
  pageSize: 'A4' | 'A3' | 'Letter' | 'Legal' | 'Custom';
  orientation: 'portrait' | 'landscape';
  margins: { top: number; right: number; bottom: number; left: number };
  columns: number;
  spacing: number;
  header: boolean;
  footer: boolean;
  pageNumbers: boolean;
}

export interface BrandingOptions {
  logo?: string;
  logoPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  companyName?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
    background: string;
  };
  fonts: {
    heading: string;
    body: string;
    code: string;
  };
  watermark?: string;
}

export interface ExportMetadata {
  title: string;
  author: string;
  subject?: string;
  keywords?: string[];
  createdAt: number;
  version: string;
  confidentiality?: 'public' | 'internal' | 'confidential' | 'restricted';
}

export type ExportFormat = 'pdf' | 'svg' | 'png' | 'jpeg' | 'html' | 'docx' | 'pptx' | 'json';

export interface ExportResult {
  success: boolean;
  data?: Blob | string;
  filename: string;
  size?: number;
  error?: string;
  metadata: ExportMetadata;
}

export class ProfessionalExportService {
  private defaultBranding: BrandingOptions = {
    logoPosition: 'top-right',
    companyName: 'ChatCraft Trainer Pro',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      text: '#1e293b',
      background: '#ffffff'
    },
    fonts: {
      heading: 'Inter',
      body: 'Inter',
      code: 'JetBrains Mono'
    }
  };

  private defaultLayout: ExportLayout = {
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: 72, right: 72, bottom: 72, left: 72 }, // 1 inch margins
    columns: 1,
    spacing: 16,
    header: true,
    footer: true,
    pageNumbers: true
  };

  async exportAnalysisResults(
    results: AnalysisResult[],
    options: Partial<ExportOptions> = {}
  ): Promise<ExportResult> {
    const exportOptions = this.mergeOptions(options);
    
    try {
      switch (exportOptions.format) {
        case 'pdf':
          return await this.exportToPDF(results, exportOptions);
        case 'html':
          return await this.exportToHTML(results, exportOptions);
        case 'docx':
          return await this.exportToDocx(results, exportOptions);
        case 'json':
          return await this.exportToJSON(results, exportOptions);
        default:
          throw new Error(`Unsupported export format: ${exportOptions.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Export failed',
        metadata: exportOptions.metadata
      };
    }
  }

  async exportDesignElements(
    elements: DesignElement[],
    options: Partial<ExportOptions> = {}
  ): Promise<ExportResult> {
    const exportOptions = this.mergeOptions(options);
    
    try {
      switch (exportOptions.format) {
        case 'svg':
          return await this.exportDesignToSVG(elements, exportOptions);
        case 'png':
          return await this.exportDesignToPNG(elements, exportOptions);
        case 'pdf':
          // PDF export for design elements is not implemented; fallback to PNG or throw error
          // return await this.exportDesignToPDF(elements, exportOptions);
          // Fallback: export as PNG or throw error
          // return await this.exportDesignToPNG(elements, exportOptions);
          throw new Error('PDF export for design elements is not implemented.');
        default:
          throw new Error(`Unsupported design export format: ${exportOptions.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Design export failed',
        metadata: exportOptions.metadata
      };
    }
  }

  async exportPresentation(
    presentation: Presentation,
    options: Partial<ExportOptions> = {}
  ): Promise<ExportResult> {
    const exportOptions = this.mergeOptions(options);
    
    try {
      switch (exportOptions.format) {
        case 'pptx':
          return await this.exportToPowerPoint(presentation, exportOptions);
        case 'pdf':
          return await this.exportPresentationToPDF(presentation, exportOptions);
        case 'html':
          return await this.exportPresentationToHTML(presentation, exportOptions);
        default:
          throw new Error(`Unsupported presentation export format: ${exportOptions.format}`);
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Presentation export failed',
        metadata: exportOptions.metadata
      };
    }
  }

  private mergeOptions(options: Partial<ExportOptions>): ExportOptions {
    return {
      format: options.format || 'pdf',
      quality: options.quality || 'standard',
      layout: { ...this.defaultLayout, ...options.layout },
      branding: { ...this.defaultBranding, ...options.branding },
      metadata: {
        title: 'Analysis Export',
        author: 'ChatCraft Trainer Pro',
        createdAt: Date.now(),
        version: '1.0',
        ...options.metadata
      }
    };
  }

  // PDF Export Implementation
  private async exportToPDF(
    results: AnalysisResult[],
    options: ExportOptions
  ): Promise<ExportResult> {
    // This would use a library like jsPDF or Puppeteer
    const htmlContent = this.generateAnalysisHTML(results, options);
    
    // Simulate PDF generation
    const pdfBlob = new Blob([htmlContent], { type: 'application/pdf' });
    
    return {
      success: true,
      data: pdfBlob,
      filename: `${options.metadata.title.replace(/\s+/g, '_')}.pdf`,
      size: pdfBlob.size,
      metadata: options.metadata
    };
  }

  // HTML Export Implementation
  private async exportToHTML(
    results: AnalysisResult[],
    options: ExportOptions
  ): Promise<ExportResult> {
    const htmlContent = this.generateAnalysisHTML(results, options);
    
    return {
      success: true,
      data: htmlContent,
      filename: `${options.metadata.title.replace(/\s+/g, '_')}.html`,
      size: new Blob([htmlContent]).size,
      metadata: options.metadata
    };
  }

  // DOCX Export Implementation
  private async exportToDocx(
    results: AnalysisResult[],
    options: ExportOptions
  ): Promise<ExportResult> {
    // This would use a library like docx
    const docxContent = this.generateDocxContent(results, options);
    const docxBlob = new Blob([docxContent], { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
    
    return {
      success: true,
      data: docxBlob,
      filename: `${options.metadata.title.replace(/\s+/g, '_')}.docx`,
      size: docxBlob.size,
      metadata: options.metadata
    };
  }

  // JSON Export Implementation
  private async exportToJSON(
    results: AnalysisResult[],
    options: ExportOptions
  ): Promise<ExportResult> {
    const jsonData = {
      metadata: options.metadata,
      exportOptions: options,
      results: results.map(result => ({
        ...result,
        timestamp: result.timestamp.toISOString()
      }))
    };
    
    const jsonString = JSON.stringify(jsonData, null, 2);
    
    return {
      success: true,
      data: jsonString,
      filename: `${options.metadata.title.replace(/\s+/g, '_')}.json`,
      size: new Blob([jsonString]).size,
      metadata: options.metadata
    };
  }

  // SVG Export for Design Elements
  private async exportDesignToSVG(
    elements: DesignElement[],
    options: ExportOptions
  ): Promise<ExportResult> {
    const svgContent = this.generateSVGFromElements(elements, options);
    
    return {
      success: true,
      data: svgContent,
      filename: `design_${Date.now()}.svg`,
      size: new Blob([svgContent]).size,
      metadata: options.metadata
    };
  }

  // PNG Export for Design Elements
  private async exportDesignToPNG(
    elements: DesignElement[],
    options: ExportOptions
  ): Promise<ExportResult> {
    // This would use Canvas API or a library like html2canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size based on elements bounds
    const bounds = this.calculateElementsBounds(elements);
    canvas.width = bounds.width;
    canvas.height = bounds.height;
    
    // Render elements to canvas
    this.renderElementsToCanvas(ctx!, elements, options);
    
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve({
          success: true,
          data: blob!,
          filename: `design_${Date.now()}.png`,
          size: blob!.size,
          metadata: options.metadata
        });
      }, 'image/png', options.quality === 'high' ? 1.0 : 0.8);
    });
  }

  // PowerPoint Export
  private async exportToPowerPoint(
    presentation: Presentation,
    options: ExportOptions
  ): Promise<ExportResult> {
    // This would use a library like PptxGenJS
    const pptxBlob = new Blob(['PPTX content'], { 
      type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
    });
    
    return {
      success: true,
      data: pptxBlob,
      filename: `${presentation.title.replace(/\s+/g, '_')}.pptx`,
      size: pptxBlob.size,
      metadata: options.metadata
    };
  }

  // Helper methods
  private generateAnalysisHTML(results: AnalysisResult[], options: ExportOptions): string {
    const { branding, layout, metadata } = options;
    
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${metadata.title}</title>
          <style>
            body {
              font-family: ${branding.fonts.body};
              color: ${branding.colors.text};
              background-color: ${branding.colors.background};
              margin: 0;
              padding: ${layout.margins.top}px ${layout.margins.right}px ${layout.margins.bottom}px ${layout.margins.left}px;
              line-height: 1.6;
            }
            .header {
              border-bottom: 2px solid ${branding.colors.primary};
              padding-bottom: 20px;
              margin-bottom: 30px;
            }
            .title {
              font-family: ${branding.fonts.heading};
              color: ${branding.colors.primary};
              font-size: 2.5em;
              margin: 0;
            }
            .subtitle {
              color: ${branding.colors.secondary};
              font-size: 1.2em;
              margin: 10px 0 0 0;
            }
            .analysis {
              margin-bottom: 40px;
              padding: 20px;
              border-left: 4px solid ${branding.colors.accent};
              background-color: ${branding.colors.background};
            }
            .question {
              font-weight: bold;
              color: ${branding.colors.primary};
              margin-bottom: 15px;
            }
            .content {
              white-space: pre-wrap;
              margin-bottom: 20px;
            }
            .metadata {
              font-size: 0.9em;
              color: ${branding.colors.secondary};
              border-top: 1px solid #e5e7eb;
              padding-top: 10px;
            }
            .footer {
              margin-top: 50px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              text-align: center;
              color: ${branding.colors.secondary};
              font-size: 0.9em;
            }
            @media print {
              body { margin: 0; }
              .analysis { page-break-inside: avoid; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1 class="title">${metadata.title}</h1>
            <p class="subtitle">Generated on ${new Date(metadata.createdAt).toLocaleDateString()}</p>
            ${branding.companyName ? `<p class="subtitle">by ${branding.companyName}</p>` : ''}
          </div>
          
          ${results.map((result, index) => `
            <div class="analysis">
              <div class="question">Question ${index + 1}: ${result.question}</div>
              <div class="content">${result.analysis}</div>
              <div class="metadata">
                Model: ${result.model} | 
                Type: ${result.analysisType} | 
                Style: ${result.style} |
                ${result.rating ? ` Rating: ${result.rating}/10 |` : ''}
                Date: ${result.timestamp.toLocaleDateString()}
              </div>
            </div>
          `).join('')}
          
          <div class="footer">
            <p>Generated by ${branding.companyName || 'ChatCraft Trainer Pro'}</p>
            ${metadata.confidentiality ? `<p>Confidentiality: ${metadata.confidentiality.toUpperCase()}</p>` : ''}
          </div>
        </body>
      </html>
    `;
  }

  private generateDocxContent(results: AnalysisResult[], options: ExportOptions): string {
    // Simplified DOCX content - would use proper DOCX library in real implementation
    return `DOCX content for ${results.length} analysis results`;
  }

  private generateSVGFromElements(elements: DesignElement[], options: ExportOptions): string {
    const bounds = this.calculateElementsBounds(elements);
    
    return `
      <svg width="${bounds.width}" height="${bounds.height}" xmlns="http://www.w3.org/2000/svg">
        ${elements.map(element => this.elementToSVG(element)).join('')}
      </svg>
    `;
  }

  private elementToSVG(element: DesignElement): string {
    switch (element.type) {
      case 'text':
        return `<text x="${element.position.x}" y="${element.position.y}" 
                      fill="${element.style.color}" 
                      font-family="${element.style.fontFamily}"
                      font-size="${element.style.fontSize}">
                  ${element.data.content}
                </text>`;
      case 'shape':
        return `<rect x="${element.position.x}" y="${element.position.y}" 
                      width="${element.size.width}" height="${element.size.height}"
                      fill="${element.style.backgroundColor}"
                      stroke="${element.style.borderColor}"
                      stroke-width="${element.style.borderWidth}" />`;
      default:
        return '';
    }
  }

  private calculateElementsBounds(elements: DesignElement[]): { width: number; height: number } {
    if (elements.length === 0) return { width: 800, height: 600 };
    
    const maxX = Math.max(...elements.map(e => e.position.x + e.size.width));
    const maxY = Math.max(...elements.map(e => e.position.y + e.size.height));
    
    return { width: maxX + 50, height: maxY + 50 }; // Add padding
  }

  private renderElementsToCanvas(
    ctx: CanvasRenderingContext2D, 
    elements: DesignElement[], 
    options: ExportOptions
  ): void {
    // Set background
    ctx.fillStyle = options.branding.colors.background;
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // Render each element
    elements.forEach(element => {
      this.renderElementToCanvas(ctx, element);
    });
  }

  private renderElementToCanvas(ctx: CanvasRenderingContext2D, element: DesignElement): void {
    switch (element.type) {
      case 'text':
        ctx.fillStyle = element.style.color;
        ctx.font = `${element.style.fontSize}px ${element.style.fontFamily}`;
        if (element.data && isTextElementData(element.data)) {
          ctx.fillText(element.data.content, element.position.x, element.position.y);
        }
        break;
      case 'shape':
        ctx.fillStyle = element.style.backgroundColor || '#ffffff';
        ctx.fillRect(element.position.x, element.position.y, element.size.width, element.size.height);
        if (element.style.borderColor && element.style.borderWidth) {
          ctx.strokeStyle = element.style.borderColor;
          ctx.lineWidth = element.style.borderWidth;
          ctx.strokeRect(element.position.x, element.position.y, element.size.width, element.size.height);
        }
        break;
    }
  }

  // Presentation-specific exports
  private async exportPresentationToPDF(
    presentation: Presentation,
    options: ExportOptions
  ): Promise<ExportResult> {
    // Convert presentation slides to PDF
    const htmlContent = this.generatePresentationHTML(presentation, options);
    const pdfBlob = new Blob([htmlContent], { type: 'application/pdf' });
    
    return {
      success: true,
      data: pdfBlob,
      filename: `${presentation.title.replace(/\s+/g, '_')}.pdf`,
      size: pdfBlob.size,
      metadata: options.metadata
    };
  }

  private async exportPresentationToHTML(
    presentation: Presentation,
    options: ExportOptions
  ): Promise<ExportResult> {
    const htmlContent = this.generatePresentationHTML(presentation, options);
    
    return {
      success: true,
      data: htmlContent,
      filename: `${presentation.title.replace(/\s+/g, '_')}_presentation.html`,
      size: new Blob([htmlContent]).size,
      metadata: options.metadata
    };
  }

  private generatePresentationHTML(presentation: Presentation, options: ExportOptions): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${presentation.title}</title>
          <style>
            body { 
              font-family: ${options.branding.fonts.body}; 
              margin: 0; 
              background: ${options.branding.colors.background};
            }
            .slide { 
              width: 100vw; 
              height: 100vh; 
              padding: 50px; 
              box-sizing: border-box;
              page-break-after: always;
              display: flex;
              flex-direction: column;
            }
            .slide h1 { 
              color: ${options.branding.colors.primary}; 
              font-family: ${options.branding.fonts.heading};
            }
            @media print {
              .slide { page-break-after: always; }
            }
          </style>
        </head>
        <body>
          ${presentation.slides.map(slide => `
            <div class="slide">
              <h1>${slide.title}</h1>
              ${slide.content.map(content => {
                let text = '';
                if (content.data && isSlideTextData(content.data)) {
                  text = content.data.text;
                }
                return `<div>${text}</div>`;
              }).join('')}
            </div>
          `).join('')}
        </body>
      </html>
    `;
  }
}

// Type guard for text data in DesignElement
type TextElementData = { content: string };
function isTextElementData(data: Record<string, unknown>): data is TextElementData {
  return typeof data.content === 'string';
}

// Type guard for text data in presentation slide content
type SlideTextData = { text: string };
function isSlideTextData(data: unknown): data is SlideTextData {
  return (
    typeof data === 'object' &&
    data !== null &&
    'text' in data &&
    typeof (data as any).text === 'string'
  );
}

export const professionalExportService = new ProfessionalExportService();
