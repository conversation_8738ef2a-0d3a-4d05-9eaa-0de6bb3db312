import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ZoomIn, ZoomOut, Move, Link, RotateCcw, LayoutGrid, Search, Filter, Link2Off } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";


interface CanvasToolbarProps {
  zoom: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  selectedNodesCount: number;
  onConnectNodes: () => void;
  snapToGrid: boolean;
  onToggleSnapToGrid: () => void;
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  analysisTypeFilter: string[];
  onAnalysisTypeFilterChange: (types: string[]) => void;
  allTags: string[];
  tagFilter: string[];
  onTagFilterChange: (tags: string[]) => void;
  areSelectedNodesConnected: boolean;
}

const analysisTypes = [
  { value: "multiple", label: "Multiple" },
  { value: "deep", label: "Deep" },
  { value: "character", label: "Character" },
];

export const CanvasToolbar: React.FC<CanvasToolbarProps> = ({
  zoom,
  onZoomIn,
  onZoomOut,
  onResetView,
  selectedNodesCount,
  onConnectNodes,
  snapToGrid,
  onToggleSnapToGrid,
  searchQuery,
  onSearchQueryChange,
  analysisTypeFilter,
  onAnalysisTypeFilterChange,
  allTags,
  tagFilter,
  onTagFilterChange,
  areSelectedNodesConnected,
}) => {
  return <div id="canvas-toolbar" className="flex items-center justify-between p-3 bg-white border-b border-gray-200 gap-4 flex-wrap">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={onZoomOut} title="Zoom out">
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Badge variant="secondary" className="min-w-[60px] justify-center">
          {Math.round(zoom * 100)}%
        </Badge>
        <Button variant="outline" size="sm" onClick={onZoomIn} title="Zoom in">
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" onClick={onResetView} title="Reset view">
          <RotateCcw className="h-4 w-4 mr-1" />
          Reset
        </Button>
        <Button variant="outline" size="sm" onClick={onToggleSnapToGrid} data-state={snapToGrid ? 'on' : 'off'} className="data-[state=on]:bg-accent data-[state=on]:text-accent-foreground" title="Toggle snap to grid">
          <LayoutGrid className="h-4 w-4 mr-1" />
          Snap
        </Button>
      </div>

      <div className="flex-1 flex items-center justify-center gap-4 min-w-[300px]">
        <div className="relative w-full max-w-xs">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search notes..."
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => onSearchQueryChange(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <ToggleGroup
            type="multiple"
            variant="outline"
            size="sm"
            value={analysisTypeFilter}
            onValueChange={onAnalysisTypeFilterChange}
            className="hidden md:flex"
          >
            {analysisTypes.map((type) => (
              <ToggleGroupItem key={type.value} value={type.value} aria-label={`Toggle ${type.label}`}>
                {type.label}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="hidden md:flex">
                <Filter className="h-4 w-4 mr-1" />
                Tags
                {tagFilter.length > 0 && <Badge variant="secondary" className="ml-2">{tagFilter.length}</Badge>}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="center">
              <DropdownMenuLabel>Filter by Tag</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {allTags.length > 0 ? (
                allTags.map((tag) => (
                  <DropdownMenuCheckboxItem
                    key={tag}
                    checked={tagFilter.includes(tag)}
                    onCheckedChange={(checked) => {
                      const newFilter = checked
                        ? [...tagFilter, tag]
                        : tagFilter.filter(t => t !== tag);
                      onTagFilterChange(newFilter);
                    }}
                  >
                    {tag}
                  </DropdownMenuCheckboxItem>
                ))
              ) : (
                <div className="px-2 py-1.5 text-sm text-muted-foreground">No tags found</div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        {selectedNodesCount > 0 && <Badge variant="default" className="bg-zinc-900 text-white">
            {selectedNodesCount} selected
          </Badge>}
        {selectedNodesCount >= 2 && (
          <Button
            variant={areSelectedNodesConnected ? "destructive" : "default"}
            size="sm"
            onClick={onConnectNodes}
            title={areSelectedNodesConnected ? "Disconnect selected nodes" : "Connect selected nodes"}
          >
            {areSelectedNodesConnected ? <Link2Off className="h-4 w-4 mr-1" /> : <Link className="h-4 w-4 mr-1" />}
            {areSelectedNodesConnected ? "Disconnect" : "Connect"}
          </Button>
        )}
        <div className="text-sm text-gray-500 flex-shrink-0 items-center gap-1 hidden lg:flex">
          <Move className="h-3 w-3" />
          Drag to pan • Click nodes to select
        </div>
      </div>
    </div>;
};
