import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ParsedDeepAnalysis } from '@/services/openRouter/types/parsedOutput';
import { FormattedContentDisplay } from './ContentFormatter';
import { ActionableListSection } from './ActionableListSection';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  MessageSquare,
  HelpCircle,
  Brain,
  Target,
  Shield,
  Eye,
  ChevronDown,
  ChevronRight,
  Lightbulb,
  ArrowRight
} from 'lucide-react';

interface DeepAnalysisDisplayProps {
  analysis: ParsedDeepAnalysis;
  onFollowUpQuestion: (question: string) => void;
  onItemSelectForChat?: (item: string) => void;
  selectedItems?: string[];
  onItemToggleSelect?: (item: string) => void;
  visualMode?: boolean;
  showProgress?: boolean;
}

interface SectionProps {
  title: string;
  content?: string;
  icon?: React.ComponentType<{ className?: string }>;
  color?: string;
  visualMode?: boolean;
  index?: number;
}

const Section: React.FC<SectionProps> = ({
  title,
  content,
  icon: IconComponent = Brain,
  color = "blue",
  visualMode = false,
  index = 0
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  if (!content) return null;

  const colorConfig = {
    blue: { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800', accent: 'bg-blue-500' },
    green: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800', accent: 'bg-green-500' },
    purple: { bg: 'bg-purple-50', border: 'border-purple-200', text: 'text-purple-800', accent: 'bg-purple-500' },
    orange: { bg: 'bg-orange-50', border: 'border-orange-200', text: 'text-orange-800', accent: 'bg-orange-500' },
    red: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800', accent: 'bg-red-500' }
  };

  const config = colorConfig[color as keyof typeof colorConfig] || colorConfig.blue;

  if (visualMode) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1, duration: 0.4 }}
      >
        <Card className={`${config.border} shadow-sm hover:shadow-md transition-shadow duration-200`}>
          <CardHeader className={`${config.bg} pb-3`}>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${config.accent}`}>
                  <IconComponent className="w-4 h-4 text-white" />
                </div>
                <span className={`text-lg font-semibold ${config.text}`}>{title}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-1"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </Button>
            </CardTitle>
          </CardHeader>
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <CardContent className="pt-4">
                  <FormattedContentDisplay content={content} />
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </motion.div>
    );
  }

  // Traditional layout
  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold mb-4 text-slate-800 border-b border-slate-200 pb-2 flex items-center">
        <div className={`w-1 h-6 ${config.accent} rounded-full mr-3`}></div>
        {title}
      </h3>
      <FormattedContentDisplay content={content} />
    </div>
  );
};

export const DeepAnalysisDisplay: React.FC<DeepAnalysisDisplayProps> = ({
  analysis,
  onFollowUpQuestion,
  onItemSelectForChat,
  selectedItems,
  onItemToggleSelect,
  visualMode = false,
  showProgress = false
}) => {
  const handleItemSelect = onItemSelectForChat ?? onFollowUpQuestion;

  // Calculate completion progress based on available sections
  const sections = [
    { key: 'summary', content: analysis.summary },
    { key: 'key_concepts', content: analysis.key_concepts },
    { key: 'main_argument', content: analysis.main_argument },
    { key: 'counterarguments', content: analysis.counterarguments },
    { key: 'assumptions', content: analysis.assumptions }
  ];

  const completedSections = sections.filter(s => s.content).length;
  const progressPercentage = (completedSections / sections.length) * 100;

  if (visualMode) {
    return (
      <div className="space-y-6">
        {/* Progress Indicator */}
        {showProgress && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-100"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Analysis Completeness</span>
              <Badge variant="secondary" className="text-xs">
                {completedSections}/{sections.length} sections
              </Badge>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </motion.div>
        )}

        {/* Analysis Flow */}
        <div className="space-y-4">
          <Section
            title="Summary"
            content={analysis.summary}
            icon={Eye}
            color="blue"
            visualMode={true}
            index={0}
          />

          <div className="flex justify-center">
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>

          <Section
            title="Key Concepts"
            content={analysis.key_concepts}
            icon={Lightbulb}
            color="green"
            visualMode={true}
            index={1}
          />

          <div className="flex justify-center">
            <ArrowRight className="w-5 h-5 text-gray-400" />
          </div>

          <Section
            title="Main Argument"
            content={analysis.main_argument}
            icon={Target}
            color="purple"
            visualMode={true}
            index={2}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <Section
              title="Counterarguments"
              content={analysis.counterarguments}
              icon={Shield}
              color="orange"
              visualMode={true}
              index={3}
            />

            <Section
              title="Assumptions"
              content={analysis.assumptions}
              icon={Brain}
              color="red"
              visualMode={true}
              index={4}
            />
          </div>
        </div>

        {/* Interactive Sections */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-4"
        >
          <ActionableListSection
            title="Possible Responses You Might Hear"
            description="Here are realistic responses the other person might give:"
            items={analysis.possibleResponses || []}
            onItemSelect={handleItemSelect}
            IconComponent={MessageSquare}
            isItemSelected={(item) => selectedItems?.includes(item) ?? false}
            onItemToggleSelect={onItemToggleSelect}
          />

          <ActionableListSection
            title="Follow-Up Questions"
            description="Use these AI-generated questions to continue the conversation:"
            items={analysis.followUpQuestions || []}
            onItemSelect={handleItemSelect}
            IconComponent={HelpCircle}
            isItemSelected={(item) => selectedItems?.includes(item) ?? false}
            onItemToggleSelect={onItemToggleSelect}
          />
        </motion.div>
      </div>
    );
  }

  // Traditional layout
  return (
    <div className="space-y-8">
      <Section title="Summary" content={analysis.summary} />
      <Section title="Key Concepts" content={analysis.key_concepts} />
      <Section title="Main Argument" content={analysis.main_argument} />
      <Section title="Counterarguments" content={analysis.counterarguments} />
      <Section title="Assumptions" content={analysis.assumptions} />

      <ActionableListSection
        title="Possible Responses You Might Hear"
        description="Here are realistic responses the other person might give:"
        items={analysis.possibleResponses || []}
        onItemSelect={handleItemSelect}
        IconComponent={MessageSquare}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />

      <ActionableListSection
        title="Follow-Up Questions"
        description="Use these AI-generated questions to continue the conversation:"
        items={analysis.followUpQuestions || []}
        onItemSelect={handleItemSelect}
        IconComponent={HelpCircle}
        isItemSelected={(item) => selectedItems?.includes(item) ?? false}
        onItemToggleSelect={onItemToggleSelect}
      />
    </div>
  );
};
