/**
 * Integration Tests for Visual Canvas System
 * 
 * Tests the complete integration between Figma tools, analysis visualization,
 * and the unified canvas system including data flow and state management.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@/utils/test-utils';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { IntegratedCanvasContainer } from '@/components/visual-analysis/IntegratedCanvasContainer';
import { useCanvasDataBridge } from '@/hooks/useCanvasDataBridge';
import { useAnalysisResults } from '@/hooks/useAnalysisResults';
import { AnalysisResult } from '@/types/conversation';

// Mock hooks
vi.mock('@/hooks/useCanvasDataBridge');
vi.mock('@/hooks/useAnalysisResults');
vi.mock('@/stores/useUIStore');

// Mock analysis data
const mockAnalysisResults: AnalysisResult[] = [
  {
    id: 'analysis-1',
    question: 'What are the benefits of AI in education?',
    analysis: 'AI in education offers personalized learning, automated grading, and intelligent tutoring systems.',
    style: 'professional',
    model: 'gpt-4',
    timestamp: new Date(),
    analysisType: 'multiple',
    rating: 8,
    followUpQuestions: ['How can AI personalize learning?', 'What are the challenges?']
  },
  {
    id: 'analysis-2',
    question: 'How does machine learning work?',
    analysis: 'Machine learning uses algorithms to find patterns in data and make predictions.',
    style: 'professional', // Use a valid ConversationStyle
    model: 'claude-3',
    timestamp: new Date(),
    analysisType: 'deep',
    rating: 9
  }
];

describe('Visual Canvas Integration', () => {
  const mockCanvasDataBridge = {
    openCanvasWithAnalysis: vi.fn(),
    exportCanvasData: vi.fn(),
    importCanvasData: vi.fn(),
    clearCanvas: vi.fn(),
    canvasState: {
      isOpen: false,
      mode: 'standard',
      data: null,
      analysisData: [],
      selectedAnalysis: null,
      canvasReady: false,
      lastSync: null,
    }
  };

  beforeEach(() => {
    // Use a minimal valid CanvasDataBridge mock to match the type
    vi.mocked(useCanvasDataBridge).mockReturnValue({
      ...mockCanvasDataBridge,
      state: {
        analysisData: [],
        selectedAnalysis: null,
        canvasReady: false,
        lastSync: null,
      },
      sendAnalysisToCanvas: vi.fn(),
      clearCanvasData: vi.fn(),
      syncWithStores: vi.fn(),
      getCanvasData: vi.fn(),
      returnFromCanvas: vi.fn(),
      setCanvasReady: vi.fn(),
      selectAnalysis: vi.fn(),
      getAnalysisById: vi.fn(),
    });
    vi.mocked(useAnalysisResults).mockReturnValue({
      handleAnalyze: vi.fn(),
      handleSaveAnalysis: vi.fn(),
      handleUpdateResult: vi.fn(),
      handleDeleteResult: vi.fn(),
      handleNewQuery: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Canvas Mode Integration', () => {
    it('renders all canvas modes', () => {
      render(<IntegratedCanvasContainer />);
      
      expect(screen.getByText('Design Studio')).toBeInTheDocument();
      expect(screen.getByText('Enhanced Canvas')).toBeInTheDocument();
      expect(screen.getByText('Safe Mode')).toBeInTheDocument();
      expect(screen.getByText('Chat Analysis')).toBeInTheDocument();
    });

    it('switches between canvas modes', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Start with Design Studio (Figma-integrated)
      const designStudioTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designStudioTab);
      
      expect(designStudioTab).toHaveAttribute('aria-selected', 'true');
    });

    it('opens canvas with analysis data', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Simulate opening canvas with analysis
      const openButton = screen.getByRole('button', { name: /open canvas/i });
      await user.click(openButton);
      
      expect(mockCanvasDataBridge.openCanvasWithAnalysis).toHaveBeenCalled();
    });
  });

  describe('Data Flow Integration', () => {
    it('passes analysis data to canvas', async () => {
      render(<IntegratedCanvasContainer />);
      
      // Verify that analysis results are available to the canvas
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
    });

    it('handles analysis result selection', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Find and click on an analysis result
      const analysisItem = screen.getByText(mockAnalysisResults[0].question);
      await user.click(analysisItem);
      
      // Should open canvas with the selected analysis
      expect(mockCanvasDataBridge.openCanvasWithAnalysis).toHaveBeenCalledWith(
        mockAnalysisResults[0],
        'figma-integrated'
      );
    });

    it('exports canvas data with analysis context', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);
      
      expect(mockCanvasDataBridge.exportCanvasData).toHaveBeenCalled();
    });
  });

  describe('Design Tool Integration', () => {
    it('integrates Figma tools with analysis data', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Switch to Design Studio mode
      const designTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designTab);
      
      // Verify design tools are available
      await waitFor(() => {
        expect(screen.getByTitle('Rectangle')).toBeInTheDocument();
        expect(screen.getByTitle('Text')).toBeInTheDocument();
        expect(screen.getByTitle('Pen')).toBeInTheDocument();
      });
    });

    it('converts analysis to design elements', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Switch to Design Studio mode
      const designTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designTab);
      
      // Find convert button in analysis bridge
      const convertButton = screen.getByRole('button', { name: /convert to design/i });
      await user.click(convertButton);
      
      // Should create design elements from analysis
      await waitFor(() => {
        expect(screen.getByText(/design element created/i)).toBeInTheDocument();
      });
    });
  });

  describe('State Management Integration', () => {
    it('maintains state across mode switches', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Select an analysis
      const analysisItem = screen.getByText(mockAnalysisResults[0].question);
      await user.click(analysisItem);
      
      // Switch modes
      const enhancedTab = screen.getByRole('tab', { name: /enhanced canvas/i });
      await user.click(enhancedTab);
      
      const designTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designTab);
      
      // Analysis should still be selected
      expect(analysisItem).toHaveClass('selected'); // Assuming selected class
    });

    it('persists canvas state', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Make changes to canvas
      const rectangleTool = screen.getByTitle('Rectangle');
      await user.click(rectangleTool);
      
      // Switch modes and back
      const enhancedTab = screen.getByRole('tab', { name: /enhanced canvas/i });
      await user.click(enhancedTab);
      
      const designTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designTab);
      
      // Tool selection should be preserved
      expect(rectangleTool).toHaveClass('bg-background'); // Selected state
    });
  });

  describe('Performance Integration', () => {
    it('lazy loads canvas components', async () => {
      render(<IntegratedCanvasContainer />);
      
      // Initially shows loading
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
      
      // After loading, shows content
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
    });

    it('handles large datasets efficiently', async () => {
      // Mock large dataset
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        ...mockAnalysisResults[0],
        id: `analysis-${i}`,
        question: `Question ${i}`
      }));
      
      render(<IntegratedCanvasContainer />);
      
      // Should render without performance issues
      await waitFor(() => {
        expect(screen.getByText(/analysis results/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('handles canvas loading errors', async () => {
      // Mock error state
      mockCanvasDataBridge.canvasState = {
        isOpen: false,
        mode: 'standard',
        data: null,
        analysisData: [],
        selectedAnalysis: null,
        canvasReady: false,
        lastSync: null,
        // error: 'Failed to load canvas' // Removed to match type
      };
      
      render(<IntegratedCanvasContainer />);
      
      expect(screen.getByText(/error loading canvas/i)).toBeInTheDocument();
    });

    it('handles analysis loading errors', async () => {
      render(<IntegratedCanvasContainer />);
      
      expect(screen.getByText(/error loading analysis/i)).toBeInTheDocument();
    });

    it('recovers from errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Start with error state
      const handleAnalyze = vi.fn();
      vi.mocked(useAnalysisResults).mockReturnValue({
        handleAnalyze,
        handleSaveAnalysis: vi.fn(),
        handleUpdateResult: vi.fn(),
        handleDeleteResult: vi.fn(),
        handleNewQuery: vi.fn(),
      });
      
      render(<IntegratedCanvasContainer />);
      
      // Click retry button
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);
      
      // Should attempt to reload
      expect(handleAnalyze).toHaveBeenCalled();
    });
  });

  describe('Accessibility Integration', () => {
    it('maintains accessibility across modes', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      // Check tab navigation
      const tabs = screen.getAllByRole('tab');
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
      
      // Test keyboard navigation
      tabs[0].focus();
      await user.keyboard('{ArrowRight}');
      expect(tabs[1]).toHaveFocus();
    });

    it('provides proper ARIA labels for canvas elements', () => {
      render(<IntegratedCanvasContainer />);
      
      const canvas = screen.getByRole('main');
      expect(canvas).toHaveAttribute('aria-label');
    });

    it('announces mode changes to screen readers', async () => {
      const user = userEvent.setup();
      render(<IntegratedCanvasContainer />);
      
      const designTab = screen.getByRole('tab', { name: /design studio/i });
      await user.click(designTab);
      
      // Should have live region for announcements
      expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('updates canvas when analysis results change', async () => {
      const { rerender } = render(<IntegratedCanvasContainer />);
      
      // Update analysis results
      const updatedResults = [
        ...mockAnalysisResults,
        {
          id: 'analysis-3',
          question: 'New analysis question',
          analysis: 'New analysis content',
          style: 'creative',
          model: 'gpt-4',
          timestamp: new Date(),
          analysisType: 'character',
          rating: 7
        }
      ];
      
      rerender(<IntegratedCanvasContainer />);
      
      // Should show new analysis
      expect(screen.getByText('New analysis question')).toBeInTheDocument();
    });

    it('syncs state between multiple canvas instances', async () => {
      // This would test if multiple canvas instances stay in sync
      // Implementation depends on global state management
      render(<IntegratedCanvasContainer />);
      
      // Verify state synchronization mechanisms are in place
      expect(mockCanvasDataBridge.canvasState).toBeDefined();
    });
  });
});
