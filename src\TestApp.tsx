import React from 'react';

const TestApp = () => {
  console.log('🧪 TestApp rendering...');
  
  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        backgroundColor: 'red',
        color: 'white',
        padding: '20px',
        fontSize: '24px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        🧪 TEST REACT APP - If you can see this, React is working!
      </div>
      
      <div style={{
        backgroundColor: 'blue',
        color: 'white',
        padding: '20px',
        fontSize: '18px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        📊 React Info:
        <br />React Version: {React.version}
        <br />Environment: {process.env.NODE_ENV}
        <br />Timestamp: {new Date().toISOString()}
      </div>
      
      <div style={{
        backgroundColor: 'green',
        color: 'white',
        padding: '20px',
        fontSize: '16px',
        borderRadius: '8px'
      }}>
        ✅ If you can see this, the following are working:
        <ul>
          <li>HTML structure</li>
          <li>CSS styling</li>
          <li>React rendering</li>
          <li>TypeScript compilation</li>
          <li>Vite bundling</li>
        </ul>
      </div>
    </div>
  );
};

export default TestApp;
