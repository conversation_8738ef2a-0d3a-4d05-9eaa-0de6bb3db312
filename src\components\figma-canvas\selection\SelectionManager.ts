import { Canvas, Object as FabricObject, Group, ActiveSelection, Rect, TEvent } from 'fabric';
import { DrawingObject, Point, Transform } from '@/types/figma';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';

interface FabricObjectWithData extends FabricObject {
  data?: {
    id: string;
  };
  originalWidth?: number;
  originalHeight?: number;
}

export class SelectionManager {
  private canvas: Canvas;
  private store: ReturnType<typeof useFigmaCanvasStore>;

  constructor(canvas: Canvas, store: ReturnType<typeof useFigmaCanvasStore>) {
    this.canvas = canvas;
    this.store = store;
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Selection events
    this.canvas.on('selection:created', this.handleSelectionCreated.bind(this));
    this.canvas.on('selection:updated', this.handleSelectionUpdated.bind(this));
    this.canvas.on('selection:cleared', this.handleSelectionCleared.bind(this));
    
    // Object modification events
    this.canvas.on('object:modified', this.handleObjectModified.bind(this));
    this.canvas.on('object:moving', this.handleObjectMoving.bind(this));
    this.canvas.on('object:scaling', this.handleObjectScaling.bind(this));
    this.canvas.on('object:rotating', this.handleObjectRotating.bind(this));
    
    // Mouse events for selection
    this.canvas.on('mouse:down', this.handleMouseDown.bind(this));
    this.canvas.on('mouse:move', this.handleMouseMove.bind(this));
    this.canvas.on('mouse:up', this.handleMouseUp.bind(this));
  }

  private handleSelectionCreated(e: unknown) {
    const selectedObjects = this.getSelectedFabricObjects();
    const objectIds = selectedObjects.map(obj => (obj as FabricObjectWithData).data?.id).filter(Boolean);
    this.store.selectObjects(objectIds);
  }

  private handleSelectionUpdated(e: unknown) {
    const selectedObjects = this.getSelectedFabricObjects();
    const objectIds = selectedObjects.map(obj => (obj as FabricObjectWithData).data?.id).filter(Boolean);
    this.store.selectObjects(objectIds);
  }

  private handleSelectionCleared(e: unknown) {
    this.store.clearSelection();
  }

  private handleObjectModified(e: unknown) {
    const target = (e as { target?: FabricObject }).target;
    if (!target) return;

    if (target instanceof ActiveSelection) {
      // Handle multiple object modification
      target.forEachObject((obj) => {
        this.updateObjectFromFabric(obj);
      });
    } else {
      // Handle single object modification
      this.updateObjectFromFabric(target);
    }
  }

  private handleObjectMoving(e: unknown) {
    const target = (e as { target?: FabricObject }).target;
    if (!target) return;

    // Apply snapping if enabled
    if (this.store.snapToGrid) {
      this.applyGridSnapping(target);
    }
  }

  private handleObjectScaling(e: unknown) {
    const target = (e as { target?: FabricObject }).target;
    if (!target) return;

    // Maintain aspect ratio if shift is held
    const pointerEvent = (e as { e?: PointerEvent }).e;
    if (pointerEvent && 'shiftKey' in pointerEvent && pointerEvent.shiftKey) {
      this.maintainAspectRatio(target);
    }
  }

  private handleObjectRotating(e: unknown) {
    const target = (e as { target?: FabricObject }).target;
    if (!target) return;

    // Snap to 15-degree increments if shift is held
    const pointerEvent = (e as { e?: PointerEvent }).e;
    if (pointerEvent && 'shiftKey' in pointerEvent && pointerEvent.shiftKey) {
      const angle = target.angle || 0;
      const snappedAngle = Math.round(angle / 15) * 15;
      target.angle = snappedAngle;
    }
  }

  private handleMouseDown(e: TEvent) {
    // Handle marquee selection start
    const target = (e as { target?: FabricObject }).target;
    if (!target && this.store.activeTool === 'select') {
      this.startMarqueeSelection(e);
    }
  }

  private handleMouseMove(e: TEvent) {
    // Handle marquee selection
    if (this.isMarqueeSelecting) {
      this.updateMarqueeSelection(e);
    }
  }

  private handleMouseUp(e: TEvent) {
    // Handle marquee selection end
    if (this.isMarqueeSelecting) {
      this.endMarqueeSelection(e);
    }
  }

  private getSelectedFabricObjects(): FabricObject[] {
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return [];

    if (activeObject instanceof ActiveSelection) {
      return activeObject.getObjects();
    } else {
      return [activeObject];
    }
  }

  private updateObjectFromFabric(fabricObject: FabricObject) {
    const objectId = (fabricObject as FabricObjectWithData).data?.id;
    if (!objectId) return;

    const transform: Partial<Transform> = {
      x: fabricObject.left || 0,
      y: fabricObject.top || 0,
      width: (fabricObject.width || 0) * (fabricObject.scaleX || 1),
      height: (fabricObject.height || 0) * (fabricObject.scaleY || 1),
      rotation: fabricObject.angle || 0,
      scaleX: fabricObject.scaleX || 1,
      scaleY: fabricObject.scaleY || 1,
    };

    this.store.updateObject(objectId, { transform });
  }

  private applyGridSnapping(target: FabricObject) {
    const gridSize = this.store.gridSize;
    const left = target.left || 0;
    const top = target.top || 0;

    const snappedLeft = Math.round(left / gridSize) * gridSize;
    const snappedTop = Math.round(top / gridSize) * gridSize;

    target.left = snappedLeft;
    target.top = snappedTop;
  }

  private maintainAspectRatio(target: FabricObject) {
    const originalWidth = (target as FabricObjectWithData).originalWidth || target.width;
    const originalHeight = (target as FabricObjectWithData).originalHeight || target.height;
    
    if (!originalWidth || !originalHeight) return;

    const aspectRatio = originalWidth / originalHeight;
    const scaleX = target.scaleX || 1;
    const scaleY = target.scaleY || 1;

    // Use the larger scale to maintain aspect ratio
    const scale = Math.max(scaleX, scaleY);
    target.scaleX = scale;
    target.scaleY = scale;
  }

  // Marquee selection
  private isMarqueeSelecting = false;
  private marqueeStartPoint: Point | null = null;
  private marqueeRect: FabricObject | null = null;

  private startMarqueeSelection(e: TEvent) {
    const pointer = this.canvas.getPointer(e.e);
    this.isMarqueeSelecting = true;
    this.marqueeStartPoint = pointer;

    // Create selection rectangle
    this.marqueeRect = new Rect({
      left: pointer.x,
      top: pointer.y,
      width: 0,
      height: 0,
      fill: 'rgba(0, 123, 255, 0.1)',
      stroke: '#007bff',
      strokeWidth: 1,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false,
    });
    this.canvas.add(this.marqueeRect);
    this.canvas.setActiveObject(this.marqueeRect);
  }

  private updateMarqueeSelection(e: TEvent) {
    if (!this.isMarqueeSelecting || !this.marqueeStartPoint || !this.marqueeRect) return;

    const pointer = this.canvas.getPointer(e.e);
    const width = pointer.x - this.marqueeStartPoint.x;
    const height = pointer.y - this.marqueeStartPoint.y;

    this.marqueeRect.set({
      width: Math.abs(width),
      height: Math.abs(height),
      left: width < 0 ? pointer.x : this.marqueeStartPoint.x,
      top: height < 0 ? pointer.y : this.marqueeStartPoint.y,
    });

    this.canvas.renderAll();
  }

  private endMarqueeSelection(e: TEvent) {
    if (!this.isMarqueeSelecting || !this.marqueeStartPoint || !this.marqueeRect) return;

    const pointer = this.canvas.getPointer(e.e);
    const selectionBounds = {
      left: Math.min(this.marqueeStartPoint.x, pointer.x),
      top: Math.min(this.marqueeStartPoint.y, pointer.y),
      right: Math.max(this.marqueeStartPoint.x, pointer.x),
      bottom: Math.max(this.marqueeStartPoint.y, pointer.y),
    };

    // Find objects within selection bounds
    const objectsInSelection: FabricObject[] = [];
    this.canvas.forEachObject((obj) => {
      if (obj === this.marqueeRect) return;

      const objBounds = obj.getBoundingRect();
      if (
        objBounds.left >= selectionBounds.left &&
        objBounds.top >= selectionBounds.top &&
        objBounds.left + objBounds.width <= selectionBounds.right &&
        objBounds.top + objBounds.height <= selectionBounds.bottom
      ) {
        objectsInSelection.push(obj);
      }
    });

    // Remove marquee rectangle
    this.canvas.remove(this.marqueeRect);
    this.marqueeRect = null;
    this.isMarqueeSelecting = false;
    this.marqueeStartPoint = null;

    // Select objects
    if (objectsInSelection.length > 0) {
      if (objectsInSelection.length === 1) {
        this.canvas.setActiveObject(objectsInSelection[0]);
      } else {
        const selection = new ActiveSelection(objectsInSelection, {
          canvas: this.canvas,
        });
        this.canvas.setActiveObject(selection);
      }
    }

    this.canvas.renderAll();
  }

  // Public methods for external control
  public selectObjects(objectIds: string[]) {
    const fabricObjects = this.canvas.getObjects().filter(obj => 
      objectIds.includes((obj as FabricObjectWithData).data?.id)
    );

    if (fabricObjects.length === 0) {
      this.canvas.discardActiveObject();
    } else if (fabricObjects.length === 1) {
      this.canvas.setActiveObject(fabricObjects[0]);
    } else {
      const selection = new ActiveSelection(fabricObjects, {
        canvas: this.canvas,
      });
      this.canvas.setActiveObject(selection);
    }

    this.canvas.renderAll();
  }

  public selectAll() {
    const allObjects = this.canvas.getObjects().filter(obj => 
      (obj as FabricObjectWithData).data?.id // Only select objects with IDs (our drawing objects)
    );

    if (allObjects.length > 0) {
      const selection = new ActiveSelection(allObjects, {
        canvas: this.canvas,
      });
      this.canvas.setActiveObject(selection);
      this.canvas.renderAll();
    }
  }

  public clearSelection() {
    this.canvas.discardActiveObject();
    this.canvas.renderAll();
  }

  public deleteSelected() {
    const activeObject = this.canvas.getActiveObject();
    if (!activeObject) return;

    if (activeObject instanceof ActiveSelection) {
      activeObject.forEachObject((obj) => {
        const objectId = (obj as FabricObjectWithData).data?.id;
        if (objectId) {
          this.store.deleteObject(objectId);
        }
      });
    } else {
      const objectId = (activeObject as FabricObjectWithData).data?.id;
      if (objectId) {
        this.store.deleteObject(objectId);
      }
    }

    // Clear selection after deletion
    this.clearSelection();
  }
}
