import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plug, 
  Database, 
  Cloud, 
  Shield, 
  Users,
  Building,
  Globe,
  Key,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Download,
  Upload,
  RefreshCw,
  Link,
  Server,
  Mail,
  Calendar,
  FileText,
  BarChart3
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export interface EnterpriseIntegration {
  id: string;
  name: string;
  category: 'sso' | 'database' | 'analytics' | 'communication' | 'storage' | 'security';
  status: 'connected' | 'disconnected' | 'error' | 'configuring';
  provider: string;
  version: string;
  lastSync: Date;
  config: Record<string, unknown>;
  metrics: {
    uptime: number;
    requests: number;
    errors: number;
    latency: number;
  };
}

export interface ScalabilityMetrics {
  currentUsers: number;
  maxUsers: number;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkThroughput: number;
  responseTime: number;
  errorRate: number;
}

export interface WhiteLabelConfig {
  companyName: string;
  logo: string;
  primaryColor: string;
  secondaryColor: string;
  favicon: string;
  customDomain: string;
  customCSS: string;
  hideAugmentBranding: boolean;
  customFooter: string;
  customTerms: string;
}

interface EnterpriseIntegrationHubProps {
  onIntegrationUpdate?: (integration: EnterpriseIntegration) => void;
  onWhiteLabelUpdate?: (config: WhiteLabelConfig) => void;
  className?: string;
}

export const EnterpriseIntegrationHub: React.FC<EnterpriseIntegrationHubProps> = ({
  onIntegrationUpdate,
  onWhiteLabelUpdate,
  className,
}) => {
  const [integrations, setIntegrations] = useState<EnterpriseIntegration[]>([
    {
      id: 'sso-okta',
      name: 'Okta SSO',
      category: 'sso',
      status: 'connected',
      provider: 'Okta',
      version: '2.1.0',
      lastSync: new Date(),
      config: { domain: 'company.okta.com', clientId: 'xxx' },
      metrics: { uptime: 99.9, requests: 15420, errors: 2, latency: 120 },
    },
    {
      id: 'db-postgres',
      name: 'PostgreSQL',
      category: 'database',
      status: 'connected',
      provider: 'PostgreSQL',
      version: '14.2',
      lastSync: new Date(),
      config: { host: 'db.company.com', database: 'analytics' },
      metrics: { uptime: 99.8, requests: 45230, errors: 12, latency: 45 },
    },
    {
      id: 'analytics-datadog',
      name: 'Datadog',
      category: 'analytics',
      status: 'connected',
      provider: 'Datadog',
      version: '3.0.1',
      lastSync: new Date(),
      config: { apiKey: 'xxx', site: 'datadoghq.com' },
      metrics: { uptime: 99.95, requests: 8920, errors: 1, latency: 200 },
    },
    {
      id: 'comm-slack',
      name: 'Slack',
      category: 'communication',
      status: 'disconnected',
      provider: 'Slack',
      version: '1.8.0',
      lastSync: new Date(Date.now() - 24 * 60 * 60 * 1000),
      config: { webhookUrl: '', channel: '#alerts' },
      metrics: { uptime: 0, requests: 0, errors: 0, latency: 0 },
    },
  ]);

  const [scalabilityMetrics, setScalabilityMetrics] = useState<ScalabilityMetrics>({
    currentUsers: 1250,
    maxUsers: 5000,
    cpuUsage: 45,
    memoryUsage: 62,
    diskUsage: 38,
    networkThroughput: 125.5,
    responseTime: 180,
    errorRate: 0.02,
  });

  const [whiteLabelConfig, setWhiteLabelConfig] = useState<WhiteLabelConfig>({
    companyName: 'Your Company',
    logo: '/company-logo.png',
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    favicon: '/favicon.ico',
    customDomain: 'analytics.yourcompany.com',
    customCSS: '',
    hideAugmentBranding: false,
    customFooter: '© 2024 Your Company. All rights reserved.',
    customTerms: 'https://yourcompany.com/terms',
  });

  // Available integrations catalog
  const availableIntegrations = [
    { id: 'azure-ad', name: 'Azure Active Directory', category: 'sso', provider: 'Microsoft' },
    { id: 'google-workspace', name: 'Google Workspace', category: 'sso', provider: 'Google' },
    { id: 'salesforce', name: 'Salesforce', category: 'database', provider: 'Salesforce' },
    { id: 'snowflake', name: 'Snowflake', category: 'database', provider: 'Snowflake' },
    { id: 'tableau', name: 'Tableau', category: 'analytics', provider: 'Tableau' },
    { id: 'power-bi', name: 'Power BI', category: 'analytics', provider: 'Microsoft' },
    { id: 'teams', name: 'Microsoft Teams', category: 'communication', provider: 'Microsoft' },
    { id: 'aws-s3', name: 'AWS S3', category: 'storage', provider: 'Amazon' },
    { id: 'azure-storage', name: 'Azure Storage', category: 'storage', provider: 'Microsoft' },
  ];

  // Update integration status
  const updateIntegration = useCallback((integrationId: string, updates: Partial<EnterpriseIntegration>) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, ...updates, lastSync: new Date() }
        : integration
    ));
  }, []);

  // Test integration connection
  const testConnection = useCallback(async (integrationId: string) => {
    updateIntegration(integrationId, { status: 'configuring' });
    
    // Simulate connection test
    setTimeout(() => {
      const success = Math.random() > 0.2;
      updateIntegration(integrationId, { 
        status: success ? 'connected' : 'error',
        lastSync: new Date(),
      });
    }, 2000);
  }, [updateIntegration]);

  // Update white label configuration
  const updateWhiteLabel = useCallback((updates: Partial<WhiteLabelConfig>) => {
    setWhiteLabelConfig(prev => {
      const newConfig = { ...prev, ...updates };
      onWhiteLabelUpdate?.(newConfig);
      return newConfig;
    });
  }, [onWhiteLabelUpdate]);

  // Simulate real-time metrics updates
  useEffect(() => {
    const interval = setInterval(() => {
      setScalabilityMetrics(prev => ({
        ...prev,
        currentUsers: prev.currentUsers + Math.floor(Math.random() * 10 - 5),
        cpuUsage: Math.max(0, Math.min(100, prev.cpuUsage + Math.random() * 10 - 5)),
        memoryUsage: Math.max(0, Math.min(100, prev.memoryUsage + Math.random() * 8 - 4)),
        responseTime: Math.max(50, prev.responseTime + Math.random() * 40 - 20),
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Enterprise Integration Hub
              <Badge variant="secondary">
                {integrations.filter(i => i.status === 'connected').length} Connected
              </Badge>
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export Config
              </Button>
              
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync All
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <Tabs defaultValue="integrations" className="w-full h-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
            <TabsTrigger value="scalability">Scalability</TabsTrigger>
            <TabsTrigger value="whitelabel">White Label</TabsTrigger>
            <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          </TabsList>

          {/* Integrations */}
          <TabsContent value="integrations" className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {integrations.map(integration => (
                <Card key={integration.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {integration.category === 'sso' && <Shield className="h-4 w-4" />}
                        {integration.category === 'database' && <Database className="h-4 w-4" />}
                        {integration.category === 'analytics' && <BarChart3 className="h-4 w-4" />}
                        {integration.category === 'communication' && <Mail className="h-4 w-4" />}
                        {integration.category === 'storage' && <Server className="h-4 w-4" />}
                        <span className="font-medium">{integration.name}</span>
                      </div>
                      
                      {integration.status === 'connected' && <CheckCircle className="h-4 w-4 text-green-600" />}
                      {integration.status === 'disconnected' && <XCircle className="h-4 w-4 text-gray-400" />}
                      {integration.status === 'error' && <AlertTriangle className="h-4 w-4 text-red-600" />}
                      {integration.status === 'configuring' && <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />}
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Provider:</span>
                        <span>{integration.provider}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Version:</span>
                        <span>{integration.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Uptime:</span>
                        <span>{integration.metrics.uptime}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Last Sync:</span>
                        <span>{integration.lastSync.toLocaleTimeString()}</span>
                      </div>
                    </div>

                    <div className="flex gap-2 mt-4">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => testConnection(integration.id)}
                        disabled={integration.status === 'configuring'}
                      >
                        Test
                      </Button>
                      <Button size="sm" variant="outline">
                        Configure
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Available Integrations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {availableIntegrations.map(integration => (
                    <div key={integration.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-2">
                        <Plug className="h-4 w-4" />
                        <span className="font-medium">{integration.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {integration.category}
                        </Badge>
                      </div>
                      <Button size="sm" variant="outline">
                        Add
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Scalability */}
          <TabsContent value="scalability" className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Active Users</div>
                      <div className="font-medium">{scalabilityMetrics.currentUsers.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">
                        of {scalabilityMetrics.maxUsers.toLocaleString()} max
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Server className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">CPU Usage</div>
                      <div className="font-medium">{scalabilityMetrics.cpuUsage}%</div>
                      <div className="text-xs text-muted-foreground">
                        {scalabilityMetrics.cpuUsage < 70 ? 'Healthy' : 'High'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Database className="h-5 w-5 text-purple-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Memory Usage</div>
                      <div className="font-medium">{scalabilityMetrics.memoryUsage}%</div>
                      <div className="text-xs text-muted-foreground">
                        {scalabilityMetrics.memoryUsage < 80 ? 'Normal' : 'High'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-600" />
                    <div>
                      <div className="text-sm text-muted-foreground">Response Time</div>
                      <div className="font-medium">{scalabilityMetrics.responseTime}ms</div>
                      <div className="text-xs text-muted-foreground">
                        {scalabilityMetrics.responseTime < 200 ? 'Fast' : 'Slow'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Auto-Scaling Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Auto-scaling Enabled</div>
                      <div className="text-sm text-muted-foreground">Automatically scale based on load</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div>
                    <label className="text-sm font-medium">CPU Threshold</label>
                    <div className="flex items-center gap-2 mt-1">
                      <Input type="number" defaultValue="70" className="w-20" />
                      <span className="text-sm">%</span>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Max Instances</label>
                    <Input type="number" defaultValue="10" className="w-20 mt-1" />
                  </div>

                  <Button className="w-full">
                    Update Scaling Policy
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Optimization</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">CDN Enabled</div>
                      <div className="text-sm text-muted-foreground">Content delivery network</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Caching</div>
                      <div className="text-sm text-muted-foreground">Redis cache layer</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Load Balancing</div>
                      <div className="text-sm text-muted-foreground">Distribute traffic across instances</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <Button variant="outline" className="w-full">
                    Run Performance Test
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* White Label */}
          <TabsContent value="whitelabel" className="p-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Brand Customization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Company Name</label>
                    <Input
                      value={whiteLabelConfig.companyName}
                      onChange={(e) => updateWhiteLabel({ companyName: e.target.value })}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Custom Domain</label>
                    <Input
                      value={whiteLabelConfig.customDomain}
                      onChange={(e) => updateWhiteLabel({ customDomain: e.target.value })}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Primary Color</label>
                    <div className="flex items-center gap-2 mt-1">
                      <input
                        type="color"
                        value={whiteLabelConfig.primaryColor}
                        onChange={(e) => updateWhiteLabel({ primaryColor: e.target.value })}
                        className="w-12 h-10 border rounded"
                      />
                      <Input
                        value={whiteLabelConfig.primaryColor}
                        onChange={(e) => updateWhiteLabel({ primaryColor: e.target.value })}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Secondary Color</label>
                    <div className="flex items-center gap-2 mt-1">
                      <input
                        type="color"
                        value={whiteLabelConfig.secondaryColor}
                        onChange={(e) => updateWhiteLabel({ secondaryColor: e.target.value })}
                        className="w-12 h-10 border rounded"
                      />
                      <Input
                        value={whiteLabelConfig.secondaryColor}
                        onChange={(e) => updateWhiteLabel({ secondaryColor: e.target.value })}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="hideAugmentBranding"
                    checked={whiteLabelConfig.hideAugmentBranding}
                    onChange={(e) => updateWhiteLabel({ hideAugmentBranding: e.target.checked })}
                  />
                  <label htmlFor="hideAugmentBranding" className="text-sm">
                    Hide Augment branding
                  </label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom Assets</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Logo Upload</label>
                  <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                    <div className="text-sm text-gray-600">
                      Drop your logo here or click to upload
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      PNG, JPG up to 2MB (recommended: 200x50px)
                    </div>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium">Favicon</label>
                  <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <div className="text-sm text-gray-600">Upload favicon.ico</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="border rounded-lg p-4"
                  style={{ 
                    backgroundColor: whiteLabelConfig.primaryColor + '10',
                    borderColor: whiteLabelConfig.primaryColor 
                  }}
                >
                  <div className="flex items-center gap-2 mb-4">
                    <div 
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: whiteLabelConfig.primaryColor }}
                    />
                    <span className="font-medium">{whiteLabelConfig.companyName}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    This is how your branded application will appear to users.
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Monitoring */}
          <TabsContent value="monitoring" className="p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Overall System Status</span>
                      <Badge variant="default">Healthy</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Database Connection</span>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">External APIs</span>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Background Jobs</span>
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Alert Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Email Alerts</div>
                      <div className="text-sm text-muted-foreground">Send alerts via email</div>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Slack Notifications</div>
                      <div className="text-sm text-muted-foreground">Send alerts to Slack</div>
                    </div>
                    <input type="checkbox" className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">SMS Alerts</div>
                      <div className="text-sm text-muted-foreground">Critical alerts via SMS</div>
                    </div>
                    <input type="checkbox" className="toggle" />
                  </div>

                  <Button variant="outline" className="w-full">
                    Configure Alert Rules
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
