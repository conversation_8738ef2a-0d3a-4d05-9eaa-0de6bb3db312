
import { create } from 'zustand';

type MainTab = 'analyze' | 'insights' | 'canvas';
type HistoricalAnalysisTab = 'analytics' | 'visual' | 'list';
type CanvasMode = 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated';

interface NavigationState {
  mainTab: MainTab;
  setMainTab: (tab: MainTab) => void;
  historicalAnalysisTab: HistoricalAnalysisTab;
  setHistoricalAnalysisTab: (tab: HistoricalAnalysisTab) => void;
  // Canvas-specific navigation state
  canvasMode: CanvasMode;
  setCanvasMode: (mode: CanvasMode) => void;
}

export const useNavigationStore = create<NavigationState>((set, get) => ({
  mainTab: 'analyze',
  setMainTab: (tab) => {
    console.log('🧭 Navigation Store - setMainTab:', { from: get().mainTab, to: tab });
    set({ mainTab: tab });
  },
  historicalAnalysisTab: 'analytics',
  setHistoricalAnalysisTab: (tab) => {
    console.log('🧭 Navigation Store - setHistoricalAnalysisTab:', { from: get().historicalAnalysisTab, to: tab });
    set({ historicalAnalysisTab: tab });
  },
  canvasMode: 'figma-integrated',
  setCanvasMode: (mode) => {
    console.log('🧭 Navigation Store - setCanvasMode:', { from: get().canvasMode, to: mode });
    set({ canvasMode: mode });
  },
}));
