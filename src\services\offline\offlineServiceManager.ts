/**
 * Offline Service Manager
 * 
 * Coordinates all offline functionality, manages service workers,
 * handles network state changes, and provides a unified interface
 * for offline-first operations.
 */

import React from 'react';
import { localAIService } from './localAIService';
import { offlineStorageService } from './offlineStorageService';
import { AnalysisResult, ConversationStyle, CharacterPersona } from '@/types/conversation';
import { QuestionContext } from '@/components/conversation-planner/QuestionContextSelector';

interface OfflineCapabilities {
  aiAnalysis: boolean;
  dataStorage: boolean;
  caching: boolean;
  serviceWorker: boolean;
  networkDetection: boolean;
}

interface NetworkState {
  isOnline: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
}

export class OfflineServiceManager {
  private capabilities: OfflineCapabilities;
  private networkState: NetworkState;
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private networkListeners: Set<(state: NetworkState) => void> = new Set();
  private isInitialized = false;

  constructor() {
    this.capabilities = {
      aiAnalysis: false,
      dataStorage: false,
      caching: false,
      serviceWorker: false,
      networkDetection: false,
    };

    this.networkState = {
      isOnline: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
    };

    this.initialize();
  }

  private async initialize() {
    if (this.isInitialized) return;

    try {
      // Initialize network detection
      this.initializeNetworkDetection();

      // Initialize service worker
      await this.initializeServiceWorker();

      // Initialize storage service
      await offlineStorageService.initialize();
      this.capabilities.dataStorage = true;

      // Initialize AI service
      if (localAIService.isAvailable()) {
        this.capabilities.aiAnalysis = true;
      }

      this.capabilities.caching = true;
      this.isInitialized = true;

      console.log('Offline Service Manager initialized with capabilities:', this.capabilities);
    } catch (error) {
      console.error('Failed to initialize Offline Service Manager:', error);
      this.isInitialized = true; // Continue with limited functionality
    }
  }

  private initializeNetworkDetection() {
    try {
      // Basic online/offline detection
      window.addEventListener('online', this.handleNetworkChange.bind(this));
      window.addEventListener('offline', this.handleNetworkChange.bind(this));

      // Enhanced network information if available
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        if (connection) {
          this.updateNetworkState(connection);
          connection.addEventListener('change', () => this.updateNetworkState(connection));
        }
      }

      this.capabilities.networkDetection = true;
    } catch (error) {
      console.warn('Network detection initialization failed:', error);
    }
  }

  private async initializeServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js');
        this.capabilities.serviceWorker = true;
        console.log('Service Worker registered successfully');
      } catch (error) {
        console.warn('Service Worker registration failed:', error);
      }
    }
  }

  private handleNetworkChange() {
    const wasOnline = this.networkState.isOnline;
    this.networkState.isOnline = navigator.onLine;

    if (wasOnline !== this.networkState.isOnline) {
      console.log(`Network state changed: ${this.networkState.isOnline ? 'online' : 'offline'}`);
      this.notifyNetworkListeners();
    }
  }

  private updateNetworkState(connection: {
    type?: string;
    effectiveType?: string;
    downlink?: number;
    rtt?: number;
  }) {
    this.networkState = {
      ...this.networkState,
      connectionType: connection.type || 'unknown',
      effectiveType: connection.effectiveType || 'unknown',
      downlink: connection.downlink || 0,
      rtt: connection.rtt || 0,
    };
    this.notifyNetworkListeners();
  }

  private notifyNetworkListeners() {
    this.networkListeners.forEach(listener => {
      try {
        listener(this.networkState);
      } catch (error) {
        console.error('Network listener error:', error);
      }
    });
  }

  // Public API methods
  async analyzeQuestion(
    question: string,
    style: ConversationStyle,
    model: string,
    numberOfAnswers: number,
    analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles' = 'multiple',
    characterPersona?: CharacterPersona,
    seedContext?: string,
    questionContext?: QuestionContext,
    selectedEmotions?: string[]
  ): Promise<AnalysisResult> {
    await this.ensureInitialized();

    if (!this.capabilities.aiAnalysis) {
      throw new Error('Offline AI analysis is not available');
    }

    const result = await localAIService.analyzeQuestion(
      question,
      style,
      model,
      numberOfAnswers,
      analysisType,
      characterPersona,
      seedContext,
      questionContext,
      selectedEmotions
    );

    // Store the result for offline access
    if (this.capabilities.dataStorage) {
      await offlineStorageService.storeAnalysisResult(result);
    }

    return result;
  }

  async getStoredAnalysisResults(limit?: number): Promise<AnalysisResult[]> {
    await this.ensureInitialized();

    if (!this.capabilities.dataStorage) {
      return [];
    }

    return await offlineStorageService.getAnalysisResults(limit);
  }

  async storeData<T>(category: string, id: string, data: T): Promise<void> {
    await this.ensureInitialized();

    if (!this.capabilities.dataStorage) {
      throw new Error('Offline storage is not available');
    }

    await offlineStorageService.store(category, id, data);
  }

  async retrieveData<T>(category: string, id: string): Promise<T | null> {
    await this.ensureInitialized();

    if (!this.capabilities.dataStorage) {
      return null;
    }

    return await offlineStorageService.retrieve<T>(category, id);
  }

  async clearCache(): Promise<void> {
    await this.ensureInitialized();

    if (this.capabilities.aiAnalysis) {
      localAIService.clearCache();
    }

    if (this.capabilities.dataStorage) {
      await offlineStorageService.clear('cache');
    }

    if (this.serviceWorkerRegistration) {
      // Clear service worker cache
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }
  }

  async getOfflineStats(): Promise<{
    capabilities: OfflineCapabilities;
    networkState: NetworkState;
    storage: unknown;
    aiCache: unknown;
  }> {
    await this.ensureInitialized();

    const stats = {
      capabilities: this.capabilities,
      networkState: this.networkState,
      storage: null as unknown,
      aiCache: null as unknown,
    };

    if (this.capabilities.dataStorage) {
      stats.storage = await offlineStorageService.getStorageStats();
    }

    if (this.capabilities.aiAnalysis) {
      stats.aiCache = localAIService.getCacheStats();
    }

    return stats;
  }

  async exportOfflineData(): Promise<string> {
    await this.ensureInitialized();

    if (!this.capabilities.dataStorage) {
      throw new Error('Offline storage is not available');
    }

    return await offlineStorageService.exportData();
  }

  async importOfflineData(jsonData: string): Promise<void> {
    await this.ensureInitialized();

    if (!this.capabilities.dataStorage) {
      throw new Error('Offline storage is not available');
    }

    await offlineStorageService.importData(jsonData);
  }

  // Network state management
  onNetworkStateChange(listener: (state: NetworkState) => void): () => void {
    this.networkListeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.networkListeners.delete(listener);
    };
  }

  isOnline(): boolean {
    return this.networkState.isOnline;
  }

  getNetworkState(): NetworkState {
    return { ...this.networkState };
  }

  getCapabilities(): OfflineCapabilities {
    return { ...this.capabilities };
  }

  // Service management
  async updateServiceWorker(): Promise<void> {
    if (this.serviceWorkerRegistration) {
      await this.serviceWorkerRegistration.update();
    }
  }

  async unregisterServiceWorker(): Promise<void> {
    if (this.serviceWorkerRegistration) {
      await this.serviceWorkerRegistration.unregister();
      this.serviceWorkerRegistration = null;
      this.capabilities.serviceWorker = false;
    }
  }

  // Utility methods
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  async performMaintenance(): Promise<void> {
    await this.ensureInitialized();

    console.log('Performing offline service maintenance...');

    // Clean up old data
    if (this.capabilities.dataStorage) {
      await offlineStorageService.cleanup();
    }

    // Update service worker
    if (this.capabilities.serviceWorker) {
      await this.updateServiceWorker();
    }

    console.log('Offline service maintenance completed');
  }

  // Fallback detection
  shouldUseOfflineMode(): boolean {
    return !this.networkState.isOnline || 
           this.networkState.effectiveType === 'slow-2g' ||
           (this.networkState.rtt > 2000); // High latency
  }

  getRecommendedMode(): 'online' | 'offline' | 'hybrid' {
    if (!this.networkState.isOnline) {
      return 'offline';
    }

    if (this.networkState.effectiveType === 'slow-2g' || this.networkState.rtt > 1000) {
      return 'hybrid';
    }

    return 'online';
  }
}

// Singleton instance
export const offlineServiceManager = new OfflineServiceManager();

// Global offline status hook for React components
export const useOfflineStatus = () => {
  const [networkState, setNetworkState] = React.useState(offlineServiceManager.getNetworkState());
  const [capabilities, setCapabilities] = React.useState(offlineServiceManager.getCapabilities());

  React.useEffect(() => {
    const unsubscribe = offlineServiceManager.onNetworkStateChange(setNetworkState);
    
    // Update capabilities periodically
    const interval = setInterval(async () => {
      const stats = await offlineServiceManager.getOfflineStats();
      setCapabilities(stats.capabilities);
    }, 30000); // Every 30 seconds

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, []);

  return {
    networkState,
    capabilities,
    isOnline: networkState.isOnline,
    shouldUseOffline: offlineServiceManager.shouldUseOfflineMode(),
    recommendedMode: offlineServiceManager.getRecommendedMode(),
  };
};

// Import React for the hook was moved to the top
