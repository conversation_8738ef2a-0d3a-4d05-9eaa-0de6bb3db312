export interface ParsedMultipleAnswers {
  type: 'multiple';
  answers: Array<{
    id: number;
    content: string;
  }>;
  isFallback?: boolean;
  warning?: string;
}

export interface ParsedDeepAnalysis {
  type: 'deep';
  summary?: string;
  key_concepts?: string;
  main_argument?: string;
  counterarguments?: string;
  assumptions?: string;
  possibleResponses?: string[];
  followUpQuestions?: string[];
  isFallback?: boolean;
  warning?: string;
}

export interface ParsedCharacterAnalysis {
  type: 'character';
  response: string;
  possibleResponses?: string[]; // Renamed from possibleRetorts
  followUpQuestions?: string[]; // Renamed from furtherInquiries
}

export interface ParsedFallback {
  type: 'fallback';
  isFallback: true;
  content: string;
  warning: string;
}

export interface ParsedProsConsAnalysis {
  type: 'pros-cons';
  pros: string[];
  cons: string[];
  summary?: string;
  isFallback?: boolean;
  warning?: string;
}

export interface ParsedSixThinkingHatsAnalysis {
  type: 'six-hats';
  white_hat?: string; // Facts & Information
  red_hat?: string;   // Emotions & Feelings
  black_hat?: string; // Cautions & Risks (Critical Judgement)
  yellow_hat?: string;// Benefits & Positives (Optimism)
  green_hat?: string; // Creativity & New Ideas
  blue_hat?: string;  // Process & Overview (Managing Thinking)
  summary?: string;
  isFallback?: boolean;
  warning?: string;
}

export interface ParsedEmotionalAnglesAnalysis {
  type: 'emotional-angles';
  angles: Array<{
    emotion: string;
    analysis: string;
    keywords?: string[];
  }>;
  summary?: string;
  isFallback?: boolean;
  warning?: string;
}

export type ParsedOutput = ParsedMultipleAnswers | ParsedDeepAnalysis | ParsedCharacterAnalysis | ParsedFallback | ParsedProsConsAnalysis | ParsedSixThinkingHatsAnalysis | ParsedEmotionalAnglesAnalysis;
