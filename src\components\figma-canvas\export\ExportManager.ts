import { Canvas } from 'fabric';
import jsPDF from 'jspdf';
import { ExportOptions, CanvasState } from '@/types/figma';

export class ExportManager {
  private canvas: Canvas;

  constructor(canvas: Canvas) {
    this.canvas = canvas;
  }

  async exportToPNG(options: ExportOptions = { format: 'png' }): Promise<Blob> {
    const {
      scale = 1,
      quality = 1,
      backgroundColor = 'white',
      includeBackground = true,
      selectedOnly = false,
    } = options;

    // Store original canvas state
    const originalZoom = this.canvas.getZoom();
    const originalBackground = this.canvas.backgroundColor;

    try {
      // Set export settings
      this.canvas.setZoom(scale);
      if (includeBackground) {
        this.canvas.backgroundColor = backgroundColor;
      }

      let dataURL: string;

      if (selectedOnly) {
        // Export only selected objects
        const activeObject = this.canvas.getActiveObject();
        if (activeObject) {
          const bounds = activeObject.getBoundingRect();
          dataURL = this.canvas.toDataURL({
            format: 'png',
            quality,
            left: bounds.left,
            top: bounds.top,
            width: bounds.width,
            height: bounds.height,
            multiplier: scale,
          });
        } else {
          throw new Error('No objects selected for export');
        }
      } else {
        // Export entire canvas
        dataURL = this.canvas.toDataURL({
          format: 'png',
          quality,
          multiplier: scale,
        });
      }

      // Convert data URL to blob
      const response = await fetch(dataURL);
      const blob = await response.blob();

      return blob;
    } finally {
      // Restore original canvas state
      this.canvas.setZoom(originalZoom);
      this.canvas.backgroundColor = originalBackground;
      this.canvas.renderAll();
    }
  }

  async exportToJPG(options: ExportOptions = { format: 'jpg' }): Promise<Blob> {
    const {
      scale = 1,
      quality = 0.8,
      backgroundColor = 'white',
      selectedOnly = false,
    } = options;

    // Store original canvas state
    const originalZoom = this.canvas.getZoom();
    const originalBackground = this.canvas.backgroundColor;

    try {
      // Set export settings
      this.canvas.setZoom(scale);
      this.canvas.backgroundColor = backgroundColor; // JPG doesn't support transparency

      let dataURL: string;

      if (selectedOnly) {
        const activeObject = this.canvas.getActiveObject();
        if (activeObject) {
          const bounds = activeObject.getBoundingRect();
          dataURL = this.canvas.toDataURL({
            format: 'jpeg',
            quality,
            left: bounds.left,
            top: bounds.top,
            width: bounds.width,
            height: bounds.height,
            multiplier: scale,
          });
        } else {
          throw new Error('No objects selected for export');
        }
      } else {
        dataURL = this.canvas.toDataURL({
          format: 'jpeg',
          quality,
          multiplier: scale,
        });
      }

      const response = await fetch(dataURL);
      const blob = await response.blob();

      return blob;
    } finally {
      // Restore original canvas state
      this.canvas.setZoom(originalZoom);
      this.canvas.backgroundColor = originalBackground;
      this.canvas.renderAll();
    }
  }

  async exportToSVG(options: ExportOptions = { format: 'svg' }): Promise<Blob> {
    const {
      selectedOnly = false,
    } = options;

    let svgString: string;

    if (selectedOnly) {
      const activeObject = this.canvas.getActiveObject();
      if (activeObject) {
        svgString = activeObject.toSVG();
      } else {
        throw new Error('No objects selected for export');
      }
    } else {
      svgString = this.canvas.toSVG();
    }

    const blob = new Blob([svgString], { type: 'image/svg+xml' });
    return blob;
  }

  async exportToPDF(options: ExportOptions = { format: 'pdf' }): Promise<Blob> {
    const {
      scale = 1,
      backgroundColor = 'white',
      selectedOnly = false,
    } = options;

    // Get canvas dimensions
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();

    // Create PDF
    const pdf = new jsPDF({
      orientation: canvasWidth > canvasHeight ? 'landscape' : 'portrait',
      unit: 'px',
      format: [canvasWidth, canvasHeight],
    });

    // Export canvas as image and add to PDF
    const imageBlob = await this.exportToPNG({
      format: 'png',
      scale,
      backgroundColor,
      selectedOnly,
    });

    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        const imageData = reader.result as string;
        pdf.addImage(imageData, 'PNG', 0, 0, canvasWidth, canvasHeight);
        
        const pdfBlob = pdf.output('blob');
        resolve(pdfBlob);
      };
      reader.readAsDataURL(imageBlob);
    });
  }

  async exportCanvasData(canvasState: CanvasState): Promise<Blob> {
    // Export canvas data as JSON
    const exportData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      canvasState,
      metadata: {
        exportedBy: 'Chat Craft Trainer Pro',
        format: 'figma-canvas-data',
      },
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    return blob;
  }

  downloadBlob(blob: Blob, filename: string) {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  async exportAndDownload(
    format: 'png' | 'jpg' | 'svg' | 'pdf',
    options: ExportOptions = { format },
    filename?: string
  ) {
    let blob: Blob;
    let defaultFilename: string;

    switch (format) {
      case 'png':
        blob = await this.exportToPNG(options);
        defaultFilename = `canvas-export-${Date.now()}.png`;
        break;
      case 'jpg':
        blob = await this.exportToJPG(options);
        defaultFilename = `canvas-export-${Date.now()}.jpg`;
        break;
      case 'svg':
        blob = await this.exportToSVG(options);
        defaultFilename = `canvas-export-${Date.now()}.svg`;
        break;
      case 'pdf':
        blob = await this.exportToPDF(options);
        defaultFilename = `canvas-export-${Date.now()}.pdf`;
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }

    this.downloadBlob(blob, filename || defaultFilename);
  }

  // Utility method to get optimal export settings based on canvas content
  getOptimalExportSettings(): ExportOptions {
    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();
    const objectCount = this.canvas.getObjects().length;

    // Determine optimal scale based on canvas size and complexity
    let scale = 1;
    if (canvasWidth * canvasHeight > 1000000) {
      scale = 0.5; // Large canvas, reduce scale
    } else if (objectCount > 100) {
      scale = 0.75; // Many objects, slightly reduce scale
    } else if (canvasWidth * canvasHeight < 100000) {
      scale = 2; // Small canvas, increase scale for better quality
    }

    return {
      format: 'png',
      scale,
      quality: 0.9,
      backgroundColor: 'white',
      includeBackground: true,
      selectedOnly: false,
    };
  }
}
