import React, { useState, useRef, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { GradientStyle, GradientStop } from '@/types/figma';
import { Plus, Trash2 } from 'lucide-react';

interface GradientEditorProps {
  value?: GradientStyle;
  onChange: (gradient: GradientStyle) => void;
  className?: string;
}

export const GradientEditor: React.FC<GradientEditorProps> = ({
  value,
  onChange,
  className,
}) => {
  const [gradient, setGradient] = useState<GradientStyle>(
    value || {
      type: 'linear',
      stops: [
        { offset: 0, color: '#000000' },
        { offset: 1, color: '#ffffff' },
      ],
      angle: 0,
    }
  );

  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (value) {
      setGradient(value);
    }
  }, [value]);

  useEffect(() => {
    drawGradientPreview();
  }, [gradient]);

  const drawGradientPreview = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    ctx.clearRect(0, 0, width, height);

    let gradientObj;

    if (gradient.type === 'linear') {
      const angle = (gradient.angle || 0) * (Math.PI / 180);
      const x1 = width / 2 - Math.cos(angle) * width / 2;
      const y1 = height / 2 - Math.sin(angle) * height / 2;
      const x2 = width / 2 + Math.cos(angle) * width / 2;
      const y2 = height / 2 + Math.sin(angle) * height / 2;
      
      gradientObj = ctx.createLinearGradient(x1, y1, x2, y2);
    } else {
      const centerX = gradient.centerX !== undefined ? gradient.centerX * width : width / 2;
      const centerY = gradient.centerY !== undefined ? gradient.centerY * height : height / 2;
      const radius = gradient.radius !== undefined ? gradient.radius * Math.min(width, height) / 2 : Math.min(width, height) / 2;
      
      gradientObj = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
    }

    gradient.stops.forEach(stop => {
      gradientObj.addColorStop(stop.offset, stop.color);
    });

    ctx.fillStyle = gradientObj;
    ctx.fillRect(0, 0, width, height);
  };

  const updateGradient = (updates: Partial<GradientStyle>) => {
    const newGradient = { ...gradient, ...updates };
    setGradient(newGradient);
    onChange(newGradient);
  };

  const updateStop = (index: number, updates: Partial<GradientStop>) => {
    const newStops = [...gradient.stops];
    newStops[index] = { ...newStops[index], ...updates };
    updateGradient({ stops: newStops });
  };

  const addStop = () => {
    const newOffset = gradient.stops.length > 0 
      ? Math.min(1, Math.max(...gradient.stops.map(s => s.offset)) + 0.1)
      : 0.5;
    
    const newStops = [...gradient.stops, { offset: newOffset, color: '#808080' }];
    newStops.sort((a, b) => a.offset - b.offset);
    updateGradient({ stops: newStops });
  };

  const removeStop = (index: number) => {
    if (gradient.stops.length <= 2) return; // Keep at least 2 stops
    
    const newStops = gradient.stops.filter((_, i) => i !== index);
    updateGradient({ stops: newStops });
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Gradient Preview */}
      <div>
        <Label className="text-xs">Preview</Label>
        <canvas
          ref={canvasRef}
          width={200}
          height={60}
          className="w-full h-15 border border-gray-200 rounded mt-1"
        />
      </div>

      {/* Gradient Type */}
      <div>
        <Label className="text-xs">Type</Label>
        <Select
          value={gradient.type}
          onValueChange={(value: 'linear' | 'radial') => updateGradient({ type: value })}
        >
          <SelectTrigger className="h-8 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="linear">Linear</SelectItem>
            <SelectItem value="radial">Radial</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Gradient Properties */}
      {gradient.type === 'linear' && (
        <div>
          <Label className="text-xs">Angle: {gradient.angle || 0}°</Label>
          <Slider
            value={[gradient.angle || 0]}
            onValueChange={([angle]) => updateGradient({ angle })}
            min={0}
            max={360}
            step={1}
            className="w-full mt-1"
          />
        </div>
      )}

      {gradient.type === 'radial' && (
        <div className="space-y-2">
          <div>
            <Label className="text-xs">Center X: {Math.round((gradient.centerX || 0.5) * 100)}%</Label>
            <Slider
              value={[gradient.centerX || 0.5]}
              onValueChange={([centerX]) => updateGradient({ centerX })}
              min={0}
              max={1}
              step={0.01}
              className="w-full mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Center Y: {Math.round((gradient.centerY || 0.5) * 100)}%</Label>
            <Slider
              value={[gradient.centerY || 0.5]}
              onValueChange={([centerY]) => updateGradient({ centerY })}
              min={0}
              max={1}
              step={0.01}
              className="w-full mt-1"
            />
          </div>
          <div>
            <Label className="text-xs">Radius: {Math.round((gradient.radius || 0.5) * 100)}%</Label>
            <Slider
              value={[gradient.radius || 0.5]}
              onValueChange={([radius]) => updateGradient({ radius })}
              min={0}
              max={1}
              step={0.01}
              className="w-full mt-1"
            />
          </div>
        </div>
      )}

      {/* Gradient Stops */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label className="text-xs">Color Stops</Label>
          <Button
            variant="ghost"
            size="sm"
            onClick={addStop}
            className="h-6 w-6 p-0"
            title="Add Stop"
          >
            <Plus className="w-3 h-3" />
          </Button>
        </div>

        <div className="space-y-2">
          {gradient.stops.map((stop, index) => (
            <div key={index} className="flex items-center gap-2 p-2 border border-gray-200 rounded">
              <ColorPicker
                value={stop.color}
                onChange={(color) => updateStop(index, { color })}
                className="w-8 h-6"
              />
              
              <div className="flex-1">
                <Label className="text-xs">Position: {Math.round(stop.offset * 100)}%</Label>
                <Slider
                  value={[stop.offset]}
                  onValueChange={([offset]) => updateStop(index, { offset })}
                  min={0}
                  max={1}
                  step={0.01}
                  className="w-full"
                />
              </div>

              <Input
                type="text"
                value={stop.color}
                onChange={(e) => updateStop(index, { color: e.target.value })}
                className="w-20 h-6 text-xs font-mono"
              />

              {gradient.stops.length > 2 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeStop(index)}
                  className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                  title="Remove Stop"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Preset Gradients */}
      <div>
        <Label className="text-xs">Presets</Label>
        <div className="grid grid-cols-4 gap-2 mt-1">
          {GRADIENT_PRESETS.map((preset, index) => (
            <button
              key={index}
              className="h-8 border border-gray-200 rounded cursor-pointer hover:border-blue-500 transition-colors"
              style={{
                background: createCSSGradient(preset),
              }}
              onClick={() => {
                setGradient(preset);
                onChange(preset);
              }}
              title={`Preset ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// Gradient presets
const GRADIENT_PRESETS: GradientStyle[] = [
  {
    type: 'linear',
    angle: 0,
    stops: [
      { offset: 0, color: '#ff0000' },
      { offset: 1, color: '#0000ff' },
    ],
  },
  {
    type: 'linear',
    angle: 45,
    stops: [
      { offset: 0, color: '#ff6b6b' },
      { offset: 1, color: '#4ecdc4' },
    ],
  },
  {
    type: 'linear',
    angle: 90,
    stops: [
      { offset: 0, color: '#667eea' },
      { offset: 1, color: '#764ba2' },
    ],
  },
  {
    type: 'radial',
    centerX: 0.5,
    centerY: 0.5,
    radius: 0.5,
    stops: [
      { offset: 0, color: '#ffeaa7' },
      { offset: 1, color: '#fab1a0' },
    ],
  },
  {
    type: 'linear',
    angle: 135,
    stops: [
      { offset: 0, color: '#74b9ff' },
      { offset: 1, color: '#0984e3' },
    ],
  },
  {
    type: 'linear',
    angle: 180,
    stops: [
      { offset: 0, color: '#fd79a8' },
      { offset: 1, color: '#e84393' },
    ],
  },
  {
    type: 'radial',
    centerX: 0.3,
    centerY: 0.3,
    radius: 0.7,
    stops: [
      { offset: 0, color: '#a29bfe' },
      { offset: 1, color: '#6c5ce7' },
    ],
  },
  {
    type: 'linear',
    angle: 270,
    stops: [
      { offset: 0, color: '#00b894' },
      { offset: 0.5, color: '#00cec9' },
      { offset: 1, color: '#55efc4' },
    ],
  },
];

// Helper function to create CSS gradient string
function createCSSGradient(gradient: GradientStyle): string {
  const stops = gradient.stops
    .map(stop => `${stop.color} ${Math.round(stop.offset * 100)}%`)
    .join(', ');

  if (gradient.type === 'linear') {
    return `linear-gradient(${gradient.angle || 0}deg, ${stops})`;
  } else {
    const centerX = Math.round((gradient.centerX || 0.5) * 100);
    const centerY = Math.round((gradient.centerY || 0.5) * 100);
    const radius = Math.round((gradient.radius || 0.5) * 100);
    return `radial-gradient(circle ${radius}% at ${centerX}% ${centerY}%, ${stops})`;
  }
}
