<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .test-box {
            background-color: red;
            color: white;
            padding: 20px;
            font-size: 24px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-box-2 {
            background-color: blue;
            color: white;
            padding: 20px;
            font-size: 18px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-box">
        🧪 TEST PAGE - If you can see this, HTML/CSS is working
    </div>
    <div class="test-box-2">
        📊 Browser Info:
        <br>User Agent: <span id="userAgent"></span>
        <br>Screen Size: <span id="screenSize"></span>
        <br>Viewport Size: <span id="viewportSize"></span>
    </div>
    
    <script>
        console.log('🧪 Test page loaded');
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('screenSize').textContent = screen.width + 'x' + screen.height;
        document.getElementById('viewportSize').textContent = window.innerWidth + 'x' + window.innerHeight;
        
        // Test if we can access localhost:8085
        fetch('http://localhost:8085')
            .then(response => {
                console.log('✅ Can access localhost:8085:', response.status);
            })
            .catch(error => {
                console.error('❌ Cannot access localhost:8085:', error);
            });
    </script>
</body>
</html>
