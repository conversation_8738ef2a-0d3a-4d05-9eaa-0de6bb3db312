/**
 * Export Dialog Component
 * 
 * Provides a comprehensive UI for exporting analysis results, designs,
 * and presentations with customizable options and real-time preview.
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { 
  Download, 
  FileText, 
  Image, 
  Presentation,
  Settings,
  Eye,
  Palette,
  Layout,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { 
  professionalExportService, 
  ExportOptions, 
  ExportFormat,
  ExportResult 
} from '@/services/export/professionalExportService';
import { AnalysisResult } from '@/types/conversation';
import { DesignElement } from '@/services/designIntegration/analysisToDesignConverter';
import { Presentation as PresentationType } from '@/services/designIntegration/presentationGenerator';

interface ExportDialogProps {
  trigger: React.ReactNode;
  data: AnalysisResult[] | DesignElement[] | PresentationType;
  dataType: 'analysis' | 'design' | 'presentation';
  defaultOptions?: Partial<ExportOptions>;
  onExportComplete?: (result: ExportResult) => void;
}

export const ExportDialog: React.FC<ExportDialogProps> = ({
  trigger,
  data,
  dataType,
  defaultOptions = {},
  onExportComplete,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [exportOptions, setExportOptions] = useState<Partial<ExportOptions>>({
    format: 'pdf',
    quality: 'standard',
    layout: {
      pageSize: 'A4',
      orientation: 'portrait',
      margins: { top: 72, right: 72, bottom: 72, left: 72 },
      columns: 1,
      spacing: 16,
      header: true,
      footer: true,
      pageNumbers: true
    },
    branding: {
      logoPosition: 'top-right',
      companyName: 'ChatCraft Trainer Pro',
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#0ea5e9',
        text: '#1e293b',
        background: '#ffffff'
      },
      fonts: {
        heading: 'Inter',
        body: 'Inter',
        code: 'JetBrains Mono'
      }
    },
    metadata: {
      title: getDefaultTitle(),
      author: 'ChatCraft Trainer Pro',
      createdAt: Date.now(),
      version: '1.0',
      confidentiality: 'internal'
    },
    ...defaultOptions
  });

  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<ExportResult | null>(null);
  const [previewMode, setPreviewMode] = useState(false);

  function getDefaultTitle(): string {
    switch (dataType) {
      case 'analysis':
        return `Analysis Report - ${new Date().toLocaleDateString()}`;
      case 'design':
        return `Design Export - ${new Date().toLocaleDateString()}`;
      case 'presentation':
        return (data as PresentationType).title || `Presentation - ${new Date().toLocaleDateString()}`;
      default:
        return `Export - ${new Date().toLocaleDateString()}`;
    }
  }

  const formatOptions: { value: ExportFormat; label: string; icon: React.ComponentType<{ className?: string }>; description: string }[] = [
    { value: 'pdf', label: 'PDF Document', icon: FileText, description: 'Professional PDF with layouts' },
    { value: 'html', label: 'HTML Page', icon: FileText, description: 'Interactive web page' },
    { value: 'png', label: 'PNG Image', icon: Image, description: 'High-quality raster image' },
    { value: 'svg', label: 'SVG Vector', icon: Image, description: 'Scalable vector graphics' },
    { value: 'docx', label: 'Word Document', icon: FileText, description: 'Microsoft Word format' },
    { value: 'pptx', label: 'PowerPoint', icon: Presentation, description: 'Microsoft PowerPoint' },
    { value: 'json', label: 'JSON Data', icon: FileText, description: 'Raw data export' },
  ];

  const availableFormats = formatOptions.filter(format => {
    switch (dataType) {
      case 'analysis':
        return ['pdf', 'html', 'docx', 'json'].includes(format.value);
      case 'design':
        return ['pdf', 'svg', 'png'].includes(format.value);
      case 'presentation':
        return ['pdf', 'pptx', 'html'].includes(format.value);
      default:
        return true;
    }
  });

  const handleExport = async () => {
    setIsExporting(true);
    setExportResult(null);

    try {
      let result: ExportResult;

      switch (dataType) {
        case 'analysis':
          result = await professionalExportService.exportAnalysisResults(
            data as AnalysisResult[],
            exportOptions
          );
          break;
        case 'design':
          result = await professionalExportService.exportDesignElements(
            data as DesignElement[],
            exportOptions
          );
          break;
        case 'presentation':
          result = await professionalExportService.exportPresentation(
            data as PresentationType,
            exportOptions
          );
          break;
        default:
          throw new Error('Invalid data type');
      }

      setExportResult(result);
      onExportComplete?.(result);

      if (result.success && result.data) {
        // Trigger download
        const url = URL.createObjectURL(result.data as Blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      setExportResult({
        success: false,
        filename: '',
        error: error instanceof Error ? error.message : 'Export failed',
        metadata: exportOptions.metadata!
      });
    } finally {
      setIsExporting(false);
    }
  };

  const updateExportOptions = (updates: Partial<ExportOptions>) => {
    setExportOptions(prev => ({
      ...prev,
      ...updates,
      layout: { ...prev.layout, ...updates.layout },
      branding: { ...prev.branding, ...updates.branding },
      metadata: { ...prev.metadata, ...updates.metadata }
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
          </DialogTitle>
          <DialogDescription>
            Configure export settings and download your {dataType} in professional formats.
          </DialogDescription>
        </DialogHeader>

        <div className="flex h-[600px]">
          {/* Settings Panel */}
          <div className="w-1/2 border-r pr-4">
            <ScrollArea className="h-full">
              <Tabs defaultValue="format" className="space-y-4">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="format">Format</TabsTrigger>
                  <TabsTrigger value="layout">Layout</TabsTrigger>
                  <TabsTrigger value="branding">Branding</TabsTrigger>
                  <TabsTrigger value="metadata">Metadata</TabsTrigger>
                </TabsList>

                {/* Format Tab */}
                <TabsContent value="format" className="space-y-4">
                  <div className="space-y-3">
                    <Label>Export Format</Label>
                    <div className="grid grid-cols-1 gap-2">
                      {availableFormats.map((format) => {
                        const Icon = format.icon;
                        return (
                          <Card
                            key={format.value}
                            className={cn(
                              "cursor-pointer transition-all hover:shadow-md",
                              exportOptions.format === format.value && "ring-2 ring-primary"
                            )}
                            onClick={() => updateExportOptions({ format: format.value })}
                          >
                            <CardContent className="p-3">
                              <div className="flex items-center gap-3">
                                <Icon className="h-5 w-5 text-primary" />
                                <div className="flex-1">
                                  <div className="font-medium">{format.label}</div>
                                  <div className="text-sm text-muted-foreground">{format.description}</div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label>Quality</Label>
                    <Select
                      value={exportOptions.quality}
                      onValueChange={(value) => updateExportOptions({ quality: value as 'draft' | 'standard' | 'high' | 'print' })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft (Fast)</SelectItem>
                        <SelectItem value="standard">Standard</SelectItem>
                        <SelectItem value="high">High Quality</SelectItem>
                        <SelectItem value="print">Print Ready</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>

                {/* Layout Tab */}
                <TabsContent value="layout" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Page Size</Label>
                      <Select
                        value={exportOptions.layout?.pageSize}
                        onValueChange={(value) => updateExportOptions({ 
                          layout: { ...exportOptions.layout, pageSize: value as 'A4' | 'Letter' | 'Legal' | 'A3' }
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="A4">A4</SelectItem>
                          <SelectItem value="A3">A3</SelectItem>
                          <SelectItem value="Letter">Letter</SelectItem>
                          <SelectItem value="Legal">Legal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Orientation</Label>
                      <Select
                        value={exportOptions.layout?.orientation}
                        onValueChange={(value) => updateExportOptions({ 
                          layout: { ...exportOptions.layout, orientation: value as 'portrait' | 'landscape' }
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="portrait">Portrait</SelectItem>
                          <SelectItem value="landscape">Landscape</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label>Margins (pixels)</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs">Top</Label>
                        <Input
                          type="number"
                          value={exportOptions.layout?.margins?.top || 72}
                          onChange={(e) => updateExportOptions({
                            layout: {
                              ...exportOptions.layout,
                              margins: {
                                ...exportOptions.layout?.margins,
                                top: parseInt(e.target.value) || 72
                              }
                            }
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Right</Label>
                        <Input
                          type="number"
                          value={exportOptions.layout?.margins?.right || 72}
                          onChange={(e) => updateExportOptions({
                            layout: {
                              ...exportOptions.layout,
                              margins: {
                                ...exportOptions.layout?.margins,
                                right: parseInt(e.target.value) || 72
                              }
                            }
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Bottom</Label>
                        <Input
                          type="number"
                          value={exportOptions.layout?.margins?.bottom || 72}
                          onChange={(e) => updateExportOptions({
                            layout: {
                              ...exportOptions.layout,
                              margins: {
                                ...exportOptions.layout?.margins,
                                bottom: parseInt(e.target.value) || 72
                              }
                            }
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Left</Label>
                        <Input
                          type="number"
                          value={exportOptions.layout?.margins?.left || 72}
                          onChange={(e) => updateExportOptions({
                            layout: {
                              ...exportOptions.layout,
                              margins: {
                                ...exportOptions.layout?.margins,
                                left: parseInt(e.target.value) || 72
                              }
                            }
                          })}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Include Header</Label>
                      <Switch
                        checked={exportOptions.layout?.header}
                        onCheckedChange={(checked) => updateExportOptions({
                          layout: { ...exportOptions.layout, header: checked }
                        })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Include Footer</Label>
                      <Switch
                        checked={exportOptions.layout?.footer}
                        onCheckedChange={(checked) => updateExportOptions({
                          layout: { ...exportOptions.layout, footer: checked }
                        })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Page Numbers</Label>
                      <Switch
                        checked={exportOptions.layout?.pageNumbers}
                        onCheckedChange={(checked) => updateExportOptions({
                          layout: { ...exportOptions.layout, pageNumbers: checked }
                        })}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Branding Tab */}
                <TabsContent value="branding" className="space-y-4">
                  <div className="space-y-3">
                    <Label>Company Name</Label>
                    <Input
                      value={exportOptions.branding?.companyName || ''}
                      onChange={(e) => updateExportOptions({
                        branding: { ...exportOptions.branding, companyName: e.target.value }
                      })}
                      placeholder="Your Company Name"
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={exportOptions.branding?.colors?.primary || '#2563eb'}
                        onChange={(e) => updateExportOptions({
                          branding: {
                            ...exportOptions.branding,
                            colors: {
                              ...exportOptions.branding?.colors,
                              primary: e.target.value
                            }
                          }
                        })}
                        className="w-16 h-10"
                      />
                      <Input
                        value={exportOptions.branding?.colors?.primary || '#2563eb'}
                        onChange={(e) => updateExportOptions({
                          branding: {
                            ...exportOptions.branding,
                            colors: {
                              ...exportOptions.branding?.colors,
                              primary: e.target.value
                            }
                          }
                        })}
                        placeholder="#2563eb"
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label>Font Family</Label>
                    <Select
                      value={exportOptions.branding?.fonts?.body}
                      onValueChange={(value) => updateExportOptions({
                        branding: {
                          ...exportOptions.branding,
                          fonts: {
                            ...exportOptions.branding?.fonts,
                            heading: value,
                            body: value
                          }
                        }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Inter">Inter</SelectItem>
                        <SelectItem value="Roboto">Roboto</SelectItem>
                        <SelectItem value="Open Sans">Open Sans</SelectItem>
                        <SelectItem value="Poppins">Poppins</SelectItem>
                        <SelectItem value="Lato">Lato</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>

                {/* Metadata Tab */}
                <TabsContent value="metadata" className="space-y-4">
                  <div className="space-y-3">
                    <Label>Title</Label>
                    <Input
                      value={exportOptions.metadata?.title || ''}
                      onChange={(e) => updateExportOptions({
                        metadata: { ...exportOptions.metadata, title: e.target.value }
                      })}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Author</Label>
                    <Input
                      value={exportOptions.metadata?.author || ''}
                      onChange={(e) => updateExportOptions({
                        metadata: { ...exportOptions.metadata, author: e.target.value }
                      })}
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Subject</Label>
                    <Input
                      value={exportOptions.metadata?.subject || ''}
                      onChange={(e) => updateExportOptions({
                        metadata: { ...exportOptions.metadata, subject: e.target.value }
                      })}
                      placeholder="Brief description"
                    />
                  </div>

                  <div className="space-y-3">
                    <Label>Confidentiality</Label>
                    <Select
                      value={exportOptions.metadata?.confidentiality}
                      onValueChange={(value) => updateExportOptions({
                        metadata: { ...exportOptions.metadata, confidentiality: value as 'public' | 'internal' | 'confidential' | 'restricted' }
                      })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">Public</SelectItem>
                        <SelectItem value="internal">Internal</SelectItem>
                        <SelectItem value="confidential">Confidential</SelectItem>
                        <SelectItem value="restricted">Restricted</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
              </Tabs>
            </ScrollArea>
          </div>

          {/* Preview Panel */}
          <div className="w-1/2 pl-4">
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Preview</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {previewMode ? 'Hide' : 'Show'} Preview
                </Button>
              </div>

              <Card className="flex-1">
                <CardContent className="p-4 h-full">
                  {previewMode ? (
                    <div className="h-full bg-white border rounded p-4 text-sm">
                      <div className="text-center mb-4">
                        <h1 className="text-xl font-bold" style={{ color: exportOptions.branding?.colors?.primary }}>
                          {exportOptions.metadata?.title}
                        </h1>
                        <p className="text-gray-600">Preview of exported {dataType}</p>
                      </div>
                      <div className="space-y-2 text-xs text-gray-500">
                        <p>Format: {exportOptions.format?.toUpperCase()}</p>
                        <p>Quality: {exportOptions.quality}</p>
                        <p>Page Size: {exportOptions.layout?.pageSize}</p>
                        <p>Orientation: {exportOptions.layout?.orientation}</p>
                      </div>
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>Click "Show Preview" to see export preview</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Export Status */}
              {exportResult && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4"
                >
                  <Card className={cn(
                    "border-2",
                    exportResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"
                  )}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        {exportResult.success ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-600" />
                        )}
                        <div className="flex-1">
                          <p className="font-medium">
                            {exportResult.success ? 'Export Successful' : 'Export Failed'}
                          </p>
                          {exportResult.success ? (
                            <p className="text-sm text-muted-foreground">
                              Downloaded: {exportResult.filename}
                              {exportResult.size && ` (${(exportResult.size / 1024).toFixed(1)} KB)`}
                            </p>
                          ) : (
                            <p className="text-sm text-red-600">{exportResult.error}</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Export Button */}
              <div className="mt-4 flex gap-2">
                <Button
                  onClick={handleExport}
                  disabled={isExporting}
                  className="flex-1"
                >
                  {isExporting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Exporting...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Export {exportOptions.format?.toUpperCase()}
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExportDialog;
