import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { 
  Eye, 
  RefreshCw, 
  Settings, 
  Archive, 
  Users, 
  UserMinus, 
  Play,
  Copy,
  Trash2,
  Star
} from 'lucide-react';

interface ChatAnalysisNodeMenuProps {
  node: SimpleNode;
  position: { x: number; y: number };
  onClose: () => void;
  onAction: (action: string, nodeId: string, analysisRecord?: AnalysisResult) => void;
}

export const ChatAnalysisNodeMenu: React.FC<ChatAnalysisNodeMenuProps> = ({
  node,
  position,
  onClose,
  onAction
}) => {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  
  const analysisRecord = node.data.analysisRecord;
  const isAnalysisNode = node.data.nodeType === 'analysis-record' && analysisRecord;

  const handleAction = async (action: string) => {
    setIsLoading(action);
    try {
      await onAction(action, node.id, analysisRecord);
    } finally {
      setIsLoading(null);
      onClose();
    }
  };

  const menuStyle: React.CSSProperties = {
    position: 'absolute',
    left: position.x,
    top: position.y,
    zIndex: 20,
    minWidth: '200px',
    backgroundColor: 'rgba(20, 20, 40, 0.98)',
    backdropFilter: 'blur(15px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '12px',
    boxShadow: '0 12px 48px rgba(0, 0, 0, 0.6)',
    padding: '8px',
  };

  const buttonClass = "w-full justify-start text-left text-sm h-8 px-3 py-1 hover:bg-white/10 transition-colors";
  const iconClass = "w-4 h-4 mr-2";

  return (
    <Card style={menuStyle}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="px-3 py-2 border-b border-white/10">
          <div className="text-white text-sm font-medium truncate">
            {node.data.label || node.id}
          </div>
          {isAnalysisNode && (
            <div className="text-gray-400 text-xs mt-1">
              {analysisRecord?.analysisType} • {analysisRecord?.style}
            </div>
          )}
        </div>

        {/* Analysis Record Actions */}
        {isAnalysisNode && (
          <div className="py-2">
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('view-details')}
              disabled={isLoading === 'view-details'}
            >
              <Eye className={iconClass} />
              View Details
            </Button>
            
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('re-run-analysis')}
              disabled={isLoading === 're-run-analysis'}
            >
              <RefreshCw className={`${iconClass} ${isLoading === 're-run-analysis' ? 'animate-spin' : ''}`} />
              Re-run Analysis
            </Button>
            
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('modify-parameters')}
              disabled={isLoading === 'modify-parameters'}
            >
              <Settings className={iconClass} />
              Modify Parameters
            </Button>
            
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('duplicate-analysis')}
              disabled={isLoading === 'duplicate-analysis'}
            >
              <Copy className={iconClass} />
              Duplicate Analysis
            </Button>
            
            <Separator className="my-2 bg-white/10" />
            
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('initiate-simulation')}
              disabled={isLoading === 'initiate-simulation'}
            >
              <Play className={iconClass} />
              Initiate Chat Simulation
            </Button>
            
            <Separator className="my-2 bg-white/10" />
          </div>
        )}

        {/* Clustering Actions */}
        <div className="py-2">
          <Button
            variant="ghost"
            className={buttonClass}
            onClick={() => handleAction('add-to-cluster')}
            disabled={isLoading === 'add-to-cluster'}
          >
            <Users className={iconClass} />
            Add to Cluster
          </Button>
          
          {node.data.clusterId && (
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('remove-from-cluster')}
              disabled={isLoading === 'remove-from-cluster'}
            >
              <UserMinus className={iconClass} />
              Remove from Cluster
            </Button>
          )}
        </div>

        <Separator className="my-2 bg-white/10" />

        {/* General Actions */}
        <div className="py-2">
          {isAnalysisNode && (
            <Button
              variant="ghost"
              className={buttonClass}
              onClick={() => handleAction('toggle-favorite')}
              disabled={isLoading === 'toggle-favorite'}
            >
              <Star className={`${iconClass} ${analysisRecord?.rating ? 'fill-yellow-400 text-yellow-400' : ''}`} />
              {analysisRecord?.rating ? 'Remove Favorite' : 'Add to Favorites'}
            </Button>
          )}
          
          <Button
            variant="ghost"
            className={buttonClass}
            onClick={() => handleAction('archive')}
            disabled={isLoading === 'archive'}
          >
            <Archive className={iconClass} />
            Archive
          </Button>
          
          <Button
            variant="ghost"
            className={`${buttonClass} text-red-400 hover:text-red-300 hover:bg-red-500/10`}
            onClick={() => handleAction('delete')}
            disabled={isLoading === 'delete'}
          >
            <Trash2 className={iconClass} />
            Delete
          </Button>
        </div>

        {/* Close button */}
        <div className="px-3 py-2 border-t border-white/10">
          <Button
            variant="ghost"
            className="w-full text-gray-400 hover:text-white text-xs h-6"
            onClick={onClose}
          >
            Close
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Menu action types for type safety
export type ChatAnalysisNodeMenuAction = 
  | 'view-details'
  | 're-run-analysis'
  | 'modify-parameters'
  | 'duplicate-analysis'
  | 'initiate-simulation'
  | 'add-to-cluster'
  | 'remove-from-cluster'
  | 'toggle-favorite'
  | 'archive'
  | 'delete';
