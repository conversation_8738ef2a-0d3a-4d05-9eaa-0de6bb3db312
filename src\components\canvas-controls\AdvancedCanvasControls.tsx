import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { 
  Settings, 
  Layers, 
  Search, 
  Filter,
  Play,
  Pause,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Grid,
  Eye,
  EyeOff,
  Palette,
  Layout,
  Target,
  Zap,
  Brain,
  BarChart3,
  Download,
  Upload,
  Save,
  Share2,
  HelpCircle,
  Maximize,
  Minimize
} from 'lucide-react';
import { DrawingObject } from '@/types/figma';
import { SavedAnalysis } from '@/types/conversation';

export interface CanvasControlState {
  zoom: number;
  pan: { x: number; y: number };
  rotation: number;
  gridVisible: boolean;
  snapToGrid: boolean;
  showRulers: boolean;
  showGuides: boolean;
  layerFilters: string[];
  searchQuery: string;
  selectedTool: string;
  brushSize: number;
  opacity: number;
  colorScheme: string;
  animationSpeed: number;
  autoSave: boolean;
  collaborationMode: boolean;
}

export interface LayerGroup {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  opacity: number;
  blendMode: string;
  objects: string[];
  children: LayerGroup[];
}

// Union type for all possible control values
type CanvasControlValue = CanvasControlState[keyof CanvasControlState];

interface AdvancedCanvasControlsProps {
  objects: Record<string, DrawingObject>;
  analyses: SavedAnalysis[];
  onControlChange?: (control: string, value: CanvasControlValue) => void;
  onObjectFilter?: (filteredIds: string[]) => void;
  onLayerUpdate?: (layerId: string, updates: Partial<LayerGroup>) => void;
  onSimulationControl?: (action: 'play' | 'pause' | 'reset' | 'step') => void;
  className?: string;
}

export const AdvancedCanvasControls: React.FC<AdvancedCanvasControlsProps> = ({
  objects,
  analyses,
  onControlChange,
  onObjectFilter,
  onLayerUpdate,
  onSimulationControl,
  className,
}) => {
  const [controlState, setControlState] = useState<CanvasControlState>({
    zoom: 100,
    pan: { x: 0, y: 0 },
    rotation: 0,
    gridVisible: true,
    snapToGrid: true,
    showRulers: true,
    showGuides: true,
    layerFilters: [],
    searchQuery: '',
    selectedTool: 'select',
    brushSize: 10,
    opacity: 100,
    colorScheme: 'default',
    animationSpeed: 1,
    autoSave: true,
    collaborationMode: false,
  });

  const [layerGroups, setLayerGroups] = useState<LayerGroup[]>([
    {
      id: 'analysis-layer',
      name: 'Analysis Results',
      visible: true,
      locked: false,
      opacity: 100,
      blendMode: 'normal',
      objects: [],
      children: [],
    },
    {
      id: 'design-layer',
      name: 'Design Elements',
      visible: true,
      locked: false,
      opacity: 100,
      blendMode: 'normal',
      objects: [],
      children: [],
    },
    {
      id: 'connections-layer',
      name: 'Connections',
      visible: true,
      locked: false,
      opacity: 80,
      blendMode: 'multiply',
      objects: [],
      children: [],
    },
  ]);

  const [filteredObjects, setFilteredObjects] = useState<string[]>([]);
  const [isSimulationRunning, setIsSimulationRunning] = useState(false);
  const [selectedObjects, setSelectedObjects] = useState<string[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [presets, setPresets] = useState<Record<string, Partial<CanvasControlState>>>({
    'Design Mode': {
      gridVisible: true,
      showRulers: true,
      snapToGrid: true,
      zoom: 100,
    },
    'Analysis Mode': {
      gridVisible: false,
      showRulers: false,
      snapToGrid: false,
      zoom: 75,
    },
    'Presentation Mode': {
      gridVisible: false,
      showRulers: false,
      showGuides: false,
      zoom: 50,
    },
  });

  // Update control state
  const updateControl = useCallback((control: keyof CanvasControlState, value: CanvasControlValue) => {
    setControlState(prev => {
      const newState = { ...prev, [control]: value };
      onControlChange?.(control, value);
      return newState;
    });
  }, [onControlChange]);

  // Search and filter objects
  const performSearch = useCallback(() => {
    if (!controlState.searchQuery.trim()) {
      setFilteredObjects(Object.keys(objects));
      onObjectFilter?.(Object.keys(objects));
      return;
    }

    const query = controlState.searchQuery.toLowerCase();
    const filtered = Object.entries(objects).filter(([id, obj]) => {
      return (
        obj.name.toLowerCase().includes(query) ||
        obj.type.toLowerCase().includes(query) ||
        (obj.style?.fill && obj.style.fill.toLowerCase().includes(query))
      );
    }).map(([id]) => id);

    setFilteredObjects(filtered);
    onObjectFilter?.(filtered);
  }, [controlState.searchQuery, objects, onObjectFilter]);

  // Apply layer filters
  const applyLayerFilters = useCallback(() => {
    if (controlState.layerFilters.length === 0) {
      setFilteredObjects(Object.keys(objects));
      return;
    }

    const filtered = Object.entries(objects).filter(([id, obj]) => {
      return controlState.layerFilters.includes(obj.layerId);
    }).map(([id]) => id);

    setFilteredObjects(filtered);
    onObjectFilter?.(filtered);
  }, [controlState.layerFilters, objects, onObjectFilter]);

  // Load preset
  const loadPreset = useCallback((presetName: string) => {
    const preset = presets[presetName];
    if (preset) {
      setControlState(prev => ({ ...prev, ...preset }));
      Object.entries(preset).forEach(([key, value]) => {
        onControlChange?.(key, value);
      });
    }
  }, [presets, onControlChange]);

  // Save current state as preset
  const saveAsPreset = useCallback((name: string) => {
    setPresets(prev => ({
      ...prev,
      [name]: { ...controlState },
    }));
  }, [controlState]);

  // Reset controls
  const resetControls = useCallback(() => {
    const defaultState: CanvasControlState = {
      zoom: 100,
      pan: { x: 0, y: 0 },
      rotation: 0,
      gridVisible: true,
      snapToGrid: true,
      showRulers: true,
      showGuides: true,
      layerFilters: [],
      searchQuery: '',
      selectedTool: 'select',
      brushSize: 10,
      opacity: 100,
      colorScheme: 'default',
      animationSpeed: 1,
      autoSave: true,
      collaborationMode: false,
    };
    setControlState(defaultState);
  }, []);

  // Auto-search when query changes
  useEffect(() => {
    const timeoutId = setTimeout(performSearch, 300);
    return () => clearTimeout(timeoutId);
  }, [performSearch]);

  // Apply layer filters when they change
  useEffect(() => {
    applyLayerFilters();
  }, [applyLayerFilters]);

  return (
    <div className={`w-80 h-full flex flex-col bg-white border-l ${className}`}>
      {/* Header */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Settings className="h-5 w-5" />
            Canvas Controls
            <Badge variant="secondary" className="ml-auto">
              {Object.keys(objects).length} objects
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Main Controls */}
      <div className="flex-1 overflow-auto">
        <Tabs defaultValue="view" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mx-2">
            <TabsTrigger value="view">View</TabsTrigger>
            <TabsTrigger value="layers">Layers</TabsTrigger>
            <TabsTrigger value="search">Search</TabsTrigger>
            <TabsTrigger value="sim">Sim</TabsTrigger>
          </TabsList>

          {/* View Controls */}
          <TabsContent value="view" className="p-4 space-y-4">
            <div>
              <label className="text-sm font-medium">Zoom</label>
              <div className="flex items-center gap-2 mt-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => updateControl('zoom', Math.max(10, controlState.zoom - 10))}
                >
                  <ZoomOut className="h-3 w-3" />
                </Button>
                <Slider
                  value={[controlState.zoom]}
                  onValueChange={([value]) => updateControl('zoom', value)}
                  min={10}
                  max={500}
                  step={5}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => updateControl('zoom', Math.min(500, controlState.zoom + 10))}
                >
                  <ZoomIn className="h-3 w-3" />
                </Button>
                <span className="text-xs w-12">{controlState.zoom}%</span>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Rotation</label>
              <div className="flex items-center gap-2 mt-1">
                <Slider
                  value={[controlState.rotation]}
                  onValueChange={([value]) => updateControl('rotation', value)}
                  min={-180}
                  max={180}
                  step={1}
                  className="flex-1"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => updateControl('rotation', 0)}
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
                <span className="text-xs w-12">{controlState.rotation}°</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="gridVisible"
                  checked={controlState.gridVisible}
                  onChange={(e) => updateControl('gridVisible', e.target.checked)}
                />
                <label htmlFor="gridVisible" className="text-sm">Show Grid</label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="snapToGrid"
                  checked={controlState.snapToGrid}
                  onChange={(e) => updateControl('snapToGrid', e.target.checked)}
                />
                <label htmlFor="snapToGrid" className="text-sm">Snap to Grid</label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="showRulers"
                  checked={controlState.showRulers}
                  onChange={(e) => updateControl('showRulers', e.target.checked)}
                />
                <label htmlFor="showRulers" className="text-sm">Show Rulers</label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="showGuides"
                  checked={controlState.showGuides}
                  onChange={(e) => updateControl('showGuides', e.target.checked)}
                />
                <label htmlFor="showGuides" className="text-sm">Show Guides</label>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Color Scheme</label>
              <select
                value={controlState.colorScheme}
                onChange={(e) => updateControl('colorScheme', e.target.value)}
                className="w-full mt-1 p-2 border rounded-md text-sm"
              >
                <option value="default">Default</option>
                <option value="dark">Dark Mode</option>
                <option value="high-contrast">High Contrast</option>
                <option value="colorblind">Colorblind Friendly</option>
              </select>
            </div>

            <div className="pt-2 border-t">
              <label className="text-sm font-medium mb-2 block">Presets</label>
              <div className="grid grid-cols-1 gap-1">
                {Object.keys(presets).map(presetName => (
                  <Button
                    key={presetName}
                    size="sm"
                    variant="outline"
                    onClick={() => loadPreset(presetName)}
                    className="justify-start text-xs"
                  >
                    {presetName}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Layer Management */}
          <TabsContent value="layers" className="p-4 space-y-4">
            <div className="space-y-2">
              {layerGroups.map(layer => (
                <Card key={layer.id} className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0"
                        onClick={() => {
                          const updated = { ...layer, visible: !layer.visible };
                          setLayerGroups(prev => prev.map(l => l.id === layer.id ? updated : l));
                          onLayerUpdate?.(layer.id, updated);
                        }}
                      >
                        {layer.visible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                      </Button>
                      <span className="text-sm font-medium">{layer.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {layer.objects.length}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div>
                      <label className="text-xs text-muted-foreground">Opacity</label>
                      <Slider
                        value={[layer.opacity]}
                        onValueChange={([value]) => {
                          const updated = { ...layer, opacity: value };
                          setLayerGroups(prev => prev.map(l => l.id === layer.id ? updated : l));
                          onLayerUpdate?.(layer.id, updated);
                        }}
                        min={0}
                        max={100}
                        step={5}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <label className="text-xs text-muted-foreground">Blend Mode</label>
                      <select
                        value={layer.blendMode}
                        onChange={(e) => {
                          const updated = { ...layer, blendMode: e.target.value };
                          setLayerGroups(prev => prev.map(l => l.id === layer.id ? updated : l));
                          onLayerUpdate?.(layer.id, updated);
                        }}
                        className="w-full mt-1 p-1 border rounded text-xs"
                      >
                        <option value="normal">Normal</option>
                        <option value="multiply">Multiply</option>
                        <option value="screen">Screen</option>
                        <option value="overlay">Overlay</option>
                      </select>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            <Button size="sm" className="w-full">
              <Layers className="h-4 w-4 mr-2" />
              Add Layer
            </Button>
          </TabsContent>

          {/* Search and Filter */}
          <TabsContent value="search" className="p-4 space-y-4">
            <div>
              <label className="text-sm font-medium">Search Objects</label>
              <div className="relative mt-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, type, or color..."
                  value={controlState.searchQuery}
                  onChange={(e) => updateControl('searchQuery', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">Layer Filters</label>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                >
                  <Filter className="h-3 w-3" />
                </Button>
              </div>
              
              <div className="space-y-1">
                {layerGroups.map(layer => (
                  <div key={layer.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={`filter-${layer.id}`}
                      checked={controlState.layerFilters.includes(layer.id)}
                      onChange={(e) => {
                        const filters = e.target.checked
                          ? [...controlState.layerFilters, layer.id]
                          : controlState.layerFilters.filter(f => f !== layer.id);
                        updateControl('layerFilters', filters);
                      }}
                    />
                    <label htmlFor={`filter-${layer.id}`} className="text-sm">
                      {layer.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {showAdvancedFilters && (
              <Card className="p-3">
                <div className="text-sm font-medium mb-2">Advanced Filters</div>
                <div className="space-y-2 text-xs">
                  <div>
                    <label>Object Type:</label>
                    <select className="w-full mt-1 p-1 border rounded">
                      <option value="">All Types</option>
                      <option value="rectangle">Rectangle</option>
                      <option value="circle">Circle</option>
                      <option value="text">Text</option>
                    </select>
                  </div>
                  
                  <div>
                    <label>Size Range:</label>
                    <div className="flex gap-1 mt-1">
                      <Input placeholder="Min" className="text-xs" />
                      <Input placeholder="Max" className="text-xs" />
                    </div>
                  </div>
                </div>
              </Card>
            )}

            <div className="text-xs text-muted-foreground">
              {filteredObjects.length} of {Object.keys(objects).length} objects visible
            </div>
          </TabsContent>

          {/* Simulation Controls */}
          <TabsContent value="sim" className="p-4 space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Simulation</label>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant={isSimulationRunning ? "default" : "outline"}
                  onClick={() => {
                    setIsSimulationRunning(!isSimulationRunning);
                    onSimulationControl?.(isSimulationRunning ? 'pause' : 'play');
                  }}
                >
                  {isSimulationRunning ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSimulationControl?.('reset')}
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSimulationControl?.('step')}
                >
                  Step
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Animation Speed</label>
              <Slider
                value={[controlState.animationSpeed]}
                onValueChange={([value]) => updateControl('animationSpeed', value)}
                min={0.1}
                max={5}
                step={0.1}
                className="mt-2"
              />
              <div className="text-xs text-muted-foreground mt-1">
                {controlState.animationSpeed}x speed
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="autoSave"
                  checked={controlState.autoSave}
                  onChange={(e) => updateControl('autoSave', e.target.checked)}
                />
                <label htmlFor="autoSave" className="text-sm">Auto-save</label>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="collaborationMode"
                  checked={controlState.collaborationMode}
                  onChange={(e) => updateControl('collaborationMode', e.target.checked)}
                />
                <label htmlFor="collaborationMode" className="text-sm">Collaboration</label>
              </div>
            </div>

            <div className="pt-2 border-t">
              <div className="text-sm font-medium mb-2">Quick Actions</div>
              <div className="grid grid-cols-2 gap-2">
                <Button size="sm" variant="outline">
                  <Save className="h-3 w-3 mr-1" />
                  Save
                </Button>
                <Button size="sm" variant="outline">
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
                <Button size="sm" variant="outline">
                  <Share2 className="h-3 w-3 mr-1" />
                  Share
                </Button>
                <Button size="sm" variant="outline">
                  <HelpCircle className="h-3 w-3 mr-1" />
                  Help
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Footer */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Selected: {selectedObjects.length}</span>
            <Button
              size="sm"
              variant="ghost"
              onClick={resetControls}
              className="h-6 px-2 text-xs"
            >
              Reset All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
