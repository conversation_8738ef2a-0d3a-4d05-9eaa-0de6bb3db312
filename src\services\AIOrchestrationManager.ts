/**
 * AI Orchestration Manager
 * Coordinates between different AI services and provides unified interface
 */

import { OpenRouterService } from './openRouterService';
import { OllamaLocalAI } from './OllamaLocalAI';
import { LangFlowWorkflow } from '../components/ai-integration/LangFlowVisualDesigner';

export interface AIProvider {
  id: string;
  name: string;
  type: 'cloud' | 'local' | 'hybrid';
  status: 'available' | 'unavailable' | 'error';
  models: string[];
  capabilities: string[];
  priority: number;
}

export interface AIRequest {
  prompt: string;
  model?: string;
  provider?: string;
  options?: {
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    context?: string[];
    format?: 'text' | 'json';
  };
  fallbackProviders?: string[];
  timeout?: number;
}

export interface AIResponse {
  response: string;
  provider: string;
  model: string;
  metadata: {
    tokensUsed: number;
    responseTime: number;
    cost?: number;
    confidence?: number;
  };
}

export interface WorkflowExecution {
  id: string;
  workflow: LangFlowWorkflow;
  status: 'pending' | 'running' | 'completed' | 'failed';
  results: Record<string, Record<string, unknown>>;
  startTime: Date;
  endTime?: Date;
  error?: string;
}

interface WorkflowNode {
  id: string;
  connections: {
    inputs: Record<string, string | string[]>;
    outputs?: Record<string, string | string[]>;
  };
  [key: string]: unknown;
}

export class AIOrchestrationManager {
  private providers: Map<string, AIProvider> = new Map();
  private openRouter: OpenRouterService;
  private ollama: OllamaLocalAI;
  private activeWorkflows: Map<string, WorkflowExecution> = new Map();
  private requestQueue: AIRequest[] = [];
  private isProcessingQueue = false;

  constructor() {
    // TODO: Replace 'YOUR_API_KEY' with a secure config/env value
    this.openRouter = new OpenRouterService('YOUR_API_KEY');
    this.ollama = new OllamaLocalAI();
    this.initializeProviders();
  }

  /**
   * Initialize AI providers
   */
  private async initializeProviders(): Promise<void> {
    // Initialize OpenRouter
    try {
      // TODO: OpenRouterService does not support getAvailableModels; update as needed
      // const openRouterModels = await this.openRouter.getAvailableModels();
      this.providers.set('openrouter', {
        id: 'openrouter',
        name: 'OpenRouter',
        type: 'cloud',
        status: 'available',
        models: ['openai/gpt-3.5-turbo'], // Placeholder, update if model listing is implemented
        capabilities: ['text-generation', 'chat', 'analysis', 'coding'],
        priority: 1,
      });
    } catch (error) {
      this.providers.set('openrouter', {
        id: 'openrouter',
        name: 'OpenRouter',
        type: 'cloud',
        status: 'unavailable',
        models: [],
        capabilities: [],
        priority: 1,
      });
    }

    // Initialize Ollama
    try {
      const ollamaStatus = await this.ollama.checkStatus();
      this.providers.set('ollama', {
        id: 'ollama',
        name: 'Ollama Local',
        type: 'local',
        status: ollamaStatus.isAvailable ? 'available' : 'unavailable',
        models: ollamaStatus.models.map(m => m.name),
        capabilities: ['text-generation', 'chat', 'embeddings', 'offline'],
        priority: 2,
      });
    } catch (error) {
      this.providers.set('ollama', {
        id: 'ollama',
        name: 'Ollama Local',
        type: 'local',
        status: 'unavailable',
        models: [],
        capabilities: [],
        priority: 2,
      });
    }
  }

  /**
   * Get available providers
   */
  getProviders(): AIProvider[] {
    return Array.from(this.providers.values()).sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get best provider for a request
   */
  private selectProvider(request: AIRequest): string {
    // If specific provider requested, use it if available
    if (request.provider && this.providers.has(request.provider)) {
      const provider = this.providers.get(request.provider)!;
      if (provider.status === 'available') {
        return request.provider;
      }
    }

    // Find best available provider
    const availableProviders = Array.from(this.providers.values())
      .filter(p => p.status === 'available')
      .sort((a, b) => a.priority - b.priority);

    if (availableProviders.length === 0) {
      throw new Error('No AI providers available');
    }

    // Prefer local providers for privacy-sensitive requests
    const localProvider = availableProviders.find(p => p.type === 'local');
    if (localProvider && request.options?.format !== 'json') {
      return localProvider.id;
    }

    return availableProviders[0].id;
  }

  /**
   * Execute AI request with automatic provider selection and fallback
   */
  async executeRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    let lastError: Error | null = null;

    // Try primary provider
    try {
      const providerId = this.selectProvider(request);
      const response = await this.executeWithProvider(request, providerId);
      return response;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
    }

    // Try fallback providers
    if (request.fallbackProviders) {
      for (const fallbackId of request.fallbackProviders) {
        if (this.providers.has(fallbackId)) {
          try {
            const response = await this.executeWithProvider(request, fallbackId);
            return response;
          } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
          }
        }
      }
    }

    // Try any remaining available provider
    const availableProviders = Array.from(this.providers.values())
      .filter(p => p.status === 'available' && p.id !== request.provider)
      .filter(p => !request.fallbackProviders?.includes(p.id));

    for (const provider of availableProviders) {
      try {
        const response = await this.executeWithProvider(request, provider.id);
        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
      }
    }

    throw lastError || new Error('All AI providers failed');
  }

  /**
   * Execute request with specific provider
   */
  private async executeWithProvider(request: AIRequest, providerId: string): Promise<AIResponse> {
    const startTime = Date.now();
    const provider = this.providers.get(providerId);
    
    if (!provider) {
      throw new Error(`Provider ${providerId} not found`);
    }

    let response: string;
    let model: string;
    let tokensUsed = 0;

    switch (providerId) {
      case 'openrouter':
        // TODO: OpenRouterService does not support generateResponse; use analyzeQuestion or implement as needed
        // const openRouterResponse = await this.openRouter.generateResponse({ ... });
        // response = openRouterResponse.choices[0]?.message?.content || '';
        // model = request.model || 'openai/gpt-3.5-turbo';
        // tokensUsed = openRouterResponse.usage?.total_tokens || 0;
        response = '[OpenRouterService: implement analyzeQuestion integration]';
        model = request.model || 'openai/gpt-3.5-turbo';
        tokensUsed = 0;
        break;

      case 'ollama':
        const ollamaModel = request.model || await this.ollama.selectBestModel('conversation') || 'llama2:7b';
        const ollamaResponse = await this.ollama.generate({
          model: ollamaModel,
          prompt: request.prompt,
          system: request.options?.systemMessage,
          options: {
            temperature: request.options?.temperature,
            num_predict: request.options?.maxTokens,
          },
        });
        response = ollamaResponse.response;
        model = ollamaModel;
        tokensUsed = ollamaResponse.eval_count || 0;
        break;

      default:
        throw new Error(`Unsupported provider: ${providerId}`);
    }

    const responseTime = Date.now() - startTime;

    return {
      response,
      provider: providerId,
      model,
      metadata: {
        tokensUsed,
        responseTime,
        confidence: 0.8 + Math.random() * 0.2, // Simulated confidence
      },
    };
  }

  /**
   * Execute LangFlow workflow
   */
  async executeWorkflow(workflow: LangFlowWorkflow): Promise<WorkflowExecution> {
    const execution: WorkflowExecution = {
      id: `exec_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      workflow,
      status: 'running',
      results: {},
      startTime: new Date(),
    };

    this.activeWorkflows.set(execution.id, execution);

    try {
      // Execute nodes in topological order
      const executionOrder = this.getExecutionOrder(workflow.nodes as unknown as WorkflowNode[]);
      
      for (const nodeId of executionOrder) {
        const node = workflow.nodes.find(n => n.id === nodeId);
        if (!node) continue;

        const nodeResult = await this.executeWorkflowNode(node, execution.results);
        // Ensure nodeResult is an object for type safety
        execution.results[nodeId] = (nodeResult && typeof nodeResult === 'object' && !Array.isArray(nodeResult))
          ? nodeResult as Record<string, unknown>
          : {};
      }

      execution.status = 'completed';
      execution.endTime = new Date();
    } catch (error) {
      execution.status = 'failed';
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.endTime = new Date();
    }

    return execution;
  }

  /**
   * Execute individual workflow node
   */
  private async executeWorkflowNode(
    node: unknown,
    previousResults: Record<string, Record<string, unknown>>
  ): Promise<unknown> {
    // Ensure nodeData always has an id property
    const nodeData = node as { id: string; type: string; data: { config: Record<string, unknown> }; connections: { inputs: Record<string, string> } };
    switch (nodeData.type) {
      case 'input':
        return { value: nodeData.data.config.placeholder || 'Sample input' };

      case 'llm':
        const promptRaw = this.resolveNodeInput(nodeData as WorkflowNode, 'prompt', previousResults) || 'Hello';
        const prompt = typeof promptRaw === 'string' ? promptRaw : String(promptRaw);
        const contextRaw = this.resolveNodeInput(nodeData as WorkflowNode, 'context', previousResults);
        const context = typeof contextRaw === 'string' ? contextRaw : String(contextRaw);
        const request: AIRequest = {
          prompt,
          model: String(nodeData.data.config.model),
          options: {
            ...nodeData.data.config,
            systemMessage: context,
          },
        };
        const response = await this.executeRequest(request);
        return { response: response.response, metadata: response.metadata };

      case 'prompt':
        const template = String(nodeData.data.config.template) || '{input}';
        const variables = this.resolveNodeInput(nodeData as WorkflowNode, 'variables', previousResults) || {};
        let formatted = template;
        Object.entries(variables as Record<string, unknown>).forEach(([key, value]) => {
          formatted = formatted.replace(new RegExp(`{${key}}`, 'g'), String(value));
        });
        return { formatted };

      case 'tool':
        const toolInput = this.resolveNodeInput(nodeData as WorkflowNode, 'input', previousResults);
        return { result: `Tool ${String(nodeData.data.config.toolName)} executed with: ${toolInput}` };

      case 'transform':
        const data = this.resolveNodeInput(nodeData as WorkflowNode, 'data', previousResults);
        const transformType = String(nodeData.data.config.transformType);
        switch (transformType) {
          case 'json':
            return { transformed: JSON.stringify(data) };
          case 'uppercase':
            return { transformed: String(data).toUpperCase() };
          case 'lowercase':
            return { transformed: String(data).toLowerCase() };
          default:
            return { transformed: data };
        }

      case 'condition':
        const value = this.resolveNodeInput(nodeData as WorkflowNode, 'value', previousResults);
        const condition = String(nodeData.data.config.condition);
        const operator = String(nodeData.data.config.operator);
        const compareValue = nodeData.data.config.value;
        
        let result = false;
        switch (operator) {
          case 'equals':
            result = value === compareValue;
            break;
          case 'contains':
            result = String(value).includes(String(compareValue));
            break;
          case 'greater':
            result = Number(value) > Number(compareValue);
            break;
          default:
            result = Boolean(value);
        }
        
        return { result, branch: result ? 'true' : 'false' };

      case 'output':
        const outputValue = this.resolveNodeInput(node as WorkflowNode, 'result', previousResults);
        return { final: outputValue };

      default:
        return { result: 'Node executed successfully' };
    }
  }

  /**
   * Resolve node input from previous results
   */
  private resolveNodeInput(node: WorkflowNode, inputName: string, previousResults: Record<string, Record<string, unknown>>): unknown {
    const connection = node.connections.inputs[inputName];
    if (!connection) return null;
    // Support both string and string[]
    const connStr = Array.isArray(connection) ? connection[0] : connection;
    if (!connStr) return null;
    const [sourceNodeId, outputName] = connStr.split(':');
    const sourceResult = previousResults[sourceNodeId];
    return sourceResult?.[outputName];
  }

  /**
   * Get execution order for workflow nodes (topological sort)
   */
  private getExecutionOrder(nodes: WorkflowNode[]): string[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const order: string[] = [];

    const visit = (nodeId: string) => {
      if (visiting.has(nodeId)) {
        throw new Error('Circular dependency detected in workflow');
      }
      if (visited.has(nodeId)) return;

      visiting.add(nodeId);
      
      const node = nodes.find(n => n.id === nodeId);
      if (node) {
        // Visit dependencies first
        Object.values(node.connections.inputs).forEach((connection) => {
          const connStr = Array.isArray(connection) ? connection[0] : connection;
          if (connStr) {
            const [sourceNodeId] = connStr.split(':');
            visit(sourceNodeId);
          }
        });
      }

      visiting.delete(nodeId);
      visited.add(nodeId);
      order.push(nodeId);
    };

    nodes.forEach(node => visit(node.id));
    return order;
  }

  /**
   * Add request to queue for batch processing
   */
  addToQueue(request: AIRequest): void {
    this.requestQueue.push(request);
    this.processQueue();
  }

  /**
   * Process request queue
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.requestQueue.length === 0) return;

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!;
      try {
        await this.executeRequest(request);
      } catch (error) {
        console.error('Queued request failed:', error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Get active workflow executions
   */
  getActiveWorkflows(): WorkflowExecution[] {
    return Array.from(this.activeWorkflows.values());
  }

  /**
   * Cancel workflow execution
   */
  cancelWorkflow(executionId: string): boolean {
    const execution = this.activeWorkflows.get(executionId);
    if (execution && execution.status === 'running') {
      execution.status = 'failed';
      execution.error = 'Cancelled by user';
      execution.endTime = new Date();
      return true;
    }
    return false;
  }

  /**
   * Get provider statistics
   */
  getProviderStats(): Record<string, {
    requestCount: number;
    successRate: number;
    averageResponseTime: number;
    totalTokensUsed: number;
  }> {
    // This would be implemented with actual tracking in a real system
    return {
      openrouter: {
        requestCount: 150,
        successRate: 0.98,
        averageResponseTime: 1200,
        totalTokensUsed: 45000,
      },
      ollama: {
        requestCount: 75,
        successRate: 0.95,
        averageResponseTime: 800,
        totalTokensUsed: 22000,
      },
    };
  }

  /**
   * Health check for all providers
   */
  async checkProviderStatus(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [id, provider] of this.providers) {
      try {
        switch (id) {
          case 'openrouter':
            // TODO: OpenRouterService does not support getAvailableModels; update as needed
            // await this.openRouter.getAvailableModels();
            results[id] = true;
            break;
          case 'ollama':
            const status = await this.ollama.checkStatus();
            results[id] = status.isAvailable;
            break;
          default:
            results[id] = false;
        }
      } catch (error) {
        results[id] = false;
      }
    }

    return results;
  }
}
