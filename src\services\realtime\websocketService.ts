import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';

/**
 * Real-time update event types
 */
export type RealtimeEventType = 
  | 'analysis_created'
  | 'analysis_updated'
  | 'analysis_deleted'
  | 'cluster_created'
  | 'cluster_updated'
  | 'cluster_deleted'
  | 'simulation_created'
  | 'simulation_updated'
  | 'simulation_progress'
  | 'simulation_completed'
  | 'canvas_state_updated'
  | 'user_joined'
  | 'user_left'
  | 'connection_status';

/**
 * Real-time event data structure
 */
export interface RealtimeEvent {
  type: RealtimeEventType;
  timestamp: string;
  userId?: string;
  data: unknown;
  metadata?: {
    source: string;
    version: string;
    sessionId: string;
  };
}

/**
 * WebSocket connection status
 */
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';

/**
 * Event listener callback type
 */
export type EventListener = (event: RealtimeEvent) => void;

/**
 * WebSocket service for real-time updates
 */
export class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private listeners: Map<RealtimeEventType, Set<EventListener>> = new Map();
  private connectionStatusListeners: Set<(status: ConnectionStatus) => void> = new Set();
  private status: ConnectionStatus = 'disconnected';

  constructor(url: string = 'ws://localhost:8080/ws') {
    this.url = url;
  }

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.setStatus('connecting');

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.setStatus('connected');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const realtimeEvent: RealtimeEvent = JSON.parse(event.data);
            this.handleEvent(realtimeEvent);
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.setStatus('disconnected');
          this.stopHeartbeat();
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.setStatus('error');
          reject(error);
        };

      } catch (error) {
        this.setStatus('error');
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.stopHeartbeat();
    this.setStatus('disconnected');
  }

  /**
   * Send message to server
   */
  send(event: Omit<RealtimeEvent, 'timestamp' | 'metadata'>): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const fullEvent: RealtimeEvent = {
        ...event,
        timestamp: new Date().toISOString(),
        metadata: {
          source: 'client',
          version: '1.0.0',
          sessionId: this.generateSessionId()
        }
      };
      
      this.ws.send(JSON.stringify(fullEvent));
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  /**
   * Subscribe to specific event type
   */
  on(eventType: RealtimeEventType, listener: EventListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    this.listeners.get(eventType)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(eventType)?.delete(listener);
    };
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionStatus(listener: (status: ConnectionStatus) => void): () => void {
    this.connectionStatusListeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.connectionStatusListeners.delete(listener);
    };
  }

  /**
   * Get current connection status
   */
  getStatus(): ConnectionStatus {
    return this.status;
  }

  /**
   * Handle incoming events
   */
  private handleEvent(event: RealtimeEvent): void {
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error(`Error in event listener for ${event.type}:`, error);
        }
      });
    }
  }

  /**
   * Set connection status and notify listeners
   */
  private setStatus(status: ConnectionStatus): void {
    if (this.status !== status) {
      this.status = status;
      this.connectionStatusListeners.forEach(listener => {
        try {
          listener(status);
        } catch (error) {
          console.error('Error in connection status listener:', error);
        }
      });
    }
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.setStatus('reconnecting');
    this.reconnectAttempts++;
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({
          type: 'connection_status',
          data: { type: 'ping' }
        });
      }
    }, 30000); // Send ping every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
}

/**
 * Singleton WebSocket service instance
 */
export const websocketService = new WebSocketService();

/**
 * High-level event emitters for specific data types
 */
export class RealtimeEventEmitters {
  /**
   * Emit analysis result created event
   */
  static emitAnalysisCreated(analysis: AnalysisResult): void {
    websocketService.send({
      type: 'analysis_created',
      data: analysis
    });
  }

  /**
   * Emit analysis result updated event
   */
  static emitAnalysisUpdated(analysis: AnalysisResult): void {
    websocketService.send({
      type: 'analysis_updated',
      data: analysis
    });
  }

  /**
   * Emit analysis result deleted event
   */
  static emitAnalysisDeleted(analysisId: string): void {
    websocketService.send({
      type: 'analysis_deleted',
      data: { id: analysisId }
    });
  }

  /**
   * Emit cluster created event
   */
  static emitClusterCreated(cluster: AnalysisCluster): void {
    websocketService.send({
      type: 'cluster_created',
      data: cluster
    });
  }

  /**
   * Emit cluster updated event
   */
  static emitClusterUpdated(cluster: AnalysisCluster): void {
    websocketService.send({
      type: 'cluster_updated',
      data: cluster
    });
  }

  /**
   * Emit cluster deleted event
   */
  static emitClusterDeleted(clusterId: string): void {
    websocketService.send({
      type: 'cluster_deleted',
      data: { id: clusterId }
    });
  }

  /**
   * Emit simulation progress event
   */
  static emitSimulationProgress(simulationId: string, progress: {
    currentPrompt: number;
    totalPrompts: number;
    percentage: number;
    currentResult?: unknown;
  }): void {
    websocketService.send({
      type: 'simulation_progress',
      data: {
        simulationId,
        ...progress
      }
    });
  }

  /**
   * Emit simulation completed event
   */
  static emitSimulationCompleted(simulation: ChatSimulation): void {
    websocketService.send({
      type: 'simulation_completed',
      data: simulation
    });
  }
}
