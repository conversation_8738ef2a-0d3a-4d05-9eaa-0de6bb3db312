// Simple throttle utility for non-hook usage
export function throttleFn<T extends (...args: unknown[]) => void>(fn: T, limit: number): T {
  let inThrottle: boolean;
  let lastArgs: Parameters<T>;
  let lastContext: ThisParameterType<T>;
  return function(this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (!inThrottle) {
      fn.apply(this, args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
        if (lastArgs) {
          fn.apply(lastContext, lastArgs);
          lastArgs = undefined as unknown as Parameters<T>;
          lastContext = undefined as unknown as ThisParameterType<T>;
        }
      }, limit);
    } else {
      lastArgs = args;
      lastContext = this;
    }
  } as T;
}
