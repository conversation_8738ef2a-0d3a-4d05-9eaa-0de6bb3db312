import React from 'react';
import { 
  <PERSON><PERSON>, 
  Trash2, 
  Group, 
  Ungroup,
  BringToFront,
  SendToBack,
  ChevronUp,
  ChevronDown,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Lock,
  Unlock,
  Eye,
  EyeOff
} from 'lucide-react';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface TransformControlsProps {
  className?: string;
  onGroup?: () => void;
  onUngroup?: () => void;
  onBringToFront?: () => void;
  onSendToBack?: () => void;
  onBringForward?: () => void;
  onSendBackward?: () => void;
  onDuplicate?: () => void;
  onDelete?: () => void;
  onFlipHorizontal?: () => void;
  onFlipVertical?: () => void;
  onRotate?: (angle: number) => void;
}

export const TransformControls: React.FC<TransformControlsProps> = ({
  className,
  onGroup,
  onUngroup,
  onBringToFront,
  onSendToBack,
  onBringForward,
  onSendBackward,
  onDuplicate,
  onDelete,
  onFlipHorizontal,
  onFlipVertical,
  onRotate,
}) => {
  const {
    selectedObjectIds,
    getSelectedObjects,
    updateObject,
    deleteObject,
    duplicateObject,
  } = useFigmaCanvasStore();

  const selectedObjects = getSelectedObjects();
  const hasSelection = selectedObjects.length > 0;
  const multipleSelection = selectedObjects.length > 1;

  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate();
    } else {
      selectedObjectIds.forEach(id => duplicateObject(id));
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete();
    } else {
      selectedObjectIds.forEach(id => deleteObject(id));
    }
  };

  const handleToggleVisibility = () => {
    selectedObjectIds.forEach(id => {
      const object = selectedObjects.find(obj => obj.id === id);
      if (object) {
        updateObject(id, { visible: !object.visible });
      }
    });
  };

  const handleToggleLock = () => {
    selectedObjectIds.forEach(id => {
      const object = selectedObjects.find(obj => obj.id === id);
      if (object) {
        updateObject(id, { locked: !object.locked });
      }
    });
  };

  const handleRotate90 = () => {
    if (onRotate) {
      onRotate(90);
    } else {
      selectedObjectIds.forEach(id => {
        const object = selectedObjects.find(obj => obj.id === id);
        if (object) {
          updateObject(id, {
            transform: {
              ...object.transform,
              rotation: object.transform.rotation + 90,
            },
          });
        }
      });
    }
  };

  const handleFlipHorizontal = () => {
    if (onFlipHorizontal) {
      onFlipHorizontal();
    } else {
      selectedObjectIds.forEach(id => {
        const object = selectedObjects.find(obj => obj.id === id);
        if (object) {
          updateObject(id, {
            transform: {
              ...object.transform,
              scaleX: -object.transform.scaleX,
            },
          });
        }
      });
    }
  };

  const handleFlipVertical = () => {
    if (onFlipVertical) {
      onFlipVertical();
    } else {
      selectedObjectIds.forEach(id => {
        const object = selectedObjects.find(obj => obj.id === id);
        if (object) {
          updateObject(id, {
            transform: {
              ...object.transform,
              scaleY: -object.transform.scaleY,
            },
          });
        }
      });
    }
  };

  // Get common properties for toggle states
  const getCommonProperty = (property: string) => {
    const values = selectedObjects.map(obj => {
      const keys = property.split('.');
      let value: unknown = obj;
      for (const key of keys) {
        if (typeof value === 'object' && value !== null && key in value) {
          value = (value as Record<string, unknown>)[key];
        } else {
          value = undefined;
          break;
        }
      }
      return value;
    });
    
    const firstValue = values[0];
    const allSame = values.every(v => v === firstValue);
    return allSame ? firstValue : null;
  };

  const allVisible = getCommonProperty('visible');
  const allLocked = getCommonProperty('locked');

  if (!hasSelection) {
    return null;
  }

  return (
    <div className={cn(
      "flex items-center gap-2 p-2 bg-white border border-gray-200 rounded-lg shadow-sm",
      className
    )}>
      {/* Basic Actions */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDuplicate}
          className="w-8 h-8 p-0"
          title="Duplicate (Ctrl+D)"
        >
          <Copy className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDelete}
          className="w-8 h-8 p-0 text-red-500 hover:text-red-700"
          title="Delete (Del)"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Grouping */}
      {multipleSelection && (
        <>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onGroup}
              className="w-8 h-8 p-0"
              title="Group (Ctrl+G)"
            >
              <Group className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onUngroup}
              className="w-8 h-8 p-0"
              title="Ungroup (Ctrl+Shift+G)"
            >
              <Ungroup className="w-4 h-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />
        </>
      )}

      {/* Layer Order */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBringToFront}
          className="w-8 h-8 p-0"
          title="Bring to Front (Ctrl+Shift+])"
        >
          <BringToFront className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onBringForward}
          className="w-8 h-8 p-0"
          title="Bring Forward (Ctrl+])"
        >
          <ChevronUp className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onSendBackward}
          className="w-8 h-8 p-0"
          title="Send Backward (Ctrl+[)"
        >
          <ChevronDown className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onSendToBack}
          className="w-8 h-8 p-0"
          title="Send to Back (Ctrl+Shift+[)"
        >
          <SendToBack className="w-4 h-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Transform */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRotate90}
          className="w-8 h-8 p-0"
          title="Rotate 90° (R)"
        >
          <RotateCw className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleFlipHorizontal}
          className="w-8 h-8 p-0"
          title="Flip Horizontal (H)"
        >
          <FlipHorizontal className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleFlipVertical}
          className="w-8 h-8 p-0"
          title="Flip Vertical (V)"
        >
          <FlipVertical className="w-4 h-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Visibility and Lock */}
      <div className="flex items-center gap-1">
        <Button
          variant={allVisible === false ? "default" : "ghost"}
          size="sm"
          onClick={handleToggleVisibility}
          className={cn(
            "w-8 h-8 p-0",
            allVisible === false && "bg-gray-500 text-white hover:bg-gray-600"
          )}
          title={allVisible ? "Hide" : "Show"}
        >
          {allVisible === false ? (
            <EyeOff className="w-4 h-4" />
          ) : (
            <Eye className="w-4 h-4" />
          )}
        </Button>
        
        <Button
          variant={allLocked ? "default" : "ghost"}
          size="sm"
          onClick={handleToggleLock}
          className={cn(
            "w-8 h-8 p-0",
            allLocked && "bg-red-500 text-white hover:bg-red-600"
          )}
          title={allLocked ? "Unlock" : "Lock"}
        >
          {allLocked ? (
            <Lock className="w-4 h-4" />
          ) : (
            <Unlock className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* Selection Info */}
      <Separator orientation="vertical" className="h-6" />
      <div className="text-xs text-gray-500 px-2">
        {selectedObjects.length} selected
      </div>
    </div>
  );
};

// Keyboard shortcuts hook for transform controls
export const useTransformKeyboardShortcuts = (
  onGroup?: () => void,
  onUngroup?: () => void,
  onBringToFront?: () => void,
  onSendToBack?: () => void,
  onBringForward?: () => void,
  onSendBackward?: () => void,
  onDuplicate?: () => void,
  onDelete?: () => void
) => {
  const { selectedObjectIds, duplicateObject, deleteObject } = useFigmaCanvasStore();

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      if (selectedObjectIds.length === 0) return;

      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'd':
            e.preventDefault();
            if (onDuplicate) {
              onDuplicate();
            } else {
              selectedObjectIds.forEach(id => duplicateObject(id));
            }
            break;
          case 'g':
            e.preventDefault();
            if (e.shiftKey) {
              onUngroup?.();
            } else {
              onGroup?.();
            }
            break;
          case ']':
            e.preventDefault();
            if (e.shiftKey) {
              onBringToFront?.();
            } else {
              onBringForward?.();
            }
            break;
          case '[':
            e.preventDefault();
            if (e.shiftKey) {
              onSendToBack?.();
            } else {
              onSendBackward?.();
            }
            break;
        }
      } else {
        switch (e.key) {
          case 'Delete':
          case 'Backspace':
            e.preventDefault();
            if (onDelete) {
              onDelete();
            } else {
              selectedObjectIds.forEach(id => deleteObject(id));
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [
    selectedObjectIds,
    onGroup,
    onUngroup,
    onBringToFront,
    onSendToBack,
    onBringForward,
    onSendBackward,
    onDuplicate,
    onDelete,
    duplicateObject,
    deleteObject,
  ]);
};
