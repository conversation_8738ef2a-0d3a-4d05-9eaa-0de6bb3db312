import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { ThemeProvider } from '../design-system/theme-provider';

// Custom render that wraps UI in ThemeProvider
const AllProviders = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>{children}</ThemeProvider>
);

const customRender = (ui: React.ReactElement, options?: RenderOptions) =>
  render(ui, { wrapper: AllProviders, ...options });

// re-export everything
export * from '@testing-library/react';
export { customRender as render };
