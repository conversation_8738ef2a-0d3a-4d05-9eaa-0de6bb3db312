/**
 * Analysis to Design Converter
 * 
 * Converts analysis results into visual design elements, templates, and presentations.
 * Provides intelligent mapping between analysis data and design components.
 */

import { AnalysisResult } from '@/types/conversation';
import { CharacterPersona } from '@/types/analysis';

export interface DesignElement {
  id: string;
  type: 'text' | 'shape' | 'chart' | 'infographic' | 'flowchart' | 'mindmap';
  data: Record<string, unknown>;
  style: DesignStyle;
  position: Position;
  size: Size;
  metadata: ElementMetadata;
}

export interface DesignStyle {
  color: string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  textAlign?: 'left' | 'center' | 'right';
  opacity?: number;
  shadow?: string;
  gradient?: string;
}

export interface Position {
  x: number;
  y: number;
  z?: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface ElementMetadata {
  analysisId: string;
  analysisType: string;
  createdAt: number;
  tags: string[];
  category: string;
  template?: string;
}

export interface DesignTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  elements: DesignElement[];
  layout: TemplateLayout;
  variables: TemplateVariable[];
}

export interface TemplateLayout {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  margins: { top: number; right: number; bottom: number; left: number };
  grid?: { rows: number; columns: number; gap: number };
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'color' | 'number' | 'image';
  defaultValue: unknown;
  description: string;
}

export class AnalysisToDesignConverter {
  private colorPalettes = {
    professional: ['#2563eb', '#7c3aed', '#059669', '#dc2626', '#ea580c'],
    creative: ['#ec4899', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b'],
    minimal: ['#374151', '#6b7280', '#9ca3af', '#d1d5db', '#f3f4f6'],
    vibrant: ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6'],
  };

  private fontPairs = {
    professional: { heading: 'Inter', body: 'Inter' },
    creative: { heading: 'Poppins', body: 'Open Sans' },
    minimal: { heading: 'Roboto', body: 'Roboto' },
    elegant: { heading: 'Playfair Display', body: 'Source Sans Pro' },
  };

  convertAnalysisToElements(
    analysis: AnalysisResult,
    templateType: string = 'auto',
    styleTheme: string = 'professional'
  ): DesignElement[] {
    const elements: DesignElement[] = [];
    const palette = this.colorPalettes[styleTheme as keyof typeof this.colorPalettes] || this.colorPalettes.professional;
    const fonts = this.fontPairs[styleTheme as keyof typeof this.fontPairs] || this.fontPairs.professional;

    // Create title element
    elements.push(this.createTitleElement(analysis, palette[0], fonts.heading));

    // Create question element
    elements.push(this.createQuestionElement(analysis, palette[1], fonts.body));

    // Create main analysis content
    elements.push(this.createAnalysisContentElement(analysis, palette[2], fonts.body));

    // Create follow-up questions if available
    if (analysis.followUpQuestions && analysis.followUpQuestions.length > 0) {
      elements.push(this.createFollowUpElement(analysis, palette[3], fonts.body));
    }

    // Create metadata element
    elements.push(this.createMetadataElement(analysis, palette[4], fonts.body));

    // Add analysis-type specific elements
    switch (analysis.analysisType) {
      case 'pros-cons':
        elements.push(...this.createProsConsElements(analysis, palette, fonts));
        break;
      case 'six-hats':
        elements.push(...this.createSixHatsElements(analysis, palette, fonts));
        break;
      case 'character':
        elements.push(...this.createCharacterElements(analysis, palette, fonts));
        break;
      case 'emotional-angles':
        elements.push(...this.createEmotionalElements(analysis, palette, fonts));
        break;
    }

    return elements;
  }

  private createTitleElement(analysis: AnalysisResult, color: string, font: string): DesignElement {
    return {
      id: `title-${analysis.id}`,
      type: 'text',
      data: {
        content: `Analysis: ${analysis.analysisType.charAt(0).toUpperCase() + analysis.analysisType.slice(1)}`,
        level: 'h1'
      },
      style: {
        color,
        fontSize: 24,
        fontFamily: font,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      position: { x: 50, y: 50 },
      size: { width: 400, height: 40 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['title', 'header'],
        category: 'text'
      }
    };
  }

  private createQuestionElement(analysis: AnalysisResult, color: string, font: string): DesignElement {
    return {
      id: `question-${analysis.id}`,
      type: 'text',
      data: {
        content: analysis.question,
        level: 'h2'
      },
      style: {
        color,
        fontSize: 18,
        fontFamily: font,
        fontWeight: '600',
        textAlign: 'left'
      },
      position: { x: 50, y: 120 },
      size: { width: 500, height: 60 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['question', 'input'],
        category: 'text'
      }
    };
  }

  private createAnalysisContentElement(analysis: AnalysisResult, color: string, font: string): DesignElement {
    return {
      id: `content-${analysis.id}`,
      type: 'text',
      data: {
        content: analysis.analysis,
        level: 'body'
      },
      style: {
        color,
        fontSize: 14,
        fontFamily: font,
        fontWeight: 'normal',
        textAlign: 'left'
      },
      position: { x: 50, y: 200 },
      size: { width: 600, height: 300 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['content', 'analysis'],
        category: 'text'
      }
    };
  }

  private createFollowUpElement(analysis: AnalysisResult, color: string, font: string): DesignElement {
    const followUpText = analysis.followUpQuestions?.map((q, i) => `${i + 1}. ${q}`).join('\n') || '';
    
    return {
      id: `followup-${analysis.id}`,
      type: 'text',
      data: {
        content: `Follow-up Questions:\n${followUpText}`,
        level: 'body'
      },
      style: {
        color,
        fontSize: 12,
        fontFamily: font,
        fontWeight: 'normal',
        textAlign: 'left'
      },
      position: { x: 50, y: 520 },
      size: { width: 500, height: 120 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['followup', 'questions'],
        category: 'text'
      }
    };
  }

  private createMetadataElement(analysis: AnalysisResult, color: string, font: string): DesignElement {
    const metadata = [
      `Model: ${analysis.model}`,
      `Style: ${analysis.style}`,
      `Date: ${analysis.timestamp.toLocaleDateString()}`,
      analysis.rating ? `Rating: ${analysis.rating}/10` : null
    ].filter(Boolean).join(' • ');

    return {
      id: `metadata-${analysis.id}`,
      type: 'text',
      data: {
        content: metadata,
        level: 'caption'
      },
      style: {
        color,
        fontSize: 10,
        fontFamily: font,
        fontWeight: 'normal',
        textAlign: 'center',
        opacity: 0.7
      },
      position: { x: 50, y: 660 },
      size: { width: 600, height: 20 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['metadata', 'info'],
        category: 'text'
      }
    };
  }

  private createProsConsElements(analysis: AnalysisResult, palette: string[], fonts: { heading: string; body: string }): DesignElement[] {
    // Extract pros and cons from analysis text
    const prosConsData = this.extractProsConsData(analysis.analysis);
    
    return [
      {
        id: `pros-${analysis.id}`,
        type: 'shape',
        data: {
          shape: 'rectangle',
          content: `Pros:\n${prosConsData.pros.join('\n')}`
        },
        style: {
          color: '#ffffff',
          backgroundColor: '#22c55e',
          borderRadius: 8,
          fontSize: 12,
          fontFamily: fonts.body
        },
        position: { x: 50, y: 350 },
        size: { width: 280, height: 150 },
        metadata: {
          analysisId: analysis.id,
          analysisType: analysis.analysisType,
          createdAt: Date.now(),
          tags: ['pros', 'positive'],
          category: 'pros-cons'
        }
      },
      {
        id: `cons-${analysis.id}`,
        type: 'shape',
        data: {
          shape: 'rectangle',
          content: `Cons:\n${prosConsData.cons.join('\n')}`
        },
        style: {
          color: '#ffffff',
          backgroundColor: '#ef4444',
          borderRadius: 8,
          fontSize: 12,
          fontFamily: fonts.body
        },
        position: { x: 350, y: 350 },
        size: { width: 280, height: 150 },
        metadata: {
          analysisId: analysis.id,
          analysisType: analysis.analysisType,
          createdAt: Date.now(),
          tags: ['cons', 'negative'],
          category: 'pros-cons'
        }
      }
    ];
  }

  private createSixHatsElements(analysis: AnalysisResult, palette: string[], fonts: { heading: string; body: string }): DesignElement[] {
    const hatColors = {
      white: '#ffffff',
      red: '#ef4444',
      black: '#1f2937',
      yellow: '#eab308',
      green: '#22c55e',
      blue: '#3b82f6'
    };

    const hatsData = this.extractSixHatsData(analysis.analysis);
    const elements: DesignElement[] = [];

    Object.entries(hatsData).forEach(([hat, content], index) => {
      if (content) {
        elements.push({
          id: `hat-${hat}-${analysis.id}`,
          type: 'shape',
          data: {
            shape: 'circle',
            content: `${hat.charAt(0).toUpperCase() + hat.slice(1)} Hat:\n${content}`
          },
          style: {
            color: hat === 'white' || hat === 'yellow' ? '#000000' : '#ffffff',
            backgroundColor: hatColors[hat as keyof typeof hatColors],
            borderRadius: 50,
            fontSize: 10,
            fontFamily: fonts.body
          },
          position: { x: 50 + (index % 3) * 200, y: 350 + Math.floor(index / 3) * 120 },
          size: { width: 180, height: 100 },
          metadata: {
            analysisId: analysis.id,
            analysisType: analysis.analysisType,
            createdAt: Date.now(),
            tags: ['six-hats', hat],
            category: 'thinking-hats'
          }
        });
      }
    });

    return elements;
  }

  private createCharacterElements(analysis: AnalysisResult, palette: string[], fonts: { heading: string; body: string }): DesignElement[] {
    if (!analysis.characterPersona) return [];

    return [
      {
        id: `character-${analysis.id}`,
        type: 'infographic',
        data: {
          character: analysis.characterPersona,
          analysis: analysis.analysis
        },
        style: {
          color: palette[0],
          backgroundColor: '#f8fafc',
          borderColor: palette[1],
          borderWidth: 2,
          borderRadius: 12,
          fontSize: 12,
          fontFamily: fonts.body
        },
        position: { x: 50, y: 350 },
        size: { width: 300, height: 200 },
        metadata: {
          analysisId: analysis.id,
          analysisType: analysis.analysisType,
          createdAt: Date.now(),
          tags: ['character', 'persona'],
          category: 'character-analysis'
        }
      }
    ];
  }

  private createEmotionalElements(analysis: AnalysisResult, palette: string[], fonts: { heading: string; body: string }): DesignElement[] {
    if (!analysis.selectedEmotions) return [];

    return analysis.selectedEmotions.map((emotion, index) => ({
      id: `emotion-${emotion}-${analysis.id}`,
      type: 'shape',
      data: {
        shape: 'rounded-rectangle',
        content: emotion,
        emotion: emotion
      },
      style: {
        color: '#ffffff',
        backgroundColor: palette[index % palette.length],
        borderRadius: 20,
        fontSize: 14,
        fontFamily: fonts.body,
        fontWeight: '600',
        textAlign: 'center'
      },
      position: { x: 50 + (index % 4) * 150, y: 350 + Math.floor(index / 4) * 60 },
      size: { width: 130, height: 40 },
      metadata: {
        analysisId: analysis.id,
        analysisType: analysis.analysisType,
        createdAt: Date.now(),
        tags: ['emotion', emotion],
        category: 'emotional-analysis'
      }
    }));
  }

  // Helper methods for extracting structured data
  private extractProsConsData(text: string): { pros: string[]; cons: string[] } {
    const pros: string[] = [];
    const cons: string[] = [];

    // Simple regex-based extraction (could be enhanced with NLP)
    const prosMatch = text.match(/(?:pros?|advantages?|benefits?|positives?)[\s\S]*?(?=cons?|disadvantages?|negatives?|$)/i);
    const consMatch = text.match(/(?:cons?|disadvantages?|negatives?|drawbacks?)[\s\S]*$/i);

    if (prosMatch) {
      const prosText = prosMatch[0];
      const prosItems = prosText.match(/[•\-\*]\s*(.+)/g) || [];
      pros.push(...prosItems.map(item => item.replace(/[•\-\*]\s*/, '').trim()));
    }

    if (consMatch) {
      const consText = consMatch[0];
      const consItems = consText.match(/[•\-\*]\s*(.+)/g) || [];
      cons.push(...consItems.map(item => item.replace(/[•\-\*]\s*/, '').trim()));
    }

    return { pros, cons };
  }

  private extractSixHatsData(text: string): Record<string, string> {
    const hats = ['white', 'red', 'black', 'yellow', 'green', 'blue'];
    const hatData: Record<string, string> = {};

    hats.forEach(hat => {
      const regex = new RegExp(`${hat}\\s+hat[:\\s]([\\s\\S]*?)(?=${hats.filter(h => h !== hat).join('|')}|$)`, 'i');
      const match = text.match(regex);
      if (match) {
        hatData[hat] = match[1].trim();
      }
    });

    return hatData;
  }

  // Template generation
  generateTemplate(elements: DesignElement[], templateName: string): DesignTemplate {
    return {
      id: `template-${Date.now()}`,
      name: templateName,
      description: `Generated template from ${elements[0]?.metadata.analysisType} analysis`,
      category: 'analysis',
      elements,
      layout: {
        width: 800,
        height: 600,
        orientation: 'landscape',
        margins: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      variables: [
        { name: 'primaryColor', type: 'color', defaultValue: '#2563eb', description: 'Primary theme color' },
        { name: 'fontFamily', type: 'text', defaultValue: 'Inter', description: 'Main font family' },
        { name: 'spacing', type: 'number', defaultValue: 20, description: 'Element spacing' }
      ]
    };
  }
}

export const analysisToDesignConverter = new AnalysisToDesignConverter();
