/**
 * Error Test Component
 * 
 * A component for testing error boundaries and error handling.
 * Only visible in development mode.
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useErrorBoundary } from '@/hooks/useErrorBoundary';

interface ErrorTestComponentProps {
  onError?: (error: Error) => void;
}

export const ErrorTestComponent: React.FC<ErrorTestComponentProps> = ({ onError }) => {
  const [shouldThrow, setShouldThrow] = useState(false);
  const { throwError, handleError } = useErrorBoundary({ 
    context: 'ErrorTestComponent',
    onError 
  });

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Throw error if flag is set
  if (shouldThrow) {
    throw new Error('Test error thrown by ErrorTestComponent');
  }

  const triggerRenderError = () => {
    setShouldThrow(true);
  };

  const triggerManualError = () => {
    throwError('Manual error triggered for testing');
  };

  const triggerHandledError = () => {
    handleError('Handled error for testing', 'medium');
  };

  const triggerAsyncError = async () => {
    try {
      // Simulate async operation that fails
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Async operation failed')), 100);
      });
    } catch (error) {
      handleError(error instanceof Error ? error : new Error('Unknown async error'), 'high');
    }
  };

  const triggerPromiseRejection = () => {
    // Create unhandled promise rejection
    Promise.reject(new Error('Unhandled promise rejection test'));
  };

  const triggerResourceError = () => {
    // Try to load a non-existent image
    const img = new Image();
    img.src = 'https://nonexistent-domain-12345.com/image.jpg';
    document.body.appendChild(img);
    setTimeout(() => document.body.removeChild(img), 1000);
  };

  return (
    <Card className="fixed bottom-4 left-4 w-80 z-50 bg-yellow-50 border-yellow-200">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm text-yellow-800">
          🧪 Error Testing (Dev Only)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="destructive"
            size="sm"
            onClick={triggerRenderError}
            className="text-xs"
          >
            Render Error
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={triggerManualError}
            className="text-xs"
          >
            Manual Error
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={triggerHandledError}
            className="text-xs"
          >
            Handled Error
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={triggerAsyncError}
            className="text-xs"
          >
            Async Error
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={triggerPromiseRejection}
            className="text-xs"
          >
            Promise Reject
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={triggerResourceError}
            className="text-xs"
          >
            Resource Error
          </Button>
        </div>
        <div className="text-xs text-yellow-700 mt-2">
          Use these buttons to test error boundaries and error handling.
        </div>
      </CardContent>
    </Card>
  );
};

export default ErrorTestComponent;
