import React, { memo } from "react";
import { type VariantProps } from "class-variance-authority";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { Calendar, Trash2, Eye, StickyNote, MessageSquare, Brain, User, Sparkles } from "lucide-react";
import { UserNote } from "@/types/conversation";

interface EnhancedNoteCardProps {
  note: UserNote;
  onView: () => void;
  onDiscover: () => void;
  onDelete: () => void;
}

export const EnhancedNoteCard: React.FC<EnhancedNoteCardProps> = memo(({
  note,
  onView,
  onDiscover,
  onDelete,
}) => {
  const getAnalysisIcon = (type?: string) => {
    switch (type) {
      case 'multiple': return <MessageSquare className="h-4 w-4 text-analysis-multiple-foreground" />;
      case 'deep': return <Brain className="h-4 w-4 text-analysis-deep-foreground" />;
      case 'character': return <User className="h-4 w-4 text-analysis-character-foreground" />;
      default: return <Sparkles className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getAnalysisTypeLabel = (type?: string) => {
    switch (type) {
      case 'multiple': return 'Multiple Answers';
      case 'deep': return 'Deep Analysis';
      case 'character': return 'Character Response';
      default: return 'Analysis';
    }
  };

  const getAnalysisTypeVariant = (type?: string): VariantProps<typeof badgeVariants>['variant'] => {
    switch (type) {
      case 'multiple': return 'analysis-multiple';
      case 'deep': return 'analysis-deep';
      case 'character': return 'analysis-character';
      default: return 'secondary';
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
  };

  const getAnalysisPreview = () => {
    if (note.originalAnalysis) {
      return truncateText(note.originalAnalysis, 150);
    }
    return "Analysis content not available";
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-300 ease-in-out hover:border-primary/30 bg-card/50 dark:bg-card/70 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-semibold flex items-start justify-between gap-4">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            <div className="p-2.5 rounded-lg bg-primary/10">
              {getAnalysisIcon(note.analysisType)}
            </div>
            <span className="line-clamp-2 flex-1 pt-1.5">{note.noteText}</span>
          </div>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onDelete}
            className="text-muted-foreground hover:text-destructive shrink-0 h-8 w-8"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </CardTitle>
        <CardDescription className="flex items-center gap-2 flex-wrap pt-1 pl-[50px]">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Calendar className="h-3.5 w-3.5" />
            {note.createdAt.toLocaleDateString()}
          </div>
          <Badge 
            variant={getAnalysisTypeVariant(note.analysisType)}
            className="text-xs"
          >
            {getAnalysisTypeLabel(note.analysisType)}
          </Badge>
          <Badge variant="outline">
            {note.linkedAnswerIds.length} linked answer{note.linkedAnswerIds.length !== 1 ? 's' : ''}
          </Badge>
          {note.tags && note.tags.map(tag => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2 space-y-4 pl-[62px] pr-6 pb-6">
        {/* Analysis Preview */}
        {note.originalAnalysis && (
          <div className="p-3 bg-background/50 dark:bg-muted/30 rounded-lg">
            <p className="text-sm text-foreground/80 line-clamp-3">
              {getAnalysisPreview()}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-4 border-t border-border/50">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onView}
            className="flex-1 text-secondary-foreground"
          >
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onDiscover} 
            className="flex-1 text-secondary-foreground"
          >
            <StickyNote className="mr-2 h-4 w-4" />
            Discover Context
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});
