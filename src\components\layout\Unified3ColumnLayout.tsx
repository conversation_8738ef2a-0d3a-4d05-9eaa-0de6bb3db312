/**
 * Unified 3-Column Layout System
 * 
 * A comprehensive, reusable 3-column layout system that provides consistent
 * structure across Visual Canvas and Conversation Suite with responsive design,
 * accessibility features, and customizable column configurations.
 */

import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { 
  ChevronLeft, 
  ChevronRight, 
  Maximize2, 
  Minimize2,
  Settings,
  Eye,
  EyeOff,
  MoreVertical,
  Grid3X3,
  Columns,
  Layout
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

// Context for column layout state
interface ColumnLayoutContext {
  layout: ColumnLayout;
  updateLayout: (updates: Partial<ColumnLayout>) => void;
  toggleColumn: (columnId: string) => void;
  resizeColumn: (columnId: string, width: number) => void;
  isResponsive: boolean;
}

const ColumnLayoutContext = createContext<ColumnLayoutContext | null>(null);

export const useColumnLayout = () => {
  const context = useContext(ColumnLayoutContext);
  if (!context) {
    throw new Error('useColumnLayout must be used within a Unified3ColumnLayout');
  }
  return context;
};

// Column configuration interface
interface ColumnConfig {
  id: string;
  title: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
  width: number; // Percentage of total width
  minWidth: number; // Minimum width in pixels
  maxWidth: number; // Maximum width in pixels
  visible: boolean;
  collapsible: boolean;
  resizable: boolean;
  scrollable: boolean;
  className?: string;
  headerActions?: React.ReactNode;
}

interface ColumnLayout {
  columns: ColumnConfig[];
  gap: number; // Gap between columns in pixels
  responsive: boolean;
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  mobileLayout: 'stack' | 'tabs' | 'accordion';
}

interface Unified3ColumnLayoutProps {
  layout: ColumnLayout;
  onLayoutChange?: (layout: ColumnLayout) => void;
  children: React.ReactNode[];
  className?: string;
  enableAnimations?: boolean;
  enableKeyboardNavigation?: boolean;
  ariaLabel?: string;
}

export const Unified3ColumnLayout: React.FC<Unified3ColumnLayoutProps> = ({
  layout: initialLayout,
  onLayoutChange,
  children,
  className,
  enableAnimations = true,
  enableKeyboardNavigation = true,
  ariaLabel = "Three column layout",
}) => {
  const [layout, setLayout] = useState<ColumnLayout>(initialLayout);
  const [activeColumn, setActiveColumn] = useState<string | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Responsive breakpoint detection
  const isResponsive = layout.responsive && windowWidth < layout.breakpoints.desktop;
  const isMobile = windowWidth < layout.breakpoints.mobile;
  const isTablet = windowWidth >= layout.breakpoints.mobile && windowWidth < layout.breakpoints.tablet;

  // Update layout and notify parent
  const updateLayout = (updates: Partial<ColumnLayout>) => {
    const newLayout = { ...layout, ...updates };
    setLayout(newLayout);
    onLayoutChange?.(newLayout);
  };

  // Toggle column visibility
  const toggleColumn = (columnId: string) => {
    const updatedColumns = layout.columns.map(col =>
      col.id === columnId ? { ...col, visible: !col.visible } : col
    );
    updateLayout({ columns: updatedColumns });
  };

  // Resize column
  const resizeColumn = (columnId: string, width: number) => {
    const updatedColumns = layout.columns.map(col =>
      col.id === columnId ? { ...col, width: Math.max(col.minWidth, Math.min(col.maxWidth, width)) } : col
    );
    updateLayout({ columns: updatedColumns });
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '1':
          case '2':
          case '3':
            event.preventDefault();
            const columnIndex = parseInt(event.key) - 1;
            const column = layout.columns[columnIndex];
            if (column) {
              setActiveColumn(column.id);
            }
            break;
          case 'h':
            event.preventDefault();
            // Toggle first column
            if (layout.columns[0]) {
              toggleColumn(layout.columns[0].id);
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [layout.columns, enableKeyboardNavigation, toggleColumn]);

  // Context value
  const contextValue: ColumnLayoutContext = {
    layout,
    updateLayout,
    toggleColumn,
    resizeColumn,
    isResponsive,
  };

  // Render mobile layout
  const renderMobileLayout = () => {
    const visibleColumns = layout.columns.filter(col => col.visible);
    
    if (layout.mobileLayout === 'tabs') {
      return (
        <div className="flex flex-col h-full">
          {/* Tab headers */}
          <div className="flex border-b bg-background">
            {visibleColumns.map((column, index) => {
              const Icon = column.icon;
              return (
                <Button
                  key={column.id}
                  variant={activeColumn === column.id ? "default" : "ghost"}
                  size="sm"
                  className="flex-1 rounded-none"
                  onClick={() => setActiveColumn(column.id)}
                >
                  {Icon && <Icon className="h-4 w-4 mr-2" />}
                  <span className="truncate">{column.title}</span>
                </Button>
              );
            })}
          </div>
          
          {/* Tab content */}
          <div className="flex-1 overflow-hidden">
            {visibleColumns.map((column, index) => (
              <div
                key={column.id}
                className={cn(
                  "h-full",
                  activeColumn === column.id ? "block" : "hidden"
                )}
              >
                {children[layout.columns.indexOf(column)]}
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Stack layout (default for mobile)
    return (
      <ScrollArea className="h-full">
        <div className="space-y-4 p-4">
          {visibleColumns.map((column, index) => {
            const Icon = column.icon;
            return (
              <Card key={column.id} className={column.className}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {Icon && <Icon className="h-5 w-5" />}
                      <div>
                        <h3 className="font-semibold">{column.title}</h3>
                        {column.subtitle && (
                          <p className="text-sm text-muted-foreground">{column.subtitle}</p>
                        )}
                      </div>
                    </div>
                    {column.headerActions}
                  </div>
                </CardHeader>
                <CardContent>
                  {children[layout.columns.indexOf(column)]}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ScrollArea>
    );
  };

  // Render desktop layout
  const renderDesktopLayout = () => {
    const visibleColumns = layout.columns.filter(col => col.visible);
    const totalVisibleWidth = visibleColumns.reduce((sum, col) => sum + col.width, 0);
    
    return (
      <div 
        className="flex h-full"
        style={{ gap: `${layout.gap}px` }}
      >
        {visibleColumns.map((column, index) => {
          const Icon = column.icon;
          const widthPercentage = (column.width / totalVisibleWidth) * 100;
          
          return (
            <motion.div
              key={column.id}
              className={cn(
                "flex flex-col",
                column.className
              )}
              style={{ 
                width: `${widthPercentage}%`,
                minWidth: `${column.minWidth}px`,
                maxWidth: `${column.maxWidth}px`
              }}
              initial={enableAnimations ? { opacity: 0, x: -20 } : {}}
              animate={{ opacity: 1, x: 0 }}
              exit={enableAnimations ? { opacity: 0, x: -20 } : {}}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {/* Column Header */}
              <div className="flex items-center justify-between p-4 border-b bg-muted/30">
                <div className="flex items-center gap-2">
                  {Icon && <Icon className="h-5 w-5" />}
                  <div>
                    <h3 className="font-semibold text-sm">{column.title}</h3>
                    {column.subtitle && (
                      <p className="text-xs text-muted-foreground">{column.subtitle}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-1">
                  {column.headerActions}
                  
                  {column.collapsible && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleColumn(column.id)}
                      className="h-6 w-6 p-0"
                      aria-label={`Toggle ${column.title} column`}
                    >
                      <EyeOff className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
              
              {/* Column Content */}
              <div className={cn(
                "flex-1",
                column.scrollable ? "overflow-hidden" : "overflow-visible"
              )}>
                {column.scrollable ? (
                  <ScrollArea className="h-full">
                    <div className="p-4">
                      {children[layout.columns.indexOf(column)]}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="p-4 h-full">
                    {children[layout.columns.indexOf(column)]}
                  </div>
                )}
              </div>
              
              {/* Resize Handle */}
              {column.resizable && index < visibleColumns.length - 1 && (
                <div
                  className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-primary/20 transition-colors"
                  onMouseDown={(e) => {
                    setIsResizing(true);
                    // Implement resize logic here
                  }}
                />
              )}
            </motion.div>
          );
        })}
      </div>
    );
  };

  return (
    <ColumnLayoutContext.Provider value={contextValue}>
      <div
        ref={containerRef}
        className={cn(
          "w-full h-full relative",
          className
        )}
        role="main"
        aria-label={ariaLabel}
      >
        {/* Layout Controls */}
        <div className="absolute top-2 right-2 z-10 flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => updateLayout({ responsive: !layout.responsive })}
            className="h-8"
          >
            {layout.responsive ? <Grid3X3 className="h-4 w-4" /> : <Columns className="h-4 w-4" />}
          </Button>
          
          {/* Column visibility toggles */}
          {layout.columns.map(column => (
            <Button
              key={column.id}
              variant={column.visible ? "default" : "outline"}
              size="sm"
              onClick={() => toggleColumn(column.id)}
              className="h-8 w-8 p-0"
              title={`Toggle ${column.title}`}
            >
              {column.visible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
            </Button>
          ))}
        </div>

        {/* Main Layout */}
        <AnimatePresence mode="wait">
          {isResponsive ? renderMobileLayout() : renderDesktopLayout()}
        </AnimatePresence>
      </div>
    </ColumnLayoutContext.Provider>
  );
};

// Predefined layout configurations
export const createAnalysisLayout = (): ColumnLayout => ({
  columns: [
    {
      id: 'input',
      title: 'Input & Questions',
      subtitle: 'Analysis inputs and context',
      width: 30,
      minWidth: 250,
      maxWidth: 400,
      visible: true,
      collapsible: true,
      resizable: true,
      scrollable: true,
    },
    {
      id: 'process',
      title: 'AI Processing',
      subtitle: 'Analysis workflow and reasoning',
      width: 40,
      minWidth: 300,
      maxWidth: 600,
      visible: true,
      collapsible: true,
      resizable: true,
      scrollable: true,
    },
    {
      id: 'output',
      title: 'Results & Insights',
      subtitle: 'Analysis results and follow-ups',
      width: 30,
      minWidth: 250,
      maxWidth: 500,
      visible: true,
      collapsible: true,
      resizable: true,
      scrollable: true,
    },
  ],
  gap: 16,
  responsive: true,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
  mobileLayout: 'tabs',
});

export const createCanvasLayout = (): ColumnLayout => ({
  columns: [
    {
      id: 'tools',
      title: 'Design Tools',
      subtitle: 'Canvas tools and properties',
      width: 20,
      minWidth: 200,
      maxWidth: 300,
      visible: true,
      collapsible: true,
      resizable: true,
      scrollable: true,
    },
    {
      id: 'canvas',
      title: 'Canvas Area',
      subtitle: 'Main design and visualization area',
      width: 60,
      minWidth: 400,
      maxWidth: 1200,
      visible: true,
      collapsible: false,
      resizable: true,
      scrollable: false,
    },
    {
      id: 'properties',
      title: 'Properties',
      subtitle: 'Element properties and settings',
      width: 20,
      minWidth: 200,
      maxWidth: 350,
      visible: true,
      collapsible: true,
      resizable: true,
      scrollable: true,
    },
  ],
  gap: 12,
  responsive: true,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
  mobileLayout: 'stack',
});

// Responsive utilities
export const useResponsiveLayout = (layout: ColumnLayout) => {
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = windowWidth < layout.breakpoints.mobile;
  const isTablet = windowWidth >= layout.breakpoints.mobile && windowWidth < layout.breakpoints.tablet;
  const isDesktop = windowWidth >= layout.breakpoints.desktop;

  return { windowWidth, isMobile, isTablet, isDesktop };
};

// Accessibility helpers
export const getColumnAriaProps = (column: ColumnConfig, index: number, total: number) => ({
  role: 'region',
  'aria-label': `${column.title} column, ${index + 1} of ${total}`,
  'aria-describedby': column.subtitle ? `${column.id}-subtitle` : undefined,
  tabIndex: -1,
});

export const getLayoutKeyboardShortcuts = () => [
  { key: 'Ctrl+1', description: 'Focus first column' },
  { key: 'Ctrl+2', description: 'Focus second column' },
  { key: 'Ctrl+3', description: 'Focus third column' },
  { key: 'Ctrl+H', description: 'Toggle first column visibility' },
];

export default Unified3ColumnLayout;
