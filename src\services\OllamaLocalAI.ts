/**
 * Ollama Local AI Integration Service
 * Provides local AI model support with fallback capabilities
 */

export interface OllamaModel {
  name: string;
  size: string;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  modified_at: string;
}

export interface OllamaGenerateRequest {
  model: string;
  prompt: string;
  system?: string;
  template?: string;
  context?: number[];
  stream?: boolean;
  raw?: boolean;
  format?: 'json';
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    seed?: number;
    num_predict?: number;
    stop?: string[];
  };
}

export interface OllamaGenerateResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface OllamaEmbeddingRequest {
  model: string;
  prompt: string;
}

export interface OllamaEmbeddingResponse {
  embedding: number[];
}

export interface OllamaStatus {
  isAvailable: boolean;
  version?: string;
  models: OllamaModel[];
  error?: string;
}

export class OllamaLocalAI {
  private baseUrl: string;
  private timeout: number;
  private retryCount: number;
  private isConnected: boolean = false;
  private availableModels: OllamaModel[] = [];

  constructor(
    baseUrl: string = 'http://localhost:11434',
    timeout: number = 30000,
    retryCount: number = 3
  ) {
    this.baseUrl = baseUrl;
    this.timeout = timeout;
    this.retryCount = retryCount;
  }

  /**
   * Check if Ollama is available and get status
   */
  async checkStatus(): Promise<OllamaStatus> {
    try {
      const response = await this.makeRequest('/api/tags', 'GET');
      const data = await response.json();
      
      this.isConnected = true;
      this.availableModels = data.models || [];
      
      return {
        isAvailable: true,
        models: this.availableModels,
      };
    } catch (error) {
      this.isConnected = false;
      return {
        isAvailable: false,
        models: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get list of available models
   */
  async getModels(): Promise<OllamaModel[]> {
    try {
      const response = await this.makeRequest('/api/tags', 'GET');
      const data = await response.json();
      this.availableModels = data.models || [];
      return this.availableModels;
    } catch (error) {
      console.error('Failed to get models:', error);
      return [];
    }
  }

  /**
   * Pull/download a model
   */
  async pullModel(modelName: string, onProgress?: (progress: string) => void): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/pull`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: modelName, stream: true }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.status) {
                onProgress?.(data.status);
              }
              if (data.error) {
                throw new Error(data.error);
              }
            } catch (parseError) {
              // Ignore JSON parse errors for individual lines
            }
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Failed to pull model:', error);
      return false;
    }
  }

  /**
   * Generate text completion
   */
  async generate(request: OllamaGenerateRequest): Promise<OllamaGenerateResponse> {
    if (!this.isConnected) {
      await this.checkStatus();
      if (!this.isConnected) {
        throw new Error('Ollama is not available');
      }
    }

    try {
      const response = await this.makeRequest('/api/generate', 'POST', request);
      
      if (request.stream) {
        // Handle streaming response
        return this.handleStreamingResponse(response);
      } else {
        return await response.json();
      }
    } catch (error) {
      console.error('Generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate embeddings
   */
  async generateEmbeddings(request: OllamaEmbeddingRequest): Promise<OllamaEmbeddingResponse> {
    if (!this.isConnected) {
      await this.checkStatus();
      if (!this.isConnected) {
        throw new Error('Ollama is not available');
      }
    }

    try {
      const response = await this.makeRequest('/api/embeddings', 'POST', request);
      return await response.json();
    } catch (error) {
      console.error('Embedding generation failed:', error);
      throw error;
    }
  }

  /**
   * Chat completion (conversation format)
   */
  async chat(
    model: string,
    messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
    options?: OllamaGenerateRequest['options']
  ): Promise<string> {
    // Convert chat format to prompt format
    let prompt = '';
    let systemMessage = '';

    for (const message of messages) {
      if (message.role === 'system') {
        systemMessage = message.content;
      } else if (message.role === 'user') {
        prompt += `Human: ${message.content}\n`;
      } else if (message.role === 'assistant') {
        prompt += `Assistant: ${message.content}\n`;
      }
    }

    prompt += 'Assistant: ';

    const request: OllamaGenerateRequest = {
      model,
      prompt,
      system: systemMessage || undefined,
      options,
      stream: false,
    };

    const response = await this.generate(request);
    return response.response;
  }

  /**
   * Get recommended models for different use cases
   */
  getRecommendedModels(): Record<string, string[]> {
    return {
      'conversation': ['llama2:7b', 'llama2:13b', 'mistral:7b', 'neural-chat:7b'],
      'coding': ['codellama:7b', 'codellama:13b', 'deepseek-coder:6.7b'],
      'analysis': ['llama2:13b', 'llama2:70b', 'mistral:7b'],
      'creative': ['llama2:7b', 'neural-chat:7b', 'vicuna:7b'],
      'fast': ['tinyllama:1.1b', 'phi:2.7b', 'gemma:2b'],
      'accurate': ['llama2:70b', 'mixtral:8x7b', 'qwen:14b'],
    };
  }

  /**
   * Auto-select best available model for a use case
   */
  async selectBestModel(useCase: string = 'conversation'): Promise<string | null> {
    const recommended = this.getRecommendedModels()[useCase] || this.getRecommendedModels()['conversation'];
    const available = await this.getModels();
    const availableNames = available.map(m => m.name);

    // Find the first recommended model that's available
    for (const modelName of recommended) {
      if (availableNames.includes(modelName)) {
        return modelName;
      }
    }

    // If no recommended models are available, return the first available model
    return availableNames.length > 0 ? availableNames[0] : null;
  }

  /**
   * Batch processing for multiple prompts
   */
  async batchGenerate(
    requests: OllamaGenerateRequest[],
    onProgress?: (completed: number, total: number) => void
  ): Promise<OllamaGenerateResponse[]> {
    const results: OllamaGenerateResponse[] = [];
    
    for (let i = 0; i < requests.length; i++) {
      try {
        const result = await this.generate(requests[i]);
        results.push(result);
        onProgress?.(i + 1, requests.length);
      } catch (error) {
        console.error(`Batch request ${i} failed:`, error);
        // Add error response
        results.push({
          model: requests[i].model,
          created_at: new Date().toISOString(),
          response: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          done: true,
        });
      }
    }

    return results;
  }

  /**
   * Model performance benchmarking
   */
  async benchmarkModel(modelName: string, testPrompts: string[] = [
    'What is the capital of France?',
    'Explain quantum computing in simple terms.',
    'Write a short poem about nature.',
  ]): Promise<{
    model: string;
    averageResponseTime: number;
    tokensPerSecond: number;
    results: Array<{
      prompt: string;
      response: string;
      duration: number;
      tokenCount: number;
    }>;
  }> {
    const results = [];
    let totalDuration = 0;
    let totalTokens = 0;

    for (const prompt of testPrompts) {
      const startTime = Date.now();
      
      try {
        const response = await this.generate({
          model: modelName,
          prompt,
          stream: false,
        });

        const duration = Date.now() - startTime;
        const tokenCount = response.eval_count || response.response.split(' ').length;

        results.push({
          prompt,
          response: response.response,
          duration,
          tokenCount,
        });

        totalDuration += duration;
        totalTokens += tokenCount;
      } catch (error) {
        results.push({
          prompt,
          response: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          duration: Date.now() - startTime,
          tokenCount: 0,
        });
      }
    }

    return {
      model: modelName,
      averageResponseTime: totalDuration / testPrompts.length,
      tokensPerSecond: totalTokens / (totalDuration / 1000),
      results,
    };
  }

  /**
   * Handle streaming response
   */
  private async handleStreamingResponse(response: Response): Promise<OllamaGenerateResponse> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';
    let lastData: Record<string, unknown> = {};

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim()) {
          try {
            const data = JSON.parse(line);
            fullResponse += data.response || '';
            lastData = data;
            
            if (data.done) {
              return {
                ...lastData,
                response: fullResponse,
              };
            }
          } catch (parseError) {
            // Ignore JSON parse errors for individual lines
          }
        }
      }
    }

    return {
      ...lastData,
      response: fullResponse,
      done: true,
    };
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest(
    endpoint: string, 
    method: 'GET' | 'POST' = 'GET', 
    body?: unknown
  ): Promise<Response> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.retryCount; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method,
          headers: method === 'POST' ? { 'Content-Type': 'application/json' } : {},
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < this.retryCount - 1) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    throw lastError || new Error('Request failed after retries');
  }

  /**
   * Get connection status
   */
  isAvailable(): boolean {
    return this.isConnected;
  }

  /**
   * Get base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Update configuration
   */
  updateConfig(config: {
    baseUrl?: string;
    timeout?: number;
    retryCount?: number;
  }): void {
    if (config.baseUrl) this.baseUrl = config.baseUrl;
    if (config.timeout) this.timeout = config.timeout;
    if (config.retryCount) this.retryCount = config.retryCount;
  }
}
