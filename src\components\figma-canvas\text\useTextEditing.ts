import { useState, useCallback, useRef, useEffect } from 'react';
import { Canvas, Textbox } from 'fabric';
import { TextObject, Point } from '@/types/figma';

interface TextEditingState {
  isEditing: boolean;
  editingObjectId: string | null;
  editorPosition: Point | null;
}

export const useTextEditing = (canvas: Canvas | null) => {
  const [editingState, setEditingState] = useState<TextEditingState>({
    isEditing: false,
    editingObjectId: null,
    editorPosition: null,
  });

  const { 
    objects, 
    updateObject, 
    getObjectById,
    activeTool,
    setActiveTool 
  } = useFigmaCanvasStore();

  const editingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Start editing a text object
  const startEditing = useCallback((objectId: string, position?: Point) => {
    const textObject = getObjectById(objectId) as TextObject;
    if (!textObject || textObject.type !== 'text') return;

    // Calculate editor position
    let editorPos = position;
    if (!editorPos && canvas) {
      const fabricObject = canvas.getObjects().find(obj => {
        return (typeof obj === 'object' && obj !== null && 'data' in obj && (obj as { data?: { id?: string } }).data?.id === objectId);
      });
      if (fabricObject) {
        const bound = fabricObject.getBoundingRect();
        editorPos = {
          x: bound.left + bound.width / 2,
          y: bound.top - 10, // Position above the text
        };
      }
    }

    setEditingState({
      isEditing: true,
      editingObjectId: objectId,
      editorPosition: editorPos || { x: 0, y: 0 },
    });

    // Switch to select tool if not already
    if (activeTool !== 'select') {
      setActiveTool('select');
    }
  }, [canvas, getObjectById, activeTool, setActiveTool]);

  // Stop editing
  const stopEditing = useCallback(() => {
    setEditingState({
      isEditing: false,
      editingObjectId: null,
      editorPosition: null,
    });

    if (editingTimeoutRef.current) {
      clearTimeout(editingTimeoutRef.current);
      editingTimeoutRef.current = null;
    }
  }, []);

  // Update text object
  const updateTextObject = useCallback((updates: Partial<TextObject>) => {
    if (!editingState.editingObjectId) return;

    updateObject(editingState.editingObjectId, updates);

    // Update the fabric object immediately for visual feedback
    if (canvas) {
      const fabricObject = canvas.getObjects().find(obj => {
        return (typeof obj === 'object' && obj !== null && 'data' in obj && (obj as { data?: { id?: string } }).data?.id === editingState.editingObjectId);
      }) as Textbox;

      if (fabricObject && fabricObject instanceof Textbox) {
        if (updates.content !== undefined) {
          fabricObject.text = updates.content;
        }
        
        if (updates.textStyle) {
          const style = updates.textStyle;
          if (style.fontFamily) fabricObject.fontFamily = style.fontFamily;
          if (style.fontSize) fabricObject.fontSize = style.fontSize;
          if (style.fontWeight) fabricObject.fontWeight = style.fontWeight;
          if (style.fontStyle) fabricObject.fontStyle = style.fontStyle;
          if (style.textAlign) fabricObject.textAlign = style.textAlign;
          if (style.textDecoration) {
            fabricObject.underline = style.textDecoration === 'underline';
            fabricObject.linethrough = style.textDecoration === 'line-through';
          }
          if (style.lineHeight) fabricObject.lineHeight = style.lineHeight;
          if (style.letterSpacing) fabricObject.charSpacing = style.letterSpacing * 1000;
        }

        if (updates.style?.fill) {
          fabricObject.fill = updates.style.fill;
        }

        canvas.renderAll();
      }
    }
  }, [editingState.editingObjectId, updateObject, canvas]);

  // Handle double-click on text objects
  const handleTextDoubleClick = useCallback((e: fabric.IEvent) => {
    if (!canvas) return;

    const target = e.target;
    if (target && typeof target === 'object' && 'data' in target && (target as { data?: { type?: string } }).data?.type === 'text') {
      const objectId = (target as { data?: { id?: string } }).data?.id;
      const pointer = canvas.getPointer(e.e);
      startEditing(objectId, pointer);
    }
  }, [canvas, startEditing]);

  // Handle canvas click to stop editing
  const handleCanvasClick = useCallback((e: fabric.IEvent) => {
    if (editingState.isEditing && !e.target) {
      // Delay stopping editing to allow for editor interactions
      editingTimeoutRef.current = setTimeout(() => {
        stopEditing();
      }, 100);
    }
  }, [editingState.isEditing, stopEditing]);

  // Handle escape key to stop editing
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && editingState.isEditing) {
      stopEditing();
    }
  }, [editingState.isEditing, stopEditing]);

  // Set up event listeners
  useEffect(() => {
    if (!canvas) return;

    canvas.on('mouse:dblclick', handleTextDoubleClick);
    canvas.on('mouse:down', handleCanvasClick);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      canvas.off('mouse:dblclick', handleTextDoubleClick);
      canvas.off('mouse:down', handleCanvasClick);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canvas, handleTextDoubleClick, handleCanvasClick, handleKeyDown]);

  // Auto-start editing when text tool creates a new text object
  useEffect(() => {
    if (activeTool === 'text') {
      // Listen for new text objects being created
      const unsubscribe = useFigmaCanvasStore.subscribe(
        (state) => state.objects,
        (objects, prevObjects) => {
          // Find newly created text objects
          const newObjectIds = Object.keys(objects).filter(
            id => !prevObjects[id] && objects[id].type === 'text'
          );

          if (newObjectIds.length > 0) {
            const newTextId = newObjectIds[newObjectIds.length - 1];
            // Start editing the new text object after a short delay
            setTimeout(() => {
              startEditing(newTextId);
            }, 100);
          }
        }
      );

      return unsubscribe;
    }
  }, [activeTool, startEditing]);

  // Create inline text editor for quick editing
  const createInlineEditor = useCallback((objectId: string, position: Point) => {
    const textObject = getObjectById(objectId) as TextObject;
    if (!textObject || textObject.type !== 'text') return null;

    const input = document.createElement('textarea');
    input.value = textObject.content;
    input.style.position = 'absolute';
    input.style.left = `${position.x}px`;
    input.style.top = `${position.y}px`;
    input.style.zIndex = '1000';
    input.style.border = '2px solid #007AFF';
    input.style.borderRadius = '4px';
    input.style.padding = '4px';
    input.style.fontFamily = textObject.textStyle.fontFamily;
    input.style.fontSize = `${textObject.textStyle.fontSize}px`;
    input.style.fontWeight = textObject.textStyle.fontWeight;
    input.style.fontStyle = textObject.textStyle.fontStyle;
    input.style.textAlign = textObject.textStyle.textAlign;
    input.style.lineHeight = textObject.textStyle.lineHeight.toString();
    input.style.letterSpacing = `${textObject.textStyle.letterSpacing}px`;
    input.style.color = textObject.style.fill || '#000000';
    input.style.background = 'white';
    input.style.resize = 'none';
    input.style.outline = 'none';
    input.style.minWidth = '100px';
    input.style.minHeight = '30px';

    // Auto-resize
    const autoResize = () => {
      input.style.height = 'auto';
      input.style.height = `${input.scrollHeight}px`;
      input.style.width = 'auto';
      const textWidth = input.value.length * textObject.textStyle.fontSize * 0.6;
      input.style.width = `${Math.max(100, textWidth)}px`;
    };

    input.addEventListener('input', autoResize);
    autoResize();

    // Handle input changes
    input.addEventListener('input', () => {
      updateTextObject({ content: input.value });
    });

    // Handle completion
    const finishEditing = () => {
      updateTextObject({ content: input.value });
      input.remove();
      stopEditing();
    };

    input.addEventListener('blur', finishEditing);
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        finishEditing();
      } else if (e.key === 'Escape') {
        input.remove();
        stopEditing();
      }
    });

    // Add to DOM and focus
    document.body.appendChild(input);
    input.focus();
    input.select();

    return input;
  }, [getObjectById, updateTextObject, stopEditing]);

  // Get the currently editing text object
  const getEditingTextObject = useCallback(() => {
    if (!editingState.editingObjectId) return null;
    return getObjectById(editingState.editingObjectId) as TextObject;
  }, [editingState.editingObjectId, getObjectById]);

  return {
    editingState,
    startEditing,
    stopEditing,
    updateTextObject,
    createInlineEditor,
    getEditingTextObject,
  };
};
