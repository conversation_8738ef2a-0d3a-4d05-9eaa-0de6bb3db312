import { useEffect, useRef, useCallback } from "react";
import { useCanvasStore } from "@/stores/useCanvasStore";
import { useCanvasFilters } from "./useCanvasFilters";
import { useCanvasInteractions } from "./useCanvasInteractions";
import { shallow } from 'zustand/shallow';

export const useCanvasState = ({ snapToGrid = true }: { snapToGrid?: boolean } = {}) => {
  const allNotes = useCanvasStore(state => state.notes);
  const nodePositions = useCanvasStore(state => state.nodePositions);
  const connections = useCanvasStore(state => state.connections);
  const setNodePositions = useCanvasStore(state => state.setNodePositions);
  const setConnections = useCanvasStore(state => state.setConnections);
  const deleteNoteFromStore = useCanvasStore(state => state.deleteNote);
  const connectingNodeId = useCanvasStore(state => state.connectingNodeId);
  const setConnectingNodeId = useCanvasStore(state => state.setConnectingNodeId);

  const {
    notes,
    connections: filteredConnections,
    searchQuery,
    setSearchQuery,
    analysisTypeFilter,
    setAnalysisTypeFilter,
    tagFilter,
    setTagFilter,
    allTags,
  } = useCanvasFilters({ allNotes, connections });

  const interactions = useCanvasInteractions({
    nodePositions,
    setNodePositions,
    connections, // Pass original connections for interaction logic
    setConnections,
    deleteNoteFromStore,
    connectingNodeId,
    setConnectingNodeId,
    snapToGrid,
  });

  // Use a ref to track which notes have been processed for positioning
  const processedNoteIds = useRef(new Set<string>());

  // Memoize the position initialization function to prevent recreation
  const initializePositions = useCallback(() => {
    setNodePositions(currentPositions => {
      const positionIds = new Set(currentPositions.map(p => p.id));
      
      const notesNeedingPositions = allNotes.filter(note => 
        !positionIds.has(note.id) && !processedNoteIds.current.has(note.id)
      );

      if (notesNeedingPositions.length > 0) {
        console.log('Adding positions for new notes:', notesNeedingPositions.map(n => n.id));
        
        notesNeedingPositions.forEach(note => {
          processedNoteIds.current.add(note.id);
        });

        const newPositions = notesNeedingPositions.map((note, index) => ({
          id: note.id,
          x: 100 + ((currentPositions.length + index) % 5) * 270,
          y: 100 + Math.floor((currentPositions.length + index) / 5) * 220,
        }));
        
        return [...currentPositions, ...newPositions];
      }
      
      return currentPositions; // Return same array if no changes
    });

    // Clean up processed IDs for notes that no longer exist
    const currentNoteIds = new Set(allNotes.map(n => n.id));
    for (const noteId of processedNoteIds.current) {
      if (!currentNoteIds.has(noteId)) {
        processedNoteIds.current.delete(noteId);
      }
    }
  }, [allNotes, setNodePositions]);

  useEffect(() => {
    initializePositions();
  }, [initializePositions]);

  return {
    // From filter hook
    notes,
    connections: filteredConnections, // Return filtered connections for rendering
    searchQuery,
    setSearchQuery,
    analysisTypeFilter,
    setAnalysisTypeFilter,
    tagFilter,
    setTagFilter,
    allTags,
    allNotesCount: allNotes.length,

    // From interaction hook
    ...interactions,
    connectingNodeId,
  };
};
