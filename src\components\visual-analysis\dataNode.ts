import * as THREE from 'three';

export interface DataNodeOptions {
  id: string;
  position?: THREE.Vector2; // Changed to Vector2 for 2D
  size?: number; // Generic size, could be radius for a circle
  color?: THREE.ColorRepresentation;
  texture?: THREE.Texture; // For sprite-based nodes
  label?: string; // Optional label for the node
  data?: unknown; // Original raw data associated with the node
}

// DataNode is now a simpler 2D visual representation, not a physics object.
export class DataNode {
  public id: string;
  public mesh: THREE.Mesh | THREE.Sprite;
  public position: THREE.Vector2; // Current 2D position
  private originalColor: THREE.ColorRepresentation;
  public data?: unknown;

  constructor(options: DataNodeOptions) {
    this.id = options.id;
    this.position = options.position || new THREE.Vector2(0, 0);
    this.originalColor = options.color || new THREE.Color(0xffffff);
    this.data = options.data;

    if (options.texture) {
      const material = new THREE.SpriteMaterial({ map: options.texture, color: this.originalColor });
      this.mesh = new THREE.Sprite(material);
      const scale = options.size || 1;
      this.mesh.scale.set(scale, scale, 1);
    } else {
      const geometry = new THREE.CircleGeometry(options.size || 0.5, 32);
      const material = new THREE.MeshBasicMaterial({ color: this.originalColor });
      this.mesh = new THREE.Mesh(geometry, material);
    }

    this.mesh.position.set(this.position.x, this.position.y, 0); // Z is 0 for 2D plane
    this.mesh.userData.dataNode = this; // Link back to this object for raycasting
    this.mesh.userData.nodeId = this.id; // Store ID directly for easier access
  }

  public setHighlight(highlight: boolean): void {
    const material = this.mesh.material as THREE.MeshBasicMaterial | THREE.SpriteMaterial;
    if (highlight) {
      material.color.set(0xffff00); // Highlight color (e.g., yellow)
    } else {
      material.color.set(this.originalColor);
    }
  }

  // Update mesh position if internal position changes (e.g., due to layout algorithm)
  public updateMeshPosition(): void {
    this.mesh.position.set(this.position.x, this.position.y, 0);
  }

  public dispose(): void {
    if (this.mesh) {
      if ((this.mesh as THREE.Mesh).geometry) {
        (this.mesh as THREE.Mesh).geometry.dispose();
      }
      if (Array.isArray(this.mesh.material)) {
        this.mesh.material.forEach(m => m.dispose());
      } else {
        this.mesh.material.dispose();
      }
      // Texture disposal if it was created and managed by this class
      const spriteMaterial = this.mesh.material as THREE.SpriteMaterial;
      if (spriteMaterial.map && spriteMaterial.map instanceof THREE.Texture) {
         spriteMaterial.map.dispose();
      }
    }
  }
}

// Static properties like materials or contact materials are no longer needed here
// as physics is removed.
