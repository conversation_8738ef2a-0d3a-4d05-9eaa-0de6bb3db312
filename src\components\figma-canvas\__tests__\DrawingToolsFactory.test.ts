import { describe, it, expect } from 'vitest';
import { DrawingToolsFactory } from '../tools/DrawingToolsFactory';

describe('DrawingToolsFactory', () => {
  const testLayerId = 'test-layer';

  describe('createRectangle', () => {
    it('should create a rectangle with correct dimensions', () => {
      const startPoint = { x: 10, y: 20 };
      const endPoint = { x: 110, y: 120 };
      
      const rectangle = DrawingToolsFactory.createRectangle(startPoint, endPoint, testLayerId);
      
      expect(rectangle.type).toBe('rectangle');
      expect(rectangle.transform.x).toBe(10);
      expect(rectangle.transform.y).toBe(20);
      expect(rectangle.transform.width).toBe(100);
      expect(rectangle.transform.height).toBe(100);
      expect(rectangle.layerId).toBe(testLayerId);
    });

    it('should handle negative dimensions', () => {
      const startPoint = { x: 110, y: 120 };
      const endPoint = { x: 10, y: 20 };
      
      const rectangle = DrawingToolsFactory.createRectangle(startPoint, endPoint, testLayerId);
      
      expect(rectangle.transform.x).toBe(10); // Min x
      expect(rectangle.transform.y).toBe(20); // Min y
      expect(rectangle.transform.width).toBe(100);
      expect(rectangle.transform.height).toBe(100);
    });

    it('should apply custom styles', () => {
      const startPoint = { x: 0, y: 0 };
      const endPoint = { x: 100, y: 100 };
      const customStyle = { fill: '#ff0000', strokeWidth: 5 };
      
      const rectangle = DrawingToolsFactory.createRectangle(startPoint, endPoint, testLayerId, customStyle);
      
      expect(rectangle.style.fill).toBe('#ff0000');
      expect(rectangle.style.strokeWidth).toBe(5);
    });
  });

  describe('createCircle', () => {
    it('should create a circle with correct radius', () => {
      const center = { x: 50, y: 50 };
      const radius = 25;
      
      const circle = DrawingToolsFactory.createCircle(center, radius, testLayerId);
      
      expect(circle.type).toBe('circle');
      expect(circle.transform.x).toBe(25); // center.x - radius
      expect(circle.transform.y).toBe(25); // center.y - radius
      expect(circle.transform.width).toBe(50); // radius * 2
      expect(circle.transform.height).toBe(50); // radius * 2
    });

    it('should apply custom styles', () => {
      const center = { x: 50, y: 50 };
      const radius = 25;
      const customStyle = { fill: '#00ff00', opacity: 0.5 };
      
      const circle = DrawingToolsFactory.createCircle(center, radius, testLayerId, customStyle);
      
      expect(circle.style.fill).toBe('#00ff00');
      expect(circle.style.opacity).toBe(0.5);
    });
  });

  describe('createLine', () => {
    it('should create a line with correct points', () => {
      const points = [
        { x: 0, y: 0 },
        { x: 100, y: 50 },
        { x: 200, y: 100 }
      ];
      
      const line = DrawingToolsFactory.createLine(points, testLayerId);
      
      expect(line.type).toBe('line');
      expect(line.transform.x).toBe(0); // Min x
      expect(line.transform.y).toBe(0); // Min y
      expect(line.transform.width).toBe(200); // Max x - Min x
      expect(line.transform.height).toBe(100); // Max y - Min y
      expect(line.points).toHaveLength(3);
    });

    it('should normalize points relative to bounding box', () => {
      const points = [
        { x: 10, y: 20 },
        { x: 110, y: 70 }
      ];
      
      const line = DrawingToolsFactory.createLine(points, testLayerId);
      
      expect(line.points[0]).toEqual({ x: 0, y: 0 }); // Relative to min point
      expect(line.points[1]).toEqual({ x: 100, y: 50 }); // Relative to min point
    });
  });

  describe('createArrow', () => {
    it('should create an arrow with correct dimensions', () => {
      const startPoint = { x: 10, y: 20 };
      const endPoint = { x: 110, y: 70 };
      
      const arrow = DrawingToolsFactory.createArrow(startPoint, endPoint, testLayerId);
      
      expect(arrow.type).toBe('arrow');
      expect(arrow.transform.x).toBe(10);
      expect(arrow.transform.y).toBe(20);
      expect(arrow.transform.width).toBe(100);
      expect(arrow.transform.height).toBe(50);
      expect(arrow.arrowHeadSize).toBe(10);
    });

    it('should handle minimum dimensions', () => {
      const startPoint = { x: 0, y: 0 };
      const endPoint = { x: 5, y: 5 };
      
      const arrow = DrawingToolsFactory.createArrow(startPoint, endPoint, testLayerId);
      
      expect(arrow.transform.width).toBe(20); // Minimum width
      expect(arrow.transform.height).toBe(20); // Minimum height
    });
  });

  describe('createText', () => {
    it('should create a text object with correct properties', () => {
      const position = { x: 50, y: 100 };
      const content = 'Hello World';
      
      const text = DrawingToolsFactory.createText(position, content, testLayerId);
      
      expect(text.type).toBe('text');
      expect(text.transform.x).toBe(50);
      expect(text.transform.y).toBe(100);
      expect(text.content).toBe('Hello World');
      expect(text.textStyle.fontFamily).toBe('Inter, sans-serif');
      expect(text.textStyle.fontSize).toBe(16);
      expect(text.autoResize).toBe(true);
    });

    it('should apply custom styles', () => {
      const position = { x: 0, y: 0 };
      const content = 'Test';
      const customStyle = { fill: '#0000ff' };
      
      const text = DrawingToolsFactory.createText(position, content, testLayerId, customStyle);
      
      expect(text.style.fill).toBe('#0000ff');
    });
  });

  describe('createPen', () => {
    it('should create a pen object with correct points', () => {
      const points = [
        { x: 0, y: 0 },
        { x: 10, y: 5 },
        { x: 20, y: 10 }
      ];
      
      const pen = DrawingToolsFactory.createPen(points, testLayerId);
      
      expect(pen.type).toBe('pen');
      expect(pen.transform.x).toBe(0);
      expect(pen.transform.y).toBe(0);
      expect(pen.transform.width).toBe(20);
      expect(pen.transform.height).toBe(10);
      expect(pen.points).toHaveLength(3);
      expect(pen.smoothing).toBe(0.5);
    });

    it('should throw error for empty points', () => {
      expect(() => {
        DrawingToolsFactory.createPen([], testLayerId);
      }).toThrow('Pen tool requires at least one point');
    });

    it('should handle single point', () => {
      const points = [{ x: 50, y: 50 }];
      
      const pen = DrawingToolsFactory.createPen(points, testLayerId);
      
      expect(pen.transform.width).toBe(1); // Minimum width
      expect(pen.transform.height).toBe(1); // Minimum height
    });
  });

  describe('createPolygon', () => {
    it('should create a polygon with correct number of sides', () => {
      const center = { x: 50, y: 50 };
      const radius = 30;
      const sides = 6;
      
      const polygon = DrawingToolsFactory.createPolygon(center, radius, sides, testLayerId);
      
      expect(polygon.type).toBe('polygon');
      expect(polygon.sides).toBe(6);
      expect(polygon.points).toHaveLength(6);
      expect(polygon.transform.x).toBe(20); // center.x - radius
      expect(polygon.transform.y).toBe(20); // center.y - radius
      expect(polygon.transform.width).toBe(60); // radius * 2
      expect(polygon.transform.height).toBe(60); // radius * 2
    });

    it('should generate correct points for triangle', () => {
      const center = { x: 0, y: 0 };
      const radius = 10;
      const sides = 3;
      
      const polygon = DrawingToolsFactory.createPolygon(center, radius, sides, testLayerId);
      
      expect(polygon.points).toHaveLength(3);
      // First point should be at top (angle -90 degrees)
      expect(polygon.points[0].x).toBeCloseTo(10); // radius
      expect(polygon.points[0].y).toBeCloseTo(0); // -radius + radius
    });
  });

  describe('createStar', () => {
    it('should create a star with correct properties', () => {
      const center = { x: 50, y: 50 };
      const outerRadius = 30;
      const innerRadius = 15;
      const points = 5;
      
      const star = DrawingToolsFactory.createStar(center, outerRadius, innerRadius, points, testLayerId);
      
      expect(star.type).toBe('star');
      expect(star.points).toBe(5);
      expect(star.outerRadius).toBe(30);
      expect(star.innerRadius).toBe(15);
      expect(star.transform.x).toBe(20); // center.x - outerRadius
      expect(star.transform.y).toBe(20); // center.y - outerRadius
    });
  });

  describe('Utility Functions', () => {
    describe('getDefaultStyle', () => {
      it('should return correct default styles for each tool', () => {
        const rectangleStyle = DrawingToolsFactory.getDefaultStyle('rectangle');
        expect(rectangleStyle.fill).toBe('#3b82f6');
        expect(rectangleStyle.stroke).toBe('#1e40af');

        const circleStyle = DrawingToolsFactory.getDefaultStyle('circle');
        expect(circleStyle.fill).toBe('#10b981');

        const lineStyle = DrawingToolsFactory.getDefaultStyle('line');
        expect(lineStyle.stroke).toBe('#ef4444');
        expect(lineStyle.strokeWidth).toBe(3);
      });
    });

    describe('snapToGrid', () => {
      it('should snap point to grid when enabled', () => {
        const point = { x: 23, y: 37 };
        const gridSize = 20;
        
        const snapped = DrawingToolsFactory.snapToGrid(point, gridSize, true);
        
        expect(snapped.x).toBe(20); // Nearest grid point
        expect(snapped.y).toBe(40); // Nearest grid point
      });

      it('should not snap when disabled', () => {
        const point = { x: 23, y: 37 };
        const gridSize = 20;
        
        const snapped = DrawingToolsFactory.snapToGrid(point, gridSize, false);
        
        expect(snapped.x).toBe(23); // Original point
        expect(snapped.y).toBe(37); // Original point
      });
    });

    describe('calculateDistance', () => {
      it('should calculate correct distance between points', () => {
        const point1 = { x: 0, y: 0 };
        const point2 = { x: 3, y: 4 };
        
        const distance = DrawingToolsFactory.calculateDistance(point1, point2);
        
        expect(distance).toBe(5); // 3-4-5 triangle
      });
    });

    describe('calculateAngle', () => {
      it('should calculate correct angle between points', () => {
        const point1 = { x: 0, y: 0 };
        const point2 = { x: 1, y: 0 };
        
        const angle = DrawingToolsFactory.calculateAngle(point1, point2);
        
        expect(angle).toBe(0); // Horizontal line to the right
      });

      it('should calculate correct angle for vertical line', () => {
        const point1 = { x: 0, y: 0 };
        const point2 = { x: 0, y: 1 };
        
        const angle = DrawingToolsFactory.calculateAngle(point1, point2);
        
        expect(angle).toBeCloseTo(Math.PI / 2); // 90 degrees
      });
    });

    describe('rotatePoint', () => {
      it('should rotate point around center', () => {
        const point = { x: 1, y: 0 };
        const center = { x: 0, y: 0 };
        const angle = Math.PI / 2; // 90 degrees
        
        const rotated = DrawingToolsFactory.rotatePoint(point, center, angle);
        
        expect(rotated.x).toBeCloseTo(0);
        expect(rotated.y).toBeCloseTo(1);
      });
    });
  });
});
// If DrawingToolsFactory ever uses theme, import render from '@/utils/test-utils';
