import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  Sparkles, 
  Network, 
  Eye, 
  EyeOff, 
  Zap,
  RotateCcw,
  Lightbulb,
  Download
} from "lucide-react";

interface AICanvasControlsProps {
  isAIEnabled: boolean;
  showSuggestions: boolean;
  showClusters: boolean;
  showPatterns: boolean;
  showInsights: boolean;
  showExport: boolean;
  isAnalyzing: boolean;
  onToggleSuggestions: () => void;
  onToggleClusters: () => void;
  onTogglePatterns: () => void;
  onToggleInsights: () => void;
  onToggleExport: () => void;
  onRunAnalysis: () => void;
  onClearAIData: () => void;
}

export const AICanvasControls: React.FC<AICanvasControlsProps> = ({
  isAIEnabled,
  showSuggestions,
  showClusters,
  showPatterns,
  showInsights,
  showExport,
  isAnalyzing,
  onToggleSuggestions,
  onToggleClusters,
  onTogglePatterns,
  onToggleInsights,
  onToggleExport,
  onRunAnalysis,
  onClearAIData
}) => {
  if (!isAIEnabled) {
    return (
      <div className="flex items-center gap-2 p-2 bg-amber-50 border border-amber-200 rounded-lg">
        <Brain className="h-4 w-4 text-amber-600" />
        <span className="text-sm text-amber-700">AI features require API configuration</span>
      </div>
    );
  }

  const inactiveButtonClass = "bg-red-50 text-red-600 hover:bg-red-100 border-red-200";

  return (
    <div id="ai-canvas-controls" className="flex items-center gap-2 p-2 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center gap-2">
        <Brain className="h-4 w-4 text-purple-600" />
        <span className="text-sm font-medium text-gray-700">AI Canvas</span>
      </div>
      
      <div className="h-4 w-px bg-gray-300" />
      
      <Button
        size="sm"
        variant={showSuggestions ? "default" : "outline"}
        onClick={onToggleSuggestions}
        className={`text-xs ${!showSuggestions ? inactiveButtonClass : ''}`}
        title="Toggle AI suggestions"
      >
        <Sparkles className="h-3 w-3 mr-1" />
        Suggestions
      </Button>
      
      <Button
        size="sm"
        variant={showClusters ? "default" : "outline"}
        onClick={onToggleClusters}
        className={`text-xs ${!showClusters ? inactiveButtonClass : ''}`}
        title="Toggle clusters view"
      >
        <Network className="h-3 w-3 mr-1" />
        Clusters
      </Button>
      
      <Button
        size="sm"
        variant={showPatterns ? "default" : "outline"}
        onClick={onTogglePatterns}
        className={`text-xs ${!showPatterns ? inactiveButtonClass : ''}`}
        title="Toggle pattern highlights"
      >
        {showPatterns ? <Eye className="h-3 w-3 mr-1" /> : <EyeOff className="h-3 w-3 mr-1" />}
        Patterns
      </Button>

      <Button
        size="sm"
        variant={showInsights ? "default" : "outline"}
        onClick={onToggleInsights}
        className={`text-xs ${!showInsights ? inactiveButtonClass : ''}`}
        title="Toggle insights overlay"
      >
        <Lightbulb className="h-3 w-3 mr-1" />
        Insights
      </Button>
      
      <div className="h-4 w-px bg-gray-300" />
      
      <Button
        size="sm"
        onClick={onRunAnalysis}
        disabled={isAnalyzing}
        className="text-xs"
        title="Run AI analysis"
      >
        <Zap className={`h-3 w-3 mr-1 ${isAnalyzing ? 'animate-spin' : ''}`} />
        {isAnalyzing ? 'Analyzing...' : 'Analyze'}
      </Button>

      <Button
        size="sm"
        variant={showExport ? "default" : "outline"}
        onClick={onToggleExport}
        className={`text-xs ${!showExport ? inactiveButtonClass : ''}`}
        title="Export canvas data"
      >
        <Download className="h-3 w-3 mr-1" />
        Export
      </Button>
      
      <Button
        size="sm"
        variant="ghost"
        onClick={onClearAIData}
        className="text-xs"
        title="Clear all AI-generated data"
      >
        <RotateCcw className="h-3 w-3 mr-1" />
        Clear AI
      </Button>
    </div>
  );
};
