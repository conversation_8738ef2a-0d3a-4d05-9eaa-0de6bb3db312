/**
 * Local AI Service
 * 
 * Provides offline AI capabilities using local models and processing.
 * Falls back to cached responses and rule-based analysis when no local AI is available.
 */

import { AnalysisResult, ConversationStyle } from '@/types/conversation';
import { CharacterPersona, QuestionContext } from '@/types/analysis';

interface LocalAIConfig {
  enableLocalModels: boolean;
  modelPath?: string;
  fallbackToRules: boolean;
  cacheResponses: boolean;
  maxCacheSize: number;
}

interface CachedResponse {
  id: string;
  question: string;
  style: ConversationStyle;
  analysisType: string;
  response: AnalysisResult;
  timestamp: number;
  useCount: number;
}

export class LocalAIService {
  private config: LocalAIConfig;
  private responseCache: Map<string, CachedResponse> = new Map();
  private isInitialized = false;
  private localModel: unknown = null;

  constructor(config: Partial<LocalAIConfig> = {}) {
    this.config = {
      enableLocalModels: false, // Disabled by default until local models are set up
      fallbackToRules: true,
      cacheResponses: true,
      maxCacheSize: 1000,
      ...config
    };

    this.initializeService();
  }

  private async initializeService() {
    try {
      // Load cached responses from localStorage
      this.loadCachedResponses();

      // Try to initialize local AI model if enabled
      if (this.config.enableLocalModels) {
        await this.initializeLocalModel();
      }

      this.isInitialized = true;
    } catch (error) {
      console.warn('Local AI Service initialization failed:', error);
      this.isInitialized = true; // Continue with fallback methods
    }
  }

  private async initializeLocalModel() {
    // This would integrate with local AI models like:
    // - WebLLM for browser-based models
    // - Ollama integration for desktop
    // - TensorFlow.js models
    // For now, we'll use a placeholder
    console.log('Local AI model initialization would happen here');
  }

  private loadCachedResponses() {
    try {
      const cached = localStorage.getItem('localAI_responseCache');
      if (cached) {
        const responses: CachedResponse[] = JSON.parse(cached);
        responses.forEach(response => {
          this.responseCache.set(this.getCacheKey(response.question, response.style, response.analysisType), response);
        });
      }
    } catch (error) {
      console.warn('Failed to load cached responses:', error);
    }
  }

  private saveCachedResponses() {
    try {
      const responses = Array.from(this.responseCache.values());
      localStorage.setItem('localAI_responseCache', JSON.stringify(responses));
    } catch (error) {
      console.warn('Failed to save cached responses:', error);
    }
  }

  private getCacheKey(question: string, style: ConversationStyle, analysisType: string): string {
    return `${question.toLowerCase().trim()}_${style}_${analysisType}`;
  }

  async analyzeQuestion(
    question: string,
    style: ConversationStyle,
    model: string,
    numberOfAnswers: number,
    analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles' = 'multiple',
    characterPersona?: CharacterPersona,
    seedContext?: string,
    questionContext?: QuestionContext,
    selectedEmotions?: string[]
  ): Promise<AnalysisResult> {
    if (!this.isInitialized) {
      await this.initializeService();
    }

    // Check cache first
    const cacheKey = this.getCacheKey(question, style, analysisType);
    const cached = this.responseCache.get(cacheKey);
    
    if (cached && this.config.cacheResponses) {
      cached.useCount++;
      this.saveCachedResponses();
      return { ...cached.response, id: Date.now().toString() + Math.random().toString(36).substring(2, 9) };
    }

    let result: AnalysisResult;

    // Try local AI model first
    if (this.config.enableLocalModels && this.localModel) {
      try {
        result = await this.processWithLocalModel(question, style, analysisType, characterPersona, selectedEmotions);
      } catch (error) {
        console.warn('Local model processing failed, falling back to rules:', error);
        result = this.processWithRules(question, style, analysisType, characterPersona, selectedEmotions);
      }
    } else {
      // Fall back to rule-based processing
      result = this.processWithRules(question, style, analysisType, characterPersona, selectedEmotions);
    }

    // Cache the response
    if (this.config.cacheResponses) {
      this.cacheResponse(question, style, analysisType, result);
    }

    return result;
  }

  private async processWithLocalModel(
    question: string,
    style: ConversationStyle,
    analysisType: string,
    characterPersona?: CharacterPersona,
    selectedEmotions?: string[]
  ): Promise<AnalysisResult> {
    // This would use the local AI model
    // For now, return a placeholder that indicates local processing
    return this.processWithRules(question, style, analysisType, characterPersona, selectedEmotions);
  }

  private processWithRules(
    question: string,
    style: ConversationStyle,
    analysisType: string,
    characterPersona?: CharacterPersona,
    selectedEmotions?: string[]
  ): Promise<AnalysisResult> {
    return new Promise((resolve) => {
      // Simulate processing time
      setTimeout(() => {
        const result = this.generateRuleBasedAnalysis(question, style, analysisType, characterPersona, selectedEmotions);
        resolve(result);
      }, 1000 + Math.random() * 2000); // 1-3 second delay to simulate processing
    });
  }

  private generateRuleBasedAnalysis(
    question: string,
    style: ConversationStyle,
    analysisType: string,
    characterPersona?: CharacterPersona,
    selectedEmotions?: string[]
  ): AnalysisResult {
    const templates = this.getAnalysisTemplates(analysisType, style);
    const selectedTemplate = templates[Math.floor(Math.random() * templates.length)];
    
    const analysis = this.fillTemplate(selectedTemplate, question, characterPersona, selectedEmotions);
    const followUpQuestions = this.generateFollowUpQuestions(question, analysisType);

    return {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      question,
      style,
      model: 'Local Rule-Based Analysis',
      analysis,
      timestamp: new Date(),
      followUpQuestions,
      analysisType: analysisType as any,
      characterPersona,
      selectedEmotions,
      rating: Math.floor(Math.random() * 3) + 7, // 7-9 rating for offline analysis
    };
  }

  private getAnalysisTemplates(analysisType: string, style: ConversationStyle): string[] {
    const baseTemplates = {
      multiple: [
        "Based on the question '{question}', here are multiple perspectives to consider:\n\n1. **Practical Perspective**: {practical_analysis}\n\n2. **Strategic Perspective**: {strategic_analysis}\n\n3. **Creative Perspective**: {creative_analysis}\n\nEach perspective offers unique insights that can help inform your decision-making process.",
        "Analyzing '{question}' from different angles:\n\n**Immediate Considerations**: {immediate_thoughts}\n\n**Long-term Implications**: {longterm_thoughts}\n\n**Alternative Approaches**: {alternative_approaches}\n\nThis multi-faceted analysis provides a comprehensive view of the situation."
      ],
      deep: [
        "Deep analysis of '{question}':\n\n**Core Issue**: {core_issue}\n\n**Underlying Factors**: {underlying_factors}\n\n**Systemic Implications**: {systemic_implications}\n\n**Recommended Actions**: {recommended_actions}\n\nThis thorough examination reveals the complexity and interconnected nature of the topic.",
        "Comprehensive examination of '{question}':\n\n**Root Cause Analysis**: {root_causes}\n\n**Impact Assessment**: {impact_assessment}\n\n**Risk Evaluation**: {risk_evaluation}\n\n**Strategic Recommendations**: {strategic_recommendations}\n\nThis deep dive provides actionable insights for informed decision-making."
      ],
      'pros-cons': [
        "Pros and Cons analysis for '{question}':\n\n**Advantages**:\n• {pro_1}\n• {pro_2}\n• {pro_3}\n\n**Disadvantages**:\n• {con_1}\n• {con_2}\n• {con_3}\n\n**Conclusion**: {balanced_conclusion}",
        "Balanced evaluation of '{question}':\n\n**Positive Aspects**:\n- {positive_1}\n- {positive_2}\n- {positive_3}\n\n**Negative Aspects**:\n- {negative_1}\n- {negative_2}\n- {negative_3}\n\n**Overall Assessment**: {overall_assessment}"
      ]
    };

    return baseTemplates[analysisType as keyof typeof baseTemplates] || baseTemplates.multiple;
  }

  private fillTemplate(
    template: string,
    question: string,
    characterPersona?: CharacterPersona,
    selectedEmotions?: string[]
  ): string {
    const placeholders = {
      question,
      practical_analysis: "Focus on immediate, actionable steps and practical considerations.",
      strategic_analysis: "Consider long-term implications and strategic positioning.",
      creative_analysis: "Explore innovative approaches and unconventional solutions.",
      immediate_thoughts: "Initial considerations that require immediate attention.",
      longterm_thoughts: "Future implications and sustained impact over time.",
      alternative_approaches: "Different methods and perspectives to consider.",
      core_issue: "The fundamental challenge or opportunity at the heart of this question.",
      underlying_factors: "Hidden elements and contributing factors that influence the situation.",
      systemic_implications: "How this connects to broader systems and patterns.",
      recommended_actions: "Specific steps recommended based on this analysis.",
      pro_1: "Significant advantage or positive outcome",
      pro_2: "Additional benefit or opportunity",
      pro_3: "Further positive consideration",
      con_1: "Primary concern or potential drawback",
      con_2: "Secondary risk or limitation",
      con_3: "Additional challenge to consider",
      balanced_conclusion: "A balanced perspective considering both sides",
    };

    let filledTemplate = template;
    Object.entries(placeholders).forEach(([key, value]) => {
      filledTemplate = filledTemplate.replace(new RegExp(`{${key}}`, 'g'), value);
    });

    return filledTemplate;
  }

  private generateFollowUpQuestions(question: string, analysisType: string): string[] {
    const questionStarters = [
      "What would happen if",
      "How might we",
      "What are the implications of",
      "What alternatives exist for",
      "How does this relate to",
      "What would be the impact of",
      "How can we improve",
      "What are the risks of",
    ];

    const followUps: string[] = [];
    const numQuestions = Math.floor(Math.random() * 3) + 2; // 2-4 questions

    for (let i = 0; i < numQuestions; i++) {
      const starter = questionStarters[Math.floor(Math.random() * questionStarters.length)];
      followUps.push(`${starter} ${question.toLowerCase().replace('?', '')}?`);
    }

    return followUps;
  }

  private cacheResponse(
    question: string,
    style: ConversationStyle,
    analysisType: string,
    response: AnalysisResult
  ) {
    const cacheKey = this.getCacheKey(question, style, analysisType);
    
    const cached: CachedResponse = {
      id: cacheKey,
      question,
      style,
      analysisType,
      response,
      timestamp: Date.now(),
      useCount: 1,
    };

    this.responseCache.set(cacheKey, cached);

    // Manage cache size
    if (this.responseCache.size > this.config.maxCacheSize) {
      this.evictOldestCacheEntries();
    }

    this.saveCachedResponses();
  }

  private evictOldestCacheEntries() {
    const entries = Array.from(this.responseCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const toRemove = entries.slice(0, Math.floor(this.config.maxCacheSize * 0.1)); // Remove 10%
    toRemove.forEach(([key]) => {
      this.responseCache.delete(key);
    });
  }

  // Public methods for configuration
  updateConfig(newConfig: Partial<LocalAIConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  clearCache() {
    this.responseCache.clear();
    localStorage.removeItem('localAI_responseCache');
  }

  getCacheStats() {
    return {
      size: this.responseCache.size,
      maxSize: this.config.maxCacheSize,
      totalUseCount: Array.from(this.responseCache.values()).reduce((sum, item) => sum + item.useCount, 0),
    };
  }

  isAvailable(): boolean {
    return this.isInitialized;
  }
}

// Singleton instance
export const localAIService = new LocalAIService();
