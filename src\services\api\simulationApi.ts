import { ChatSimulation, SimulationPrompt, SimulationResult } from '@/components/visual-analysis/types';

/**
 * API service for simulation CRUD operations
 */
export class SimulationApi {
  private static baseUrl = '/api/simulations';

  /**
   * Get all simulations
   */
  static async getAllSimulations(): Promise<ChatSimulation[]> {
    try {
      const response = await fetch(`${this.baseUrl}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch simulations: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching simulations:', error);
      // Return mock data for development
      return this.getMockSimulations();
    }
  }

  /**
   * Get simulation by ID
   */
  static async getSimulation(id: string): Promise<ChatSimulation | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch simulation: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching simulation:', error);
      return null;
    }
  }

  /**
   * Create new simulation
   */
  static async createSimulation(simulation: Omit<ChatSimulation, 'id' | 'createdAt' | 'updatedAt'>): Promise<ChatSimulation> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(simulation),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to create simulation: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating simulation:', error);
      // Return mock created simulation for development
      const mockSimulation: ChatSimulation = {
        ...simulation,
        id: `sim_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      return mockSimulation;
    }
  }

  /**
   * Update simulation
   */
  static async updateSimulation(id: string, updates: Partial<ChatSimulation>): Promise<ChatSimulation> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          updatedAt: new Date().toISOString(),
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update simulation: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating simulation:', error);
      throw error;
    }
  }

  /**
   * Delete simulation
   */
  static async deleteSimulation(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete simulation: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting simulation:', error);
      throw error;
    }
  }

  /**
   * Execute simulation
   */
  static async executeSimulation(id: string): Promise<ChatSimulation> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`Failed to execute simulation: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error executing simulation:', error);
      throw error;
    }
  }

  /**
   * Get simulation results
   */
  static async getSimulationResults(id: string): Promise<SimulationResult[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/results`);
      if (!response.ok) {
        throw new Error(`Failed to fetch simulation results: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching simulation results:', error);
      return [];
    }
  }

  /**
   * Add result to simulation
   */
  static async addSimulationResult(simulationId: string, result: Omit<SimulationResult, 'id' | 'timestamp'>): Promise<SimulationResult> {
    try {
      const response = await fetch(`${this.baseUrl}/${simulationId}/results`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(result),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to add simulation result: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error adding simulation result:', error);
      // Return mock result for development
      const mockResult: SimulationResult = {
        ...result,
        id: `result_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: new Date(),
      };
      return mockResult;
    }
  }

  /**
   * Export simulation results
   */
  static async exportSimulationResults(id: string, format: 'json' | 'csv' | 'xlsx' = 'json'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/export?format=${format}`);
      if (!response.ok) {
        throw new Error(`Failed to export simulation results: ${response.statusText}`);
      }
      return await response.blob();
    } catch (error) {
      console.error('Error exporting simulation results:', error);
      // Return mock export for development
      const results = await this.getSimulationResults(id);
      const dataStr = JSON.stringify(results, null, 2);
      return new Blob([dataStr], { type: 'application/json' });
    }
  }

  /**
   * Duplicate simulation
   */
  static async duplicateSimulation(id: string, newName?: string): Promise<ChatSimulation> {
    const original = await this.getSimulation(id);
    if (!original) {
      throw new Error('Simulation not found');
    }

    const duplicate = {
      ...original,
      name: newName || `${original.name} (Copy)`,
      status: 'pending' as const,
      results: [], // Clear results for new simulation
    };
    
    // Remove fields that should be regenerated
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...rest } = duplicate;
    
    return this.createSimulation(rest);
  }

  /**
   * Get simulation statistics
   */
  static async getSimulationStatistics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    averagePrompts: number;
    averageResults: number;
    recentActivity: { date: string; executed: number; created: number }[];
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/statistics`);
      if (!response.ok) {
        throw new Error(`Failed to fetch simulation statistics: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching simulation statistics:', error);
      // Return mock statistics
      return {
        total: 8,
        byStatus: {
          'pending': 3,
          'running': 1,
          'completed': 4,
          'error': 0
        },
        averagePrompts: 2.5,
        averageResults: 2.1,
        recentActivity: [
          { date: '2024-01-15', executed: 2, created: 1 },
          { date: '2024-01-14', executed: 1, created: 0 },
          { date: '2024-01-13', executed: 0, created: 2 }
        ]
      };
    }
  }

  /**
   * Mock data for development
   */
  private static getMockSimulations(): ChatSimulation[] {
    return [
      {
        id: 'sim_1',
        name: 'AI Healthcare Analysis',
        description: 'Simulation exploring AI applications in healthcare',
        sourceNodeIds: ['mock_1'],
        prompts: [
          {
            id: 'prompt_1',
            name: 'Healthcare AI Benefits',
            content: 'Explore the benefits of AI in healthcare and provide detailed analysis...',
            sourceType: 'node-derived',
            sourceNodeId: 'mock_1'
          }
        ],
        results: [
          {
            id: 'result_1',
            promptId: 'prompt_1',
            response: 'AI in healthcare offers transformative potential through predictive analytics, personalized medicine, and automated diagnostics...',
            timestamp: new Date('2024-01-15T10:00:00Z'),
            metadata: {
              model: 'gpt-4',
              tokens: 450,
              duration: 2500
            }
          }
        ],
        status: 'completed',
        createdAt: new Date('2024-01-15T09:00:00Z'),
        updatedAt: new Date('2024-01-15T10:05:00Z')
      },
      {
        id: 'sim_2',
        name: 'Renewable Energy Discussion',
        description: 'Multi-perspective analysis of renewable energy adoption',
        sourceNodeIds: ['mock_2'],
        prompts: [
          {
            id: 'prompt_2',
            name: 'Energy Transition Challenges',
            content: 'Discuss the challenges and opportunities in transitioning to renewable energy...',
            sourceType: 'custom'
          }
        ],
        results: [],
        status: 'pending',
        createdAt: new Date('2024-01-14T15:00:00Z'),
        updatedAt: new Date('2024-01-14T15:00:00Z')
      }
    ];
  }
}
