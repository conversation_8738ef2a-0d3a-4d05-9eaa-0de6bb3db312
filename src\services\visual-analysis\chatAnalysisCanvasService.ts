import { NodeData, ConnectionData, ChatAnalysisCanvasData, AnalysisCluster } from '@/components/visual-analysis/types';
import * as THREE from 'three';

/**
 * Service for converting chat analysis records to canvas data
 * and managing the integration between analysis data and visual representation
 */
export class ChatAnalysisCanvasService {
  
  /**
   * Convert AnalysisResult to NodeData for canvas visualization
   */
  static analysisResultToNodeData(analysisResult: AnalysisResult, position?: { x: number; y: number }): NodeData {
    // Generate a color based on analysis type
    const color = this.getColorByAnalysisType(analysisResult.analysisType);
    
    // Calculate value based on analysis characteristics
    const value = this.calculateNodeValue(analysisResult);
    
    // Generate tags from analysis properties
    const tags = this.generateTagsFromAnalysis(analysisResult);
    
    return {
      id: analysisResult.id,
      label: this.generateNodeLabel(analysisResult),
      value,
      category: analysisResult.analysisType,
      position: position || this.generateRandomPosition(),
      color,
      tags,
      analysisRecord: analysisResult,
      nodeType: 'analysis-record',
      status: 'completed' // Default status for existing records
    };
  }

  /**
   * Convert multiple AnalysisResults to canvas data
   */
  static analysisResultsToCanvasData(
    analysisResults: AnalysisResult[], 
    existingConnections: ConnectionData[] = [],
    clusters: AnalysisCluster[] = []
  ): ChatAnalysisCanvasData {
    const nodes = analysisResults.map((result, index) => 
      this.analysisResultToNodeData(result, this.generateGridPosition(index, analysisResults.length))
    );

    return {
      nodes,
      connections: existingConnections,
      clusters,
      simulations: [],
      metadata: {
        lastUpdated: new Date(),
        version: '1.0.0'
      }
    };
  }

  /**
   * Generate a descriptive label for the analysis node
   */
  private static generateNodeLabel(analysis: AnalysisResult): string {
    const maxLength = 50;
    let label = analysis.question;
    
    // Add analysis type indicator
    const typeIndicator = this.getAnalysisTypeIndicator(analysis.analysisType);
    label = `${typeIndicator} ${label}`;
    
    // Truncate if too long
    if (label.length > maxLength) {
      label = label.substring(0, maxLength - 3) + '...';
    }
    
    return label;
  }

  /**
   * Get color based on analysis type
   */
  private static getColorByAnalysisType(analysisType: string): number {
    const colorMap: Record<string, number> = {
      'multiple': 0x4A90E2,      // Blue
      'deep': 0x7ED321,          // Green  
      'character': 0xF5A623,     // Orange
      'pros-cons': 0xD0021B,     // Red
      'six-hats': 0x9013FE,      // Purple
      'emotional-angles': 0xE91E63 // Pink
    };
    
    return colorMap[analysisType] || 0x50E3C2; // Default teal
  }

  /**
   * Get emoji indicator for analysis type
   */
  private static getAnalysisTypeIndicator(analysisType: string): string {
    const indicatorMap: Record<string, string> = {
      'multiple': '🔄',
      'deep': '🔍',
      'character': '👤',
      'pros-cons': '⚖️',
      'six-hats': '🎩',
      'emotional-angles': '💭'
    };
    
    return indicatorMap[analysisType] || '📊';
  }

  /**
   * Calculate node value based on analysis characteristics
   */
  private static calculateNodeValue(analysis: AnalysisResult): number {
    let value = 50; // Base value
    
    // Add value based on analysis length
    value += Math.min(analysis.analysis.length / 100, 30);
    
    // Add value for refinements
    if (analysis.refinements && analysis.refinements.length > 0) {
      value += analysis.refinements.length * 5;
    }
    
    // Add value for rating
    if (analysis.rating) {
      value += analysis.rating * 0.2;
    }
    
    // Add value for follow-up questions
    if (analysis.followUpQuestions && analysis.followUpQuestions.length > 0) {
      value += analysis.followUpQuestions.length * 3;
    }
    
    return Math.min(Math.max(value, 10), 100); // Clamp between 10-100
  }

  /**
   * Generate tags from analysis properties
   */
  private static generateTagsFromAnalysis(analysis: AnalysisResult): string[] {
    const tags: string[] = [];
    
    tags.push(analysis.style);
    tags.push(analysis.analysisType);
    tags.push(analysis.model);
    
    if (analysis.characterPersona) {
      tags.push('character');
      tags.push(analysis.characterPersona.name);
    }
    
    if (analysis.selectedEmotions) {
      tags.push(...analysis.selectedEmotions);
    }
    
    // Add time-based tags
    const now = new Date();
    const analysisDate = new Date(analysis.timestamp);
    const daysDiff = Math.floor((now.getTime() - analysisDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 0) tags.push('today');
    else if (daysDiff <= 7) tags.push('this-week');
    else if (daysDiff <= 30) tags.push('this-month');
    else tags.push('older');
    
    return tags;
  }

  /**
   * Generate random position for node placement
   */
  private static generateRandomPosition(): { x: number; y: number; z: number } {
    return {
      x: (Math.random() - 0.5) * 10,
      y: (Math.random() - 0.5) * 10,
      z: 0
    };
  }

  /**
   * Generate grid position for organized layout
   */
  private static generateGridPosition(index: number, total: number): { x: number; y: number; z: number } {
    const cols = Math.ceil(Math.sqrt(total));
    const row = Math.floor(index / cols);
    const col = index % cols;
    
    return {
      x: (col - cols / 2) * 3,
      y: (row - Math.ceil(total / cols) / 2) * 3,
      z: 0
    };
  }

  /**
   * Create connections between related analysis records
   */
  static generateConnectionsFromAnalysisResults(analysisResults: AnalysisResult[]): ConnectionData[] {
    const connections: ConnectionData[] = [];
    
    // Connect analyses with similar questions or contexts
    for (let i = 0; i < analysisResults.length; i++) {
      for (let j = i + 1; j < analysisResults.length; j++) {
        const similarity = this.calculateAnalysisSimilarity(analysisResults[i], analysisResults[j]);
        
        if (similarity > 0.3) { // Threshold for connection
          connections.push({
            id: `${analysisResults[i].id}-${analysisResults[j].id}`,
            source: analysisResults[i].id,
            target: analysisResults[j].id,
            strength: similarity,
            label: this.getConnectionLabel(analysisResults[i], analysisResults[j]),
            tags: ['auto-generated', 'similarity']
          });
        }
      }
    }
    
    return connections;
  }

  /**
   * Calculate similarity between two analysis results
   */
  private static calculateAnalysisSimilarity(a1: AnalysisResult, a2: AnalysisResult): number {
    let similarity = 0;
    
    // Same analysis type
    if (a1.analysisType === a2.analysisType) similarity += 0.3;
    
    // Same style
    if (a1.style === a2.style) similarity += 0.2;
    
    // Same model
    if (a1.model === a2.model) similarity += 0.1;
    
    // Similar questions (basic text similarity)
    const questionSimilarity = this.calculateTextSimilarity(a1.question, a2.question);
    similarity += questionSimilarity * 0.4;
    
    return Math.min(similarity, 1.0);
  }

  /**
   * Basic text similarity calculation
   */
  private static calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;
    
    return commonWords.length / totalWords;
  }

  /**
   * Generate connection label
   */
  private static getConnectionLabel(a1: AnalysisResult, a2: AnalysisResult): string {
    if (a1.analysisType === a2.analysisType) {
      return `Similar ${a1.analysisType} analysis`;
    }
    if (a1.style === a2.style) {
      return `Same ${a1.style} style`;
    }
    return 'Related analysis';
  }
}
