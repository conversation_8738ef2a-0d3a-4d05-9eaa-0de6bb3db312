import { useState, useCallback, useEffect, useRef } from "react"; // Added useRef
import { AnalysisResult, CharacterPersona, ParsedOutput } from "@/types/conversation"; // Added ParsedOutput
import { useToast } from "@/hooks/use-toast";
import { ChatMessage, FeedbackMode, ConversationModeType } from "./types"; // Added ConversationModeType
import { processUserMessage } from "@/services/chatMessageManager";
import { parseFeedback, round } from "./feedbackUtils";

interface UseChatProps {
  initialAnalysis: AnalysisResult;
  apiKey: string;
  selectedModel: string;
  selectedPersona: CharacterPersona | null;
  feedbackMode: FeedbackMode;
  conversationMode: ConversationModeType; // Added
}

export const useChat = ({
  initialAnalysis,
  apiKey,
  selectedModel,
  selectedPersona,
  feedbackMode,
  conversationMode, // Added
}: UseChatProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>(() => [
    {
      id: "initial",
      role: "assistant",
      content: initialAnalysis.analysis,
      timestamp: new Date(initialAnalysis.timestamp),
    },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Refs to track current index for cycling modes
  const currentAnswerIndex = useRef(0);
  const currentFollowUpIndex = useRef(0);

  useEffect(() => {
    setMessages([
      {
        id: `initial-${initialAnalysis.id}`,
        role: "assistant",
        content: initialAnalysis.analysis,
        timestamp: new Date(initialAnalysis.timestamp),
      },
    ]);
    // Reset cycle indices when initialAnalysis changes
    currentAnswerIndex.current = 0;
    currentFollowUpIndex.current = 0;
  }, [initialAnalysis]);

  const getNextCycleItem = (parsedOutput: ParsedOutput | undefined, mode: ConversationModeType): string | null => {
    if (!parsedOutput) return null;

    if (mode === "cycle-answers" && parsedOutput.answers) {
      if (currentAnswerIndex.current < parsedOutput.answers.length) {
        const nextItem = parsedOutput.answers[currentAnswerIndex.current];
        currentAnswerIndex.current++;
        return typeof nextItem === 'string' ? nextItem : nextItem.answer; // Handle both string[] and AnswerItem[]
      }
    } else if (mode === "cycle-followups" && parsedOutput.follow_up_questions) {
      if (currentFollowUpIndex.current < parsedOutput.follow_up_questions.length) {
        const nextItem = parsedOutput.follow_up_questions[currentFollowUpIndex.current];
        currentFollowUpIndex.current++;
        return nextItem;
      }
    }
    return null;
  };

  const sendMessage = useCallback(
    async (currentMessage: string, isAutoCycleMessage: boolean = false) => {
      if (!currentMessage.trim() || !apiKey) return;

      const timestamp = new Date();
      const userId = Date.now().toString();

      const userMessage: ChatMessage = {
        id: userId,
        role: isAutoCycleMessage ? "system" : "user", // System role for auto-cycled messages
        content: currentMessage,
        timestamp,
        autoFeedback: isAutoCycleMessage ? undefined : { text: "", isLoading: true },
      };

      const history = [...messages];
      setMessages((prev) => [...prev, userMessage]);
      setIsLoading(true);

      try {
        const { assistantContent, rawFeedbackText } = await processUserMessage({
          apiKey,
          model: selectedModel,
          selectedPersona,
          initialAnalysis,
          history,
          currentMessage,
          feedbackMode,
        });

        const newAssistantMsg: ChatMessage = {
          id: Date.now().toString() + "_assistant",
          role: "assistant",
          content: assistantContent,
          timestamp: new Date(),
        };
        setMessages((prev) => [...prev, newAssistantMsg]);
        
        if (!isAutoCycleMessage) {
            const { score, coaching, detailScores, biasAnalysis } = parseFeedback(rawFeedbackText);
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === userId && msg.role === "user"
                  ? {
                      ...msg,
                      performanceScore: typeof score === "number" ? round(score) : undefined,
                      autoFeedback: {
                        text: coaching,
                        isLoading: false,
                        detail: detailScores,
                        biasAnalysis: biasAnalysis,
                      },
                    }
                  : msg
              )
            );
        }

        // Handle cycling modes
        if (conversationMode === "cycle-answers" || conversationMode === "cycle-followups") {
          const nextItemToCycle = getNextCycleItem(initialAnalysis.parsedOutput, conversationMode);
          if (nextItemToCycle) {
            // Automatically send the next item as a new "system" message to trigger AI response
            // Or, could inject it into context for user to send, but auto-send is more direct for cycling
            // Adding a slight delay for readability in UI
            setTimeout(() => {
              sendMessage(nextItemToCycle, true); 
            }, 500);
          } else {
            // Optional: Notify user that cycle is complete
            toast({ title: "Cycle Complete", description: `All ${conversationMode === "cycle-answers" ? "answers" : "follow-ups"} have been cycled.` });
          }
        }

      } catch (error) {
        console.error("Chat message/feedback failed:", error);
        toast({
          title: "Interaction Failed",
          description: "Message or feedback could not be processed. Please try again.",
          variant: "destructive",
        });
        setMessages(history);
      } finally {
        setIsLoading(false);
      }
    },
    [apiKey, messages, selectedModel, selectedPersona, initialAnalysis, toast, feedbackMode, conversationMode] // Added conversationMode
  );

  const rateMessage = useCallback(() => {}, []);

  return { messages, isLoading, sendMessage, rateMessage };
};
