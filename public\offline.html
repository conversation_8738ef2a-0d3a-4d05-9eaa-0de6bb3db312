<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatCraft Trainer Pro - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .description {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .features {
            text-align: left;
            margin: 30px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
        }

        .features h3 {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .status.online {
            background: rgba(74, 222, 128, 0.2);
            border: 1px solid rgba(74, 222, 128, 0.3);
        }

        .status.offline {
            background: rgba(248, 113, 113, 0.2);
            border: 1px solid rgba(248, 113, 113, 0.3);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 600px) {
            .offline-container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1>You're Offline</h1>
        <p class="subtitle">But ChatCraft Trainer Pro still works!</p>
        
        <p class="description">
            Don't worry - our offline-first design means you can continue using most features 
            even without an internet connection.
        </p>

        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>Local AI analysis with cached responses</li>
                <li>Visual Canvas with design tools</li>
                <li>Historical analysis review</li>
                <li>Data visualization and charts</li>
                <li>Export and presentation features</li>
                <li>All your saved work and settings</li>
            </ul>
        </div>

        <button class="retry-button" onclick="checkConnection()">
            <span id="retry-text">Check Connection</span>
        </button>
        
        <button class="retry-button" onclick="goToApp()">
            Continue Offline
        </button>

        <div id="status" class="status offline">
            <strong>Status:</strong> <span id="connection-status">Offline</span>
        </div>
    </div>

    <script>
        let isChecking = false;

        function updateConnectionStatus() {
            const status = document.getElementById('status');
            const connectionStatus = document.getElementById('connection-status');
            
            if (navigator.onLine) {
                status.className = 'status online';
                connectionStatus.textContent = 'Online - All features available';
            } else {
                status.className = 'status offline';
                connectionStatus.textContent = 'Offline - Limited features available';
            }
        }

        async function checkConnection() {
            if (isChecking) return;
            
            isChecking = true;
            const retryText = document.getElementById('retry-text');
            const originalText = retryText.textContent;
            
            retryText.innerHTML = '<span class="loading"></span>Checking...';
            
            try {
                // Try to fetch a small resource to test connectivity
                const response = await fetch('/', { 
                    method: 'HEAD',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(5000)
                });
                
                if (response.ok) {
                    // Connection restored, redirect to main app
                    window.location.href = '/';
                    return;
                }
            } catch (error) {
                console.log('Still offline:', error);
            }
            
            setTimeout(() => {
                retryText.textContent = originalText;
                isChecking = false;
                updateConnectionStatus();
            }, 1000);
        }

        function goToApp() {
            // Try to go to the main app
            window.location.href = '/';
        }

        // Listen for online/offline events
        window.addEventListener('online', () => {
            updateConnectionStatus();
            // Auto-redirect when connection is restored
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        });

        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            if (!isChecking && !navigator.onLine) {
                // Silently check connection every 30 seconds when offline
                fetch('/', { 
                    method: 'HEAD', 
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(3000)
                })
                .then(response => {
                    if (response.ok && !navigator.onLine) {
                        // Connection might be restored but browser hasn't detected it yet
                        window.location.href = '/';
                    }
                })
                .catch(() => {
                    // Still offline, do nothing
                });
            }
        }, 30000);

        // Handle service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                const { type, data } = event.data;
                
                if (type === 'CACHE_UPDATED') {
                    console.log('Cache updated, app ready for offline use');
                }
            });
        }
    </script>
</body>
</html>
