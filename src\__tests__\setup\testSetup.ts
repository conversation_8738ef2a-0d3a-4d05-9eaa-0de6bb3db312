/**
 * Test Setup and Configuration
 * 
 * Configures the testing environment with mocks, utilities, and
 * global setup for unit, integration, and e2e tests.
 */

import { vi, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';

// Mock global objects and APIs
beforeAll(() => {
  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock Canvas API
  HTMLCanvasElement.prototype.getContext = vi.fn().mockReturnValue({
    fillRect: vi.fn(),
    clearRect: vi.fn(),
    getImageData: vi.fn(() => ({ data: new Array(4) })),
    putImageData: vi.fn(),
    createImageData: vi.fn(() => ({ data: new Array(4) })),
    setTransform: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    fillText: vi.fn(),
    restore: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    closePath: vi.fn(),
    stroke: vi.fn(),
    translate: vi.fn(),
    scale: vi.fn(),
    rotate: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    measureText: vi.fn(() => ({ width: 0 })),
    transform: vi.fn(),
    rect: vi.fn(),
    clip: vi.fn(),
  });

  // Mock URL.createObjectURL
  global.URL.createObjectURL = vi.fn(() => 'mocked-url');
  global.URL.revokeObjectURL = vi.fn();

  // Mock File API
  global.File = class MockFile {
    constructor(public chunks: unknown[], public name: string, public options?: Record<string, unknown>) {}
    get size() { return 1024; }
    get type() { return 'text/plain'; }
    get lastModified() { return Date.now(); }
  } as unknown as typeof File;

  // Mock Blob
  global.Blob = class MockBlob {
    constructor(public chunks: unknown[], public options?: Record<string, unknown>) {}
    get size() { return 1024; }
    get type() { return this.options?.type || 'text/plain'; }
  } as unknown as typeof Blob;

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });

  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
  });

  // Mock IndexedDB
  const indexedDBMock = {
    open: vi.fn().mockReturnValue({
      onsuccess: null,
      onerror: null,
      onupgradeneeded: null,
      result: {
        transaction: vi.fn().mockReturnValue({
          objectStore: vi.fn().mockReturnValue({
            add: vi.fn(),
            put: vi.fn(),
            get: vi.fn(),
            delete: vi.fn(),
            clear: vi.fn(),
            openCursor: vi.fn(),
            createIndex: vi.fn(),
          }),
        }),
        createObjectStore: vi.fn(),
        deleteObjectStore: vi.fn(),
      },
    }),
    deleteDatabase: vi.fn(),
  };
  Object.defineProperty(window, 'indexedDB', {
    value: indexedDBMock,
  });

  // Mock fetch
  global.fetch = vi.fn();

  // Mock navigator
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: true,
  });

  Object.defineProperty(navigator, 'serviceWorker', {
    value: {
      register: vi.fn().mockResolvedValue({}),
      ready: Promise.resolve({}),
      controller: null,
    },
  });

  // Mock crypto
  Object.defineProperty(global, 'crypto', {
    value: {
      randomUUID: vi.fn(() => 'mocked-uuid'),
      getRandomValues: vi.fn((arr) => arr.map(() => Math.floor(Math.random() * 256))),
    },
  });

  // Mock performance
  Object.defineProperty(global, 'performance', {
    value: {
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByType: vi.fn(() => []),
    },
  });
});

// Cleanup after each test
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

// Global test utilities
export const createMockAnalysisResult = (overrides = {}) => ({
  id: 'test-analysis-1',
  question: 'Test question',
  analysis: 'Test analysis content',
  style: 'professional',
  model: 'gpt-4',
  timestamp: new Date(),
  analysisType: 'multiple',
  rating: 8,
  followUpQuestions: ['Follow up 1', 'Follow up 2'],
  ...overrides,
});

export const createMockDesignElement = (overrides = {}) => ({
  id: 'test-element-1',
  type: 'text',
  data: { content: 'Test content' },
  style: {
    color: '#000000',
    fontSize: 14,
    fontFamily: 'Inter',
  },
  position: { x: 100, y: 100 },
  size: { width: 200, height: 50 },
  metadata: {
    analysisId: 'test-analysis-1',
    analysisType: 'multiple',
    createdAt: Date.now(),
    tags: ['test'],
    category: 'text',
  },
  ...overrides,
});

export const createMockPresentation = (overrides = {}) => ({
  id: 'test-presentation-1',
  title: 'Test Presentation',
  subtitle: 'Test Subtitle',
  author: 'Test Author',
  slides: [
    {
      id: 'slide-1',
      title: 'Test Slide',
      content: [],
      layout: { type: 'title', padding: { top: 50, right: 50, bottom: 50, left: 50 } },
      style: {
        backgroundColor: '#ffffff',
        textColor: '#000000',
        accentColor: '#2563eb',
        fontFamily: 'Inter',
        fontSize: { title: 32, subtitle: 18, body: 14, caption: 12 },
      },
    },
  ],
  theme: {
    name: 'Professional',
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0ea5e9',
      background: '#ffffff',
      text: '#1e293b',
      muted: '#64748b',
    },
    fonts: { heading: 'Inter', body: 'Inter', code: 'JetBrains Mono' },
    spacing: { small: 8, medium: 16, large: 32 },
  },
  metadata: {
    createdAt: Date.now(),
    analysisIds: ['test-analysis-1'],
    template: 'professional',
    exportFormats: ['pdf', 'pptx'],
    version: '1.0',
  },
  ...overrides,
});

export const createMockNodeData = (overrides = {}) => ({
  id: 'test-node-1',
  type: 'analysis-input',
  title: 'Test Node',
  content: { question: 'Test question' },
  metadata: {
    createdAt: Date.now(),
    category: 'analysis',
    tags: ['test'],
  },
  style: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
    textColor: '#1f2937',
    shadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    opacity: 1,
  },
  position: { x: 100, y: 100 },
  size: { width: 200, height: 120 },
  ports: [
    {
      id: 'port-1',
      type: 'output',
      dataType: 'question',
      label: 'Output',
      position: 'right',
      connected: false,
      connectionIds: [],
    },
  ],
  ...overrides,
});

// Mock implementations for common hooks
export const mockUseAnalysisResults = {
  results: [createMockAnalysisResult()],
  isLoading: false,
  error: null,
  handleAnalyze: vi.fn(),
  clearResults: vi.fn(),
};

export const mockUseCanvasDataBridge = {
  openCanvasWithAnalysis: vi.fn(),
  exportCanvasData: vi.fn(),
  importCanvasData: vi.fn(),
  clearCanvas: vi.fn(),
  canvasState: {
    isOpen: false,
    mode: 'standard',
    data: null,
  },
};

export const mockUseOfflineStatus = {
  networkState: {
    isOnline: true,
    connectionType: 'wifi',
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
  },
  capabilities: {
    aiAnalysis: true,
    dataStorage: true,
    caching: true,
    serviceWorker: true,
    networkDetection: true,
  },
  isOnline: true,
  shouldUseOffline: false,
  recommendedMode: 'online',
};

// Test environment setup
export const setupTestEnvironment = () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.VITE_APP_VERSION = '1.0.0-test';
  
  // Configure console for tests
  const originalError = console.error;
  console.error = (...args) => {
    // Suppress known React warnings in tests
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
       args[0].includes('Warning: componentWillReceiveProps has been renamed'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
};

// Performance testing utilities
export const measurePerformance = async (fn: () => Promise<void> | void, label: string) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  const duration = end - start;
  
  console.log(`${label}: ${duration.toFixed(2)}ms`);
  
  // Fail test if operation takes too long
  if (duration > 5000) {
    throw new Error(`Performance test failed: ${label} took ${duration.toFixed(2)}ms (limit: 5000ms)`);
  }
  
  return duration;
};

// Memory leak detection
export const detectMemoryLeaks = () => {
  if (typeof window !== 'undefined' && window.performance && (window.performance as unknown as { memory: { usedJSHeapSize: number; jsHeapSizeLimit: number } }).memory) {
    const memory = (window.performance as unknown as { memory: { usedJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
    const used = memory.usedJSHeapSize / 1048576; // Convert to MB
    const limit = memory.jsHeapSizeLimit / 1048576;
    
    console.log(`Memory usage: ${used.toFixed(2)}MB / ${limit.toFixed(2)}MB`);
    
    if (used > limit * 0.8) {
      console.warn('High memory usage detected');
    }
    
    return { used, limit, percentage: (used / limit) * 100 };
  }
  
  return null;
};

// Accessibility testing utilities
export const checkAccessibility = async (container: HTMLElement) => {
  // Basic accessibility checks
  const issues = [];
  
  // Check for missing alt text on images
  const images = container.querySelectorAll('img');
  images.forEach((img, index) => {
    if (!img.getAttribute('alt')) {
      issues.push(`Image ${index + 1} missing alt text`);
    }
  });
  
  // Check for missing labels on form elements
  const inputs = container.querySelectorAll('input, select, textarea');
  inputs.forEach((input, index) => {
    const id = input.getAttribute('id');
    const ariaLabel = input.getAttribute('aria-label');
    const ariaLabelledBy = input.getAttribute('aria-labelledby');
    const label = id ? container.querySelector(`label[for="${id}"]`) : null;
    
    if (!ariaLabel && !ariaLabelledBy && !label) {
      issues.push(`Form element ${index + 1} missing label`);
    }
  });
  
  // Check for proper heading hierarchy
  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let lastLevel = 0;
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    if (level > lastLevel + 1) {
      issues.push(`Heading ${index + 1} skips levels (h${lastLevel} to h${level})`);
    }
    lastLevel = level;
  });
  
  return issues;
};

// Initialize test environment
setupTestEnvironment();
