import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Point, DrawingObject } from '@/types/figma';

export interface ConnectionPoint {
  id: string;
  objectId: string;
  position: Point;
  type: 'input' | 'output' | 'bidirectional';
  direction: 'top' | 'right' | 'bottom' | 'left';
}

export interface SmartConnection {
  id: string;
  sourceObjectId: string;
  targetObjectId: string;
  sourcePoint: ConnectionPoint;
  targetPoint: ConnectionPoint;
  path: Point[];
  style: ConnectionStyle;
  label?: string;
  animated?: boolean;
}

export interface ConnectionStyle {
  strokeColor: string;
  strokeWidth: number;
  strokeDashArray?: number[];
  arrowType: 'none' | 'arrow' | 'diamond' | 'circle';
  arrowSize: number;
  curveType: 'straight' | 'curved' | 'orthogonal' | 'bezier';
}

interface SmartConnectionSystemProps {
  objects: Record<string, DrawingObject>;
  connections: SmartConnection[];
  onConnectionCreate?: (connection: Omit<SmartConnection, 'id'>) => void;
  onConnectionUpdate?: (connectionId: string, updates: Partial<SmartConnection>) => void;
  onConnectionDelete?: (connectionId: string) => void;
  snapDistance?: number;
  showConnectionPoints?: boolean;
}

export const SmartConnectionSystem: React.FC<SmartConnectionSystemProps> = ({
  objects,
  connections,
  onConnectionCreate,
  onConnectionUpdate,
  onConnectionDelete,
  snapDistance = 20,
  showConnectionPoints = true,
}) => {
  const [isConnecting, setIsConnecting] = useState(false);
  const [sourcePoint, setSourcePoint] = useState<ConnectionPoint | null>(null);
  const [currentMousePos, setCurrentMousePos] = useState<Point>({ x: 0, y: 0 });
  const [hoveredConnectionPoint, setHoveredConnectionPoint] = useState<string | null>(null);

  const svgRef = useRef<SVGSVGElement>(null);

  // Generate connection points for each object
  const generateConnectionPoints = useCallback((obj: DrawingObject): ConnectionPoint[] => {
    const { transform } = obj;
    const points: ConnectionPoint[] = [];

    // Generate points on each side of the object
    const sides = [
      { direction: 'top' as const, x: transform.x + transform.width / 2, y: transform.y },
      { direction: 'right' as const, x: transform.x + transform.width, y: transform.y + transform.height / 2 },
      { direction: 'bottom' as const, x: transform.x + transform.width / 2, y: transform.y + transform.height },
      { direction: 'left' as const, x: transform.x, y: transform.y + transform.height / 2 },
    ];

    sides.forEach((side, index) => {
      points.push({
        id: `${obj.id}-${side.direction}`,
        objectId: obj.id,
        position: { x: side.x, y: side.y },
        type: 'bidirectional',
        direction: side.direction,
      });
    });

    return points;
  }, []);

  // Get all connection points from all objects
  const allConnectionPoints = Object.values(objects).flatMap(generateConnectionPoints);

  // Find the nearest connection point to a given position
  const findNearestConnectionPoint = useCallback((position: Point, excludeObjectId?: string): ConnectionPoint | null => {
    let nearest: ConnectionPoint | null = null;
    let minDistance = snapDistance;

    allConnectionPoints.forEach(point => {
      if (excludeObjectId && point.objectId === excludeObjectId) return;

      const distance = Math.sqrt(
        Math.pow(point.position.x - position.x, 2) + 
        Math.pow(point.position.y - position.y, 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    });

    return nearest;
  }, [allConnectionPoints, snapDistance]);

  // Generate smart path between two points
  const generateSmartPath = useCallback((
    source: ConnectionPoint, 
    target: ConnectionPoint, 
    curveType: ConnectionStyle['curveType'] = 'curved'
  ): Point[] => {
    const start = source.position;
    const end = target.position;

    switch (curveType) {
      case 'straight':
        return [start, end];

      case 'orthogonal':
        return generateOrthogonalPath(start, end, source.direction, target.direction);

      case 'bezier':
        return generateBezierPath(start, end, source.direction, target.direction);

      case 'curved':
      default:
        return generateCurvedPath(start, end, source.direction, target.direction);
    }
  }, []);

  const generateOrthogonalPath = (
    start: Point, 
    end: Point, 
    startDir: ConnectionPoint['direction'], 
    endDir: ConnectionPoint['direction']
  ): Point[] => {
    const path: Point[] = [start];
    const offset = 30; // Distance to extend from connection point

    // Calculate intermediate points for orthogonal routing
    const current = { ...start };

    // Move out from start point
    switch (startDir) {
      case 'top':
        current.y -= offset;
        break;
      case 'right':
        current.x += offset;
        break;
      case 'bottom':
        current.y += offset;
        break;
      case 'left':
        current.x -= offset;
        break;
    }
    path.push({ ...current });

    // Calculate target approach point
    const targetApproach = { ...end };
    switch (endDir) {
      case 'top':
        targetApproach.y -= offset;
        break;
      case 'right':
        targetApproach.x += offset;
        break;
      case 'bottom':
        targetApproach.y += offset;
        break;
      case 'left':
        targetApproach.x -= offset;
        break;
    }

    // Add intermediate routing points
    if (startDir === 'top' || startDir === 'bottom') {
      if (endDir === 'left' || endDir === 'right') {
        path.push({ x: current.x, y: targetApproach.y });
      } else {
        const midY = (current.y + targetApproach.y) / 2;
        path.push({ x: current.x, y: midY });
        path.push({ x: targetApproach.x, y: midY });
      }
    } else {
      if (endDir === 'top' || endDir === 'bottom') {
        path.push({ x: targetApproach.x, y: current.y });
      } else {
        const midX = (current.x + targetApproach.x) / 2;
        path.push({ x: midX, y: current.y });
        path.push({ x: midX, y: targetApproach.y });
      }
    }

    path.push(targetApproach);
    path.push(end);

    return path;
  };

  const generateCurvedPath = (
    start: Point, 
    end: Point, 
    startDir: ConnectionPoint['direction'], 
    endDir: ConnectionPoint['direction']
  ): Point[] => {
    // For curved paths, we'll use quadratic curves
    const distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
    const controlOffset = Math.min(distance * 0.3, 100);

    let controlPoint: Point;

    // Calculate control point based on connection directions
    if (startDir === 'right' && endDir === 'left') {
      controlPoint = {
        x: start.x + controlOffset,
        y: (start.y + end.y) / 2
      };
    } else if (startDir === 'left' && endDir === 'right') {
      controlPoint = {
        x: start.x - controlOffset,
        y: (start.y + end.y) / 2
      };
    } else if (startDir === 'bottom' && endDir === 'top') {
      controlPoint = {
        x: (start.x + end.x) / 2,
        y: start.y + controlOffset
      };
    } else if (startDir === 'top' && endDir === 'bottom') {
      controlPoint = {
        x: (start.x + end.x) / 2,
        y: start.y - controlOffset
      };
    } else {
      // Default control point
      controlPoint = {
        x: (start.x + end.x) / 2,
        y: (start.y + end.y) / 2
      };
    }

    return [start, controlPoint, end];
  };

  const generateBezierPath = (
    start: Point, 
    end: Point, 
    startDir: ConnectionPoint['direction'], 
    endDir: ConnectionPoint['direction']
  ): Point[] => {
    const distance = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
    const controlOffset = Math.min(distance * 0.4, 120);

    // Generate two control points for cubic Bezier
    let cp1: Point, cp2: Point;

    switch (startDir) {
      case 'right':
        cp1 = { x: start.x + controlOffset, y: start.y };
        break;
      case 'left':
        cp1 = { x: start.x - controlOffset, y: start.y };
        break;
      case 'bottom':
        cp1 = { x: start.x, y: start.y + controlOffset };
        break;
      case 'top':
        cp1 = { x: start.x, y: start.y - controlOffset };
        break;
    }

    switch (endDir) {
      case 'right':
        cp2 = { x: end.x + controlOffset, y: end.y };
        break;
      case 'left':
        cp2 = { x: end.x - controlOffset, y: end.y };
        break;
      case 'bottom':
        cp2 = { x: end.x, y: end.y + controlOffset };
        break;
      case 'top':
        cp2 = { x: end.x, y: end.y - controlOffset };
        break;
    }

    return [start, cp1, cp2, end];
  };

  // Handle connection point click
  const handleConnectionPointClick = useCallback((point: ConnectionPoint) => {
    if (!isConnecting) {
      // Start connection
      setIsConnecting(true);
      setSourcePoint(point);
    } else if (sourcePoint && point.objectId !== sourcePoint.objectId) {
      // Complete connection
      const path = generateSmartPath(sourcePoint, point);
      const newConnection: Omit<SmartConnection, 'id'> = {
        sourceObjectId: sourcePoint.objectId,
        targetObjectId: point.objectId,
        sourcePoint,
        targetPoint: point,
        path,
        style: {
          strokeColor: '#6366f1',
          strokeWidth: 2,
          arrowType: 'arrow',
          arrowSize: 8,
          curveType: 'curved',
        },
      };

      onConnectionCreate?.(newConnection);
      setIsConnecting(false);
      setSourcePoint(null);
    }
  }, [isConnecting, sourcePoint, generateSmartPath, onConnectionCreate]);

  // Handle mouse move for preview
  const handleMouseMove = useCallback((e: React.MouseEvent<SVGSVGElement>) => {
    const rect = svgRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mousePos: Point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    setCurrentMousePos(mousePos);

    // Check for hovered connection points
    const nearestPoint = findNearestConnectionPoint(mousePos);
    setHoveredConnectionPoint(nearestPoint?.id || null);
  }, [findNearestConnectionPoint]);

  // Render connection path as SVG
  const renderConnectionPath = (connection: SmartConnection) => {
    const { path, style } = connection;
    
    if (path.length < 2) return null;

    let pathString = '';

    if (style.curveType === 'bezier' && path.length === 4) {
      // Cubic Bezier curve
      pathString = `M ${path[0].x} ${path[0].y} C ${path[1].x} ${path[1].y} ${path[2].x} ${path[2].y} ${path[3].x} ${path[3].y}`;
    } else if (style.curveType === 'curved' && path.length === 3) {
      // Quadratic curve
      pathString = `M ${path[0].x} ${path[0].y} Q ${path[1].x} ${path[1].y} ${path[2].x} ${path[2].y}`;
    } else {
      // Straight or orthogonal path
      pathString = `M ${path[0].x} ${path[0].y} ` + 
        path.slice(1).map(p => `L ${p.x} ${p.y}`).join(' ');
    }

    return (
      <g key={connection.id}>
        <path
          d={pathString}
          stroke={style.strokeColor}
          strokeWidth={style.strokeWidth}
          strokeDasharray={style.strokeDashArray?.join(' ')}
          fill="none"
          markerEnd={style.arrowType !== 'none' ? `url(#arrow-${connection.id})` : undefined}
        />
        
        {/* Arrow marker */}
        {style.arrowType === 'arrow' && (
          <defs>
            <marker
              id={`arrow-${connection.id}`}
              viewBox="0 0 10 10"
              refX="9"
              refY="3"
              markerWidth={style.arrowSize}
              markerHeight={style.arrowSize}
              orient="auto"
            >
              <path d="M0,0 L0,6 L9,3 z" fill={style.strokeColor} />
            </marker>
          </defs>
        )}

        {/* Connection label */}
        {connection.label && (
          <text
            x={path[Math.floor(path.length / 2)].x}
            y={path[Math.floor(path.length / 2)].y - 10}
            textAnchor="middle"
            fontSize="12"
            fill={style.strokeColor}
            className="pointer-events-none"
          >
            {connection.label}
          </text>
        )}
      </g>
    );
  };

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg
        ref={svgRef}
        className="w-full h-full pointer-events-auto"
        onMouseMove={handleMouseMove}
        onClick={() => {
          if (isConnecting) {
            setIsConnecting(false);
            setSourcePoint(null);
          }
        }}
      >
        {/* Render existing connections */}
        {connections.map(renderConnectionPath)}

        {/* Render preview connection while connecting */}
        {isConnecting && sourcePoint && (
          <line
            x1={sourcePoint.position.x}
            y1={sourcePoint.position.y}
            x2={currentMousePos.x}
            y2={currentMousePos.y}
            stroke="#6366f1"
            strokeWidth={2}
            strokeDasharray="5,5"
            opacity={0.7}
          />
        )}

        {/* Render connection points */}
        {showConnectionPoints && allConnectionPoints.map(point => (
          <circle
            key={point.id}
            cx={point.position.x}
            cy={point.position.y}
            r={hoveredConnectionPoint === point.id ? 6 : 4}
            fill={hoveredConnectionPoint === point.id ? '#ef4444' : '#6366f1'}
            stroke="#ffffff"
            strokeWidth={2}
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              handleConnectionPointClick(point);
            }}
            opacity={showConnectionPoints ? 1 : 0}
          />
        ))}
      </svg>
    </div>
  );
};
