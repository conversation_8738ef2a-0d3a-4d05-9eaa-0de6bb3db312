import { FC, ReactNode } from 'react';
import { Loader2, Sparkles } from 'lucide-react';

interface LoadingFallbackProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  className?: string;
  variant?: 'default' | 'gradient' | 'shimmer';
}

export const LoadingFallback: FC<LoadingFallbackProps> = ({
  size = 'md',
  message = 'Loading...',
  className = '',
  variant = 'default'
}) => {
  console.log('⏳ LoadingFallback rendering:', { size, message, variant });
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8'
  };

  const containerClasses = {
    sm: 'min-h-[100px]',
    md: 'min-h-[200px]',
    lg: 'min-h-[400px]'
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-br from-primary/5 via-purple-600/5 to-primary/5 backdrop-blur-sm border border-border/50 rounded-2xl p-8';
      case 'shimmer':
        return 'loading-shimmer rounded-xl p-6';
      default:
        return '';
    }
  };

  return (
    <div className={`${containerClasses[size]} flex items-center justify-center ${className} ${getVariantStyles()}`}>
      <div className="text-center space-y-4">
        <div className="relative">
          <Loader2 className={`${sizeClasses[size]} animate-spin mx-auto text-primary`} />
          {variant === 'gradient' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Sparkles className={`${sizeClasses[size]} text-purple-600 opacity-50 animate-pulse`} />
            </div>
          )}
        </div>
        <p className="text-sm font-medium text-muted-foreground">{message}</p>
        {variant === 'gradient' && (
          <div className="flex justify-center gap-1">
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
          </div>
        )}
      </div>
    </div>
  );
};

// Specialized loading components for different use cases
export const PageLoader: FC = () => {
  console.log('⏳ PageLoader rendering...');
  return (
    <LoadingFallback
      size="lg"
      message="Loading application..."
      className="min-h-screen bg-background"
      variant="gradient"
    />
  );
};

export const ComponentLoader: FC<{ message?: string }> = ({ message }) => (
  <LoadingFallback 
    size="md" 
    message={message || "Loading component..."} 
    variant="gradient"
  />
);

export const ModalLoader: FC = () => (
  <LoadingFallback size="sm" message="Loading..." className="p-8" />
);

export const TabLoader: FC = () => (
  <LoadingFallback 
    size="md" 
    message="Loading content..." 
    className="min-h-[300px]" 
    variant="shimmer"
  />
);
