/**
 * Card Component
 * 
 * A flexible card component that follows the design system.
 * Supports multiple variants, interactive states, and composition patterns.
 */

import { cardVariants, type CardVariants } from '../variants';

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    CardVariants {
  asChild?: boolean;
}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, interactive, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, padding, interactive }), className)}
        {...props}
      />
    );
  }
);

Card.displayName = 'Card';

// Card Header component
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
}

export const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, title, subtitle, action, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col space-y-1.5 p-6 pb-0',
          action && 'flex-row items-start justify-between space-y-0',
          className
        )}
        {...props}
      >
        {(title || subtitle || children) && (
          <div className="space-y-1">
            {title && (
              <h3 className="text-2xl font-semibold leading-none tracking-tight">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
            {children}
          </div>
        )}
        {action && <div className="flex items-center space-x-2">{action}</div>}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Content component
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('p-6 pt-0', className)}
        {...props}
      />
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer component
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-center p-6 pt-0', className)}
        {...props}
      />
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Specialized card variants
export interface StatsCardProps extends CardProps {
  title: string;
  value: string | number;
  change?: {
    value: string | number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: React.ReactNode;
}

export const StatsCard = React.forwardRef<HTMLDivElement, StatsCardProps>(
  ({ title, value, change, icon, className, ...props }, ref) => {
    const changeColor = change
      ? change.type === 'increase'
        ? 'text-success'
        : change.type === 'decrease'
        ? 'text-destructive'
        : 'text-muted-foreground'
      : '';

    return (
      <Card
        ref={ref}
        className={cn('p-6', className)}
        {...props}
      >
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-3xl font-bold">{value}</p>
            {change && (
              <p className={cn('text-sm', changeColor)}>
                {change.type === 'increase' ? '+' : change.type === 'decrease' ? '-' : ''}
                {change.value}
              </p>
            )}
          </div>
          {icon && (
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
              {icon}
            </div>
          )}
        </div>
      </Card>
    );
  }
);

StatsCard.displayName = 'StatsCard';

// Feature card for showcasing features
export interface FeatureCardProps extends CardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

export const FeatureCard = React.forwardRef<HTMLDivElement, FeatureCardProps>(
  ({ title, description, icon, action, className, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        variant="elevated"
        className={cn('p-6 text-center', className)}
        {...props}
      >
        {icon && (
          <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            {icon}
          </div>
        )}
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground mb-4">{description}</p>
        {action && <div className="mt-4">{action}</div>}
      </Card>
    );
  }
);

FeatureCard.displayName = 'FeatureCard';

// Testimonial card
export interface TestimonialCardProps extends CardProps {
  quote: string;
  author: {
    name: string;
    title?: string;
    avatar?: string;
  };
  rating?: number;
}

export const TestimonialCard = React.forwardRef<HTMLDivElement, TestimonialCardProps>(
  ({ quote, author, rating, className, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        className={cn('p-6', className)}
        {...props}
      >
        {rating && (
          <div className="flex mb-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <svg
                key={i}
                className={cn(
                  'h-5 w-5',
                  i < rating ? 'text-yellow-400' : 'text-gray-300'
                )}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
        )}
        <blockquote className="text-lg mb-4">"{quote}"</blockquote>
        <div className="flex items-center">
          {author.avatar && (
            <img
              src={author.avatar}
              alt={author.name}
              className="h-10 w-10 rounded-full mr-3"
            />
          )}
          <div>
            <p className="font-semibold">{author.name}</p>
            {author.title && (
              <p className="text-sm text-muted-foreground">{author.title}</p>
            )}
          </div>
        </div>
      </Card>
    );
  }
);

TestimonialCard.displayName = 'TestimonialCard';

// Export types
export type { CardVariants };
