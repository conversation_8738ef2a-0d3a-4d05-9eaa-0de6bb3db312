import { CharacterPersona } from "@/types/conversation";

export class LibraryService {
  private static CHARACTER_PERSONAS_KEY = "character_personas";

  // Methods for SavedAnalyses, SavedFolders, and UserNotes have been removed
  // as this is now handled by useLibraryStore and useCanvasStore.

  // Character Persona Methods
  static saveCharacterPersona(persona: CharacterPersona): void {
    const personas = this.getCharacterPersonas();
    const existingIndex = personas.findIndex(p => p.id === persona.id);
    
    if (existingIndex >= 0) {
      personas[existingIndex] = persona;
    } else {
      personas.push(persona);
    }
    
    localStorage.setItem(this.CHARACTER_PERSONAS_KEY, JSON.stringify(personas));
  }

  static getCharacterPersonas(): CharacterPersona[] {
    const stored = localStorage.getItem(this.CHARACTER_PERSONAS_KEY);
    if (!stored) return [];
    
    try {
      const parsed = JSON.parse(stored);
      return parsed.map((persona: unknown) => ({
        ...persona,
        createdAt: new Date(persona.createdAt),
        updatedAt: new Date(persona.updatedAt)
      }));
    } catch {
      return [];
    }
  }

  static deleteCharacterPersona(personaId: string): void {
    const personas = this.getCharacterPersonas().filter(p => p.id !== personaId);
    localStorage.setItem(this.CHARACTER_PERSONAS_KEY, JSON.stringify(personas));
  }

  static searchCharacterPersonas(query: string): CharacterPersona[] {
    const personas = this.getCharacterPersonas();
    const lowercaseQuery = query.toLowerCase();
    
    return personas.filter(persona => 
      persona.name.toLowerCase().includes(lowercaseQuery) ||
      persona.coreRole.toLowerCase().includes(lowercaseQuery) ||
      persona.personalityTraits.some(trait => trait.toLowerCase().includes(lowercaseQuery)) ||
      persona.expertise.some(exp => exp.toLowerCase().includes(lowercaseQuery))
    );
  }
}
