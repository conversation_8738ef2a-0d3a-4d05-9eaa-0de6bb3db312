
import { useHistoricalAnalysisSettingsStore, HistoricalAnalysisSettings } from "@/stores/useHistoricalAnalysisSettingsStore";
import { useSettingsStore } from "@/stores/useSettingsStore";

export interface CombinedAISettings extends HistoricalAnalysisSettings {
  openRouterApiKey: string;
  selectedModel: string;
}

export class AIServiceAPIHandler {

  /**
   * Enhanced error handling for historical analysis API responses
   */
  private async handleAPIResponse(response: Response): Promise<unknown> {
    if (!response.ok) {
      let errorBody = '';
      try {
        errorBody = await response.text();
      } catch (e) {
        errorBody = 'Unable to read error response';
      }

      let errorMessage = `Historical Analysis API error (${response.status}): ${response.statusText}`;
      
      // Provide more specific error messages
      switch (response.status) {
        case 400:
          errorMessage = 'Invalid request for historical analysis. Check your settings.';
          break;
        case 401:
          errorMessage = 'Invalid API key for historical analysis.';
          break;
        case 403:
          errorMessage = 'Access forbidden for historical analysis. Check API permissions.';
          break;
        case 429:
          errorMessage = 'Rate limit exceeded for historical analysis. Please wait.';
          break;
        case 500:
          errorMessage = 'OpenRouter server error during historical analysis.';
          break;
        case 502:
        case 503:
        case 504:
          errorMessage = 'Historical analysis service temporarily unavailable.';
          break;
      }

      console.error("Historical Analysis API Error:", {
        status: response.status,
        statusText: response.statusText,
        errorBody
      });

      throw new Error(errorMessage);
    }

    try {
      return await response.json();
    } catch (error) {
      throw new Error('Invalid JSON response from historical analysis API');
    }
  }

  async makeAPIRequest(prompt: string, systemPrompt: string, settings: CombinedAISettings): Promise<string> {
    if (!settings.openRouterApiKey?.trim()) {
      throw new Error("OpenRouter API key is required for AI Historical Analysis");
    }

    if (!prompt?.trim()) {
      throw new Error("Prompt is required for historical analysis");
    }

    if (!settings.selectedModel?.trim()) {
      throw new Error("Model selection is required for historical analysis");
    }

    if (settings.currentMonthUsage >= settings.monthlyTokenLimit) {
      throw new Error("Monthly token limit reached. Please reset or increase your limit.");
    }

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${settings.openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.href,
          'X-Title': 'ChatCraft Trainer Pro - Historical Analysis'
        },
        body: JSON.stringify({
          model: settings.selectedModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: prompt }
          ],
          temperature: Math.max(0, Math.min(2, settings.temperature)), // Clamp temperature
          max_tokens: Math.min(settings.maxTokens, settings.maxTokensPerRequest, 4000), // Safety limit
        }),
      });

      const data = await this.handleAPIResponse(response);
      
      if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
        throw new Error('Invalid response structure from historical analysis API');
      }

      const content = data.choices[0]?.message?.content;

      if (!content || typeof content !== 'string') {
        throw new Error('No valid content received from historical analysis API');
      }

      const tokensUsed = data.usage?.total_tokens || 0;
      if (tokensUsed > 0) {
        useHistoricalAnalysisSettingsStore.getState().incrementTokenUsage(tokensUsed);
      }

      return content;
    } catch (error) {
      console.error('AI Historical Service API call failed:', error);
      
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Historical analysis failed: ${String(error)}`);
      }
    }
  }

  getTokenUsageStats() {
    const settings = useHistoricalAnalysisSettingsStore.getState().settings;
    return {
      used: settings.currentMonthUsage,
      limit: settings.monthlyTokenLimit,
      percentage: settings.monthlyTokenLimit > 0 ? (settings.currentMonthUsage / settings.monthlyTokenLimit) * 100 : 0
    };
  }

  isAPIConfigured(): boolean {
    const settings = useSettingsStore.getState().settings;
    return !!settings.openRouterApiKey;
  }

  canMakeRequest(): boolean {
    const mainSettings = useSettingsStore.getState().settings;
    const histSettings = useHistoricalAnalysisSettingsStore.getState().settings;
    return !!mainSettings.openRouterApiKey && 
           histSettings.currentMonthUsage < histSettings.monthlyTokenLimit;
  }
}
