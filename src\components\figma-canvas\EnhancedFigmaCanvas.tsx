import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON><PERSON>, 
  Layers, 
  Settings, 
  Brain,
  Zap,
  MousePointer,
  Square,
  Circle,
  Type,
  PenTool,
  Spline,
  Link,
  Group,
  Download,
  Upload,
  Share2,
  Eye,
  EyeOff,
  RotateCcw,
  RotateCw
} from 'lucide-react';

// Import our enhanced components
import { SmartConnectionSystem, SmartConnection } from './connections/SmartConnectionSystem';
import { IntelligentClusteringSystem, ClusterBoundary } from './clustering/IntelligentClusteringSystem';
import { AdvancedVectorTools, BezierPoint } from './tools/AdvancedVectorTools';

interface EnhancedFigmaCanvasProps {
  className?: string;
  showAdvancedTools?: boolean;
  enableAIFeatures?: boolean;
  onExport?: (format: string) => void;
}

type AdvancedTool = 'pen-enhanced' | 'connections' | 'clustering' | 'boolean-ops';

export const EnhancedFigmaCanvas: React.FC<EnhancedFigmaCanvasProps> = ({
  className,
  showAdvancedTools = true,
  enableAIFeatures = true,
  onExport,
}) => {
  const [activeAdvancedTool, setActiveAdvancedTool] = useState<AdvancedTool | null>(null);
  const [connections, setConnections] = useState<SmartConnection[]>([]);
  const [clusters, setClusters] = useState<ClusterBoundary[]>([]);
  const [showConnections, setShowConnections] = useState(true);
  const [showClusters, setShowClusters] = useState(true);
  const [showGrid, setShowGrid] = useState(true);

  const { 
    objects, 
    activeTool, 
    setActiveTool,
    selectedObjectIds,
    zoom,
    pan,
    gridVisible,
    toggleGrid,
    snapToGrid,
    toggleSnapToGrid
  } = useFigmaCanvasStore();

  // Handle enhanced pen tool completion
  const handlePenToolComplete = useCallback((points: BezierPoint[]) => {
    console.log('Pen tool completed with points:', points);
    setActiveAdvancedTool(null);
  }, []);

  // Handle connection creation
  const handleConnectionCreate = useCallback((connection: Omit<SmartConnection, 'id'>) => {
    const newConnection: SmartConnection = {
      ...connection,
      id: `connection_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    };
    setConnections(prev => [...prev, newConnection]);
  }, []);

  // Handle cluster creation
  const handleClusterCreate = useCallback((cluster: Omit<ClusterBoundary, 'id'>) => {
    const newCluster: ClusterBoundary = {
      ...cluster,
      id: `cluster_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    };
    setClusters(prev => [...prev, newCluster]);
  }, []);

  // Advanced tool configurations
  const advancedTools = [
    {
      id: 'pen-enhanced' as AdvancedTool,
      icon: Spline,
      label: 'Enhanced Pen',
      description: 'Professional Bezier curve tool',
      badge: 'Pro',
    },
    {
      id: 'connections' as AdvancedTool,
      icon: Link,
      label: 'Smart Connections',
      description: 'Intelligent object connections',
      badge: 'AI',
    },
    {
      id: 'clustering' as AdvancedTool,
      icon: Group,
      label: 'AI Clustering',
      description: 'Automatic object grouping',
      badge: 'AI',
    },
    {
      id: 'boolean-ops' as AdvancedTool,
      icon: Zap,
      label: 'Boolean Ops',
      description: 'Path operations',
      badge: 'Pro',
    },
  ];

  // Standard drawing tools
  const standardTools = [
    { tool: 'select' as DrawingTool, icon: MousePointer, label: 'Select' },
    { tool: 'rectangle' as DrawingTool, icon: Square, label: 'Rectangle' },
    { tool: 'circle' as DrawingTool, icon: Circle, label: 'Circle' },
    { tool: 'text' as DrawingTool, icon: Type, label: 'Text' },
    { tool: 'pen' as DrawingTool, icon: PenTool, label: 'Pen' },
  ];

  const handleToolSelect = useCallback((tool: DrawingTool) => {
    setActiveTool(tool);
    setActiveAdvancedTool(null);
  }, [setActiveTool]);

  const handleAdvancedToolSelect = useCallback((tool: AdvancedTool) => {
    if (tool === 'pen-enhanced') {
      setActiveTool('pen');
    }
    setActiveAdvancedTool(activeAdvancedTool === tool ? null : tool);
  }, [activeAdvancedTool, setActiveTool]);

  const handleExport = useCallback((format: string) => {
    onExport?.(format);
  }, [onExport]);

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Enhanced Toolbar */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            {/* Standard Tools */}
            <div className="flex items-center gap-2">
              {standardTools.map(({ tool, icon: Icon, label }) => (
                <Button
                  key={tool}
                  variant={activeTool === tool ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleToolSelect(tool)}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{label}</span>
                </Button>
              ))}
            </div>

            {/* Advanced Tools */}
            {showAdvancedTools && (
              <div className="flex items-center gap-2">
                {advancedTools.map(({ id, icon: Icon, label, badge }) => (
                  <Button
                    key={id}
                    variant={activeAdvancedTool === id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleAdvancedToolSelect(id)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{label}</span>
                    {badge && <Badge variant="secondary" className="text-xs">{badge}</Badge>}
                  </Button>
                ))}
              </div>
            )}

            {/* View Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConnections(!showConnections)}
              >
                <Link className="h-4 w-4" />
                {showConnections ? <Eye className="h-3 w-3 ml-1" /> : <EyeOff className="h-3 w-3 ml-1" />}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowClusters(!showClusters)}
              >
                <Group className="h-4 w-4" />
                {showClusters ? <Eye className="h-3 w-3 ml-1" /> : <EyeOff className="h-3 w-3 ml-1" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleGrid()}
              >
                <Settings className="h-4 w-4" />
                Grid
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Canvas Area */}
      <div className="flex-1 flex">
        {/* Canvas Container */}
        <div className="flex-1 relative">
          <FigmaCanvasContainer
            className="w-full h-full"
            showLayersPanel={false}
            showPropertiesPanel={false}
            onSelectionChange={(ids) => console.log('Selection changed:', ids)}
            onObjectCreated={(id) => console.log('Object created:', id)}
          />

          {/* Smart Connection Overlay */}
          {showConnections && (
            <SmartConnectionSystem
              objects={objects}
              connections={connections}
              onConnectionCreate={handleConnectionCreate}
              onConnectionUpdate={(id, updates) => {
                setConnections(prev => prev.map(conn => 
                  conn.id === id ? { ...conn, ...updates } : conn
                ));
              }}
              onConnectionDelete={(id) => {
                setConnections(prev => prev.filter(conn => conn.id !== id));
              }}
              showConnectionPoints={activeAdvancedTool === 'connections'}
            />
          )}

          {/* Enhanced Pen Tool Modal */}
          {activeAdvancedTool === 'pen-enhanced' && (
            <EnhancedPenTool
              isActive={true}
              onComplete={handlePenToolComplete}
              onCancel={() => setActiveAdvancedTool(null)}
            />
          )}
        </div>

        {/* Advanced Tools Panel */}
        {activeAdvancedTool && (
          <Card className="w-80 rounded-none border-y-0 border-r-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {activeAdvancedTool === 'connections' && <Link className="h-5 w-5" />}
                {activeAdvancedTool === 'clustering' && <Group className="h-5 w-5" />}
                {activeAdvancedTool === 'boolean-ops' && <Zap className="h-5 w-5" />}
                {activeAdvancedTool === 'pen-enhanced' && <Spline className="h-5 w-5" />}
                
                {activeAdvancedTool === 'connections' && 'Smart Connections'}
                {activeAdvancedTool === 'clustering' && 'AI Clustering'}
                {activeAdvancedTool === 'boolean-ops' && 'Boolean Operations'}
                {activeAdvancedTool === 'pen-enhanced' && 'Enhanced Pen Tool'}
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 overflow-auto">
              {activeAdvancedTool === 'clustering' && (
                <IntelligentClusteringSystem
                  objects={objects}
                  clusters={clusters}
                  onClusterCreate={handleClusterCreate}
                  onClusterUpdate={(id, updates) => {
                    setClusters(prev => prev.map(cluster => 
                      cluster.id === id ? { ...cluster, ...updates } : cluster
                    ));
                  }}
                  onClusterDelete={(id) => {
                    setClusters(prev => prev.filter(cluster => cluster.id !== id));
                  }}
                  enableAIClustering={enableAIFeatures}
                />
              )}

              {activeAdvancedTool === 'connections' && (
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Click on connection points to create smart connections between objects.
                  </div>
                  
                  <div className="space-y-2">
                    <div className="font-medium">Active Connections: {connections.length}</div>
                    {connections.map(conn => (
                      <div key={conn.id} className="p-2 border rounded text-xs">
                        <div className="font-medium">{conn.label || 'Unnamed Connection'}</div>
                        <div className="text-muted-foreground">
                          {conn.sourceObjectId} → {conn.targetObjectId}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeAdvancedTool === 'boolean-ops' && (
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Select two objects to perform boolean operations.
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button size="sm" disabled={selectedObjectIds.length !== 2}>
                      Union
                    </Button>
                    <Button size="sm" disabled={selectedObjectIds.length !== 2}>
                      Subtract
                    </Button>
                    <Button size="sm" disabled={selectedObjectIds.length !== 2}>
                      Intersect
                    </Button>
                    <Button size="sm" disabled={selectedObjectIds.length !== 2}>
                      Exclude
                    </Button>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    Selected objects: {selectedObjectIds.length}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Status Bar */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Objects: {Object.keys(objects).length}</span>
              <span>Connections: {connections.length}</span>
              <span>Clusters: {clusters.length}</span>
              <span>Selected: {selectedObjectIds.length}</span>
            </div>
            
            <div className="flex items-center gap-4">
              <span>Zoom: {(zoom * 100).toFixed(0)}%</span>
              <span>Grid: {gridVisible ? 'On' : 'Off'}</span>
              <span>Snap: {snapToGrid ? 'On' : 'Off'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedFigmaCanvas;
