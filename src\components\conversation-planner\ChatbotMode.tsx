import React, { useState, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { MessageSquare } from "lucide-react";
import { AnalysisResult, CharacterPersona } from "@/types/conversation";
import { useToast } from "@/hooks/use-toast";
import { useUIStore } from "@/stores/useUIStore";
import { CharacterPersonaModal } from "./CharacterPersonaModal";
import { ChatHeader } from "./chatbot/ChatHeader";
import { ChatInfoBar } from "./chatbot/ChatInfoBar";
import { ChatMessages } from "./chatbot/ChatMessages";
import { ChatInput } from "./chatbot/ChatInput";
import { useChat } from "./chatbot/useChat";
import { ChatMessage, FeedbackMode, ConversationModeType } from "./chatbot/types";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ChatPerformance<PERSON>hart } from "./chatbot/ChatPerformanceChart";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { FeedbackModeSelector } from "./chatbot/FeedbackModeSelector";

interface ChatbotModeProps {
  initialAnalysis: AnalysisResult;
  apiKey: string;
  selectedModel: string;
  onBack: () => void;
  onSaveChat: (messages: ChatMessage[], title: string, persona?: CharacterPersona | null) => void;
}

// Add our modes:
type ConversationModeType = "single-question" | "cycle-answers" | "cycle-followups";

export const ChatbotMode: React.FC<ChatbotModeProps> = ({
  initialAnalysis,
  apiKey,
  selectedModel,
  onBack,
  onSaveChat,
}) => {
  const { toast } = useToast();
  const openModal = useUIStore((state) => state.openModal);

  const [selectedPersona, setSelectedPersona] = useState<CharacterPersona | null>(
    initialAnalysis.characterPersona || null
  );
  const [isLiveFeedbackEnabled, setIsLiveFeedbackEnabled] = useState(false);
  const [selectedMessageIds, setSelectedMessageIds] = useState<string[]>([]);
  // Conversation mode state
  const [conversationMode, setConversationMode] = useState<ConversationModeType>("single-question");
  const [feedbackMode, setFeedbackMode] = useState<FeedbackMode>("general");
  const isPersonaSimulation = initialAnalysis.analysisType === 'character';

  const { messages, isLoading, sendMessage, rateMessage } = useChat({
    initialAnalysis,
    apiKey,
    selectedModel,
    selectedPersona,
    feedbackMode,
    conversationMode, // Pass conversationMode to useChat hook
  });

  const handleSelectMessage = useCallback((messageId: string) => {
    setSelectedMessageIds((prev) =>
      prev.includes(messageId) ? prev.filter((id) => id !== messageId) : [...prev, messageId]
    );
  }, []);

  const handleSelectPersona = useCallback(() => {
    openModal("CHARACTER_PERSONA", { 
      onPersonaSelect: (persona: CharacterPersona | null) => {
        setSelectedPersona(persona);
      }, 
      currentPersona: selectedPersona 
    });
  }, [openModal, selectedPersona]);

  const handleSaveChat = useCallback(() => {
    const chatTitle = `Chat: ${initialAnalysis.question.substring(0, 50)}${initialAnalysis.question.length > 50 ? '...' : ''}`;

    let messagesToSave = messages;
    if (selectedMessageIds.length > 0) {
      const selectedAndContextMessages = new Set<string>();
      messages.forEach((msg, index) => {
        if (selectedMessageIds.includes(msg.id)) {
          selectedAndContextMessages.add(msg.id);
          if (index > 0 && messages[index - 1].role === 'user') {
            selectedAndContextMessages.add(messages[index - 1].id);
          }
        }
      });
      messagesToSave = messages.filter(m => selectedAndContextMessages.has(m.id));
    }

    if (messagesToSave.length === 0) {
      toast({
        title: "No Messages to Save",
        description: "Please select at least one response to save, or deselect all to save the full chat.",
        variant: "destructive",
      });
      return;
    }

    onSaveChat(messagesToSave, chatTitle, selectedPersona);
    toast({
      title: "Chat Saved",
      description: "Your chat conversation has been saved to the library.",
    });
    setSelectedMessageIds([]);
  }, [messages, selectedMessageIds, initialAnalysis.question, onSaveChat, selectedPersona, toast]);

  const hasRatings = messages.some(m => m.performanceScore !== undefined);

  return (
    <div className="max-w-5xl mx-auto space-y-6 p-4">
      <ChatHeader
        onBack={onBack}
        onSaveChat={handleSaveChat}
        onSelectPersona={handleSelectPersona}
        selectedPersona={selectedPersona}
      />

      {/* Conversation Mode Switcher */}
      <div className="flex items-center justify-end gap-4 p-4 bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl border border-slate-200 shadow-sm flex-wrap">
        {!isPersonaSimulation && (
            <div className="flex items-center">
                <Label htmlFor="conversation-mode" className="text-sm font-semibold text-slate-700 mr-3">
                Conversation Mode:
                </Label>
                <Select
                    value={conversationMode}
                    onValueChange={(val: ConversationModeType) => setConversationMode(val)}
                >
                    <SelectTrigger id="conversation-mode" className="w-[240px] bg-white border-slate-300 font-medium">
                    <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                    <SelectItem value="single-question">Stay on Single Question</SelectItem>
                    <SelectItem value="cycle-answers">Cycle Through All Answers</SelectItem>
                    <SelectItem value="cycle-followups">Cycle Through All Follow-Ups</SelectItem>
                    </SelectContent>
                </Select>
            </div>
        )}
        {isLiveFeedbackEnabled && <FeedbackModeSelector value={feedbackMode} onValueChange={(v) => setFeedbackMode(v as FeedbackMode)} />}
        <div className="flex items-center space-x-3 bg-white px-4 py-2 rounded-lg border border-slate-300">
          <Label htmlFor="live-feedback-switch" className="text-sm font-semibold text-slate-700">
            Enable Live Feedback
          </Label>
          <Switch
            id="live-feedback-switch"
            checked={isLiveFeedbackEnabled}
            onCheckedChange={setIsLiveFeedbackEnabled}
          />
        </div>
      </div>

      <Card className="shadow-xl border-0 rounded-2xl overflow-hidden bg-white">
        <CardHeader className="pb-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <CardTitle className="text-xl flex items-center font-semibold">
            <MessageSquare className="h-6 w-6 mr-3" />
            Focused Analysis: "{initialAnalysis.question}"
          </CardTitle>
          <div className="mt-3">
            <ChatInfoBar
              initialAnalysis={initialAnalysis}
              selectedModel={selectedModel}
              selectedPersona={selectedPersona}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          <ChatMessages
            messages={messages}
            isLoading={isLoading}
            isLiveFeedbackEnabled={isLiveFeedbackEnabled}
            onSelectMessage={handleSelectMessage}
            isSelected={(id) => selectedMessageIds.includes(id)}
          />
          <ChatInput
            onSendMessage={sendMessage}
            isLoading={isLoading}
            apiKey={apiKey}
          />
        </CardContent>
      </Card>

      {isLiveFeedbackEnabled && hasRatings && (
        <div className="mt-6">
          <ChatPerformanceChart messages={messages} />
        </div>
      )}

      <CharacterPersonaModal />
    </div>
  );
};
