import * as React from 'react';
import {
  MousePointer2,
  Square,
  Circle,
  Minus,
  ArrowRight,
  Type,
  Pen,
  Hexagon,
  Star,
  Image,
  Undo2,
  Redo2,
  ZoomIn,
  ZoomOut,
  Grid3X3,
  Move3D,
  Download
} from 'lucide-react';
import { DrawingTool, ExportOptions, ImportOptions } from '@/types/figma';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { cn } from '@/lib/utils';
import { ExportImportDialog } from './export/ExportImportDialog';
import type { fabric } from 'fabric';

interface FigmaToolbarProps {
  className?: string;
  canvas?: fabric.Canvas | null;
}

export const FigmaToolbar: React.FC<FigmaToolbarProps> = ({ className, canvas }) => {
  const {
    activeTool,
    zoom,
    gridVisible,
    snapToGrid,
    selectedObjectIds,
    objects,
    layers,
    setActiveTool,
    setZoom,
    toggleGrid,
    toggleSnapToGrid,
    undo,
    redo,
    resetView,
    fitToScreen,
    exportCanvas,
    importImage,
  } = useFigmaCanvasStore();

  const tools: Array<{
    tool: DrawingTool;
    icon: React.ComponentType<{ className?: string }>;
    label: string;
    shortcut?: string;
  }> = [
    { tool: 'select', icon: MousePointer2, label: 'Select', shortcut: 'V' },
    { tool: 'rectangle', icon: Square, label: 'Rectangle', shortcut: 'R' },
    { tool: 'circle', icon: Circle, label: 'Circle', shortcut: 'O' },
    { tool: 'line', icon: Minus, label: 'Line', shortcut: 'L' },
    { tool: 'arrow', icon: ArrowRight, label: 'Arrow', shortcut: 'A' },
    { tool: 'text', icon: Type, label: 'Text', shortcut: 'T' },
    { tool: 'pen', icon: Pen, label: 'Pen', shortcut: 'P' },
    { tool: 'polygon', icon: Hexagon, label: 'Polygon', shortcut: 'Y' },
    { tool: 'star', icon: Star, label: 'Star', shortcut: 'S' },
    { tool: 'image', icon: Image, label: 'Image', shortcut: 'I' },
  ];

  const handleZoomIn = () => {
    setZoom(Math.min(10, zoom * 1.2));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(0.1, zoom / 1.2));
  };

  const formatZoom = (zoom: number) => {
    return `${Math.round(zoom * 100)}%`;
  };

  const handleExport = async (format: string, options: ExportOptions, filename?: string) => {
    if (!canvas) {
      console.error('Canvas not available for export');
      return;
    }

    try {
      // Use the ExportManager to handle exports
      const { ExportManager } = await import('./export/ExportManager');
      const exportManager = new ExportManager(canvas);

      await exportManager.exportAndDownload(format as ExportFormat, options, filename);
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  };

  const handleImport = async (files: FileList, options: ImportOptions) => {
    if (!canvas) {
      console.error('Canvas not available for import');
      return;
    }

    try {
      // Use the ImportManager to handle imports
      const { ImportManager } = await import('./import/ImportManager');
      const importManager = new ImportManager(canvas, useFigmaCanvasStore.getState());

      await importManager.handleFileDrop(files, options);
    } catch (error) {
      console.error('Import failed:', error);
      throw error;
    }
  };

  return (
    <div className={cn(
      "flex items-center gap-2 p-2 bg-white border border-gray-200 rounded-lg shadow-sm",
      className
    )}>
      {/* Drawing Tools */}
      <div className="flex items-center gap-1">
        {tools.map(({ tool, icon: Icon, label, shortcut }) => (
          <Button
            key={tool}
            variant={activeTool === tool ? "default" : "ghost"}
            size="sm"
            onClick={() => setActiveTool(tool)}
            className={cn(
              "w-8 h-8 p-0",
              activeTool === tool && "bg-blue-500 text-white hover:bg-blue-600"
            )}
            title={`${label}${shortcut ? ` (${shortcut})` : ''}`}
          >
            <Icon className="w-4 h-4" />
          </Button>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* History Controls */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={undo}
          className="w-8 h-8 p-0"
          title="Undo (Ctrl+Z)"
        >
          <Undo2 className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={redo}
          className="w-8 h-8 p-0"
          title="Redo (Ctrl+Y)"
        >
          <Redo2 className="w-4 h-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Zoom Controls */}
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomOut}
          className="w-8 h-8 p-0"
          title="Zoom Out (Ctrl+-)"
        >
          <ZoomOut className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={resetView}
          className="px-2 h-8 text-xs font-mono min-w-[50px]"
          title="Reset Zoom (Ctrl+0)"
        >
          {formatZoom(zoom)}
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleZoomIn}
          className="w-8 h-8 p-0"
          title="Zoom In (Ctrl++)"
        >
          <ZoomIn className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={fitToScreen}
          className="w-8 h-8 p-0"
          title="Fit to Screen (Ctrl+1)"
        >
          <Move3D className="w-4 h-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Grid Controls */}
      <div className="flex items-center gap-1">
        <Button
          variant={gridVisible ? "default" : "ghost"}
          size="sm"
          onClick={toggleGrid}
          className={cn(
            "w-8 h-8 p-0",
            gridVisible && "bg-blue-500 text-white hover:bg-blue-600"
          )}
          title="Toggle Grid"
        >
          <Grid3X3 className="w-4 h-4" />
        </Button>
        
        <Button
          variant={snapToGrid ? "default" : "ghost"}
          size="sm"
          onClick={toggleSnapToGrid}
          className={cn(
            "px-2 h-8 text-xs",
            snapToGrid && "bg-blue-500 text-white hover:bg-blue-600"
          )}
          title="Toggle Snap to Grid"
        >
          Snap
        </Button>
      </div>

      {/* Export/Import */}
      <Separator orientation="vertical" className="h-6" />
      <ExportImportDialog
        onExport={handleExport}
        onImport={handleImport}
      />

      {/* Selection Info */}
      {selectedObjectIds.length > 0 && (
        <>
          <Separator orientation="vertical" className="h-6" />
          <div className="text-xs text-gray-500 px-2">
            {selectedObjectIds.length} selected
          </div>
        </>
      )}
    </div>
  );
};

// Keyboard shortcuts hook
export const useFigmaKeyboardShortcuts = () => {
  const { setActiveTool, undo, redo, resetView, fitToScreen } = useFigmaCanvasStore();

  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle shortcuts when not in input fields
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Handle tool shortcuts
      if (!e.ctrlKey && !e.metaKey && !e.altKey) {
        switch (e.key.toLowerCase()) {
          case 'v':
            e.preventDefault();
            setActiveTool('select');
            break;
          case 'r':
            e.preventDefault();
            setActiveTool('rectangle');
            break;
          case 'o':
            e.preventDefault();
            setActiveTool('circle');
            break;
          case 'l':
            e.preventDefault();
            setActiveTool('line');
            break;
          case 'a':
            e.preventDefault();
            setActiveTool('arrow');
            break;
          case 't':
            e.preventDefault();
            setActiveTool('text');
            break;
          case 'p':
            e.preventDefault();
            setActiveTool('pen');
            break;
          case 'y':
            e.preventDefault();
            setActiveTool('polygon');
            break;
          case 's':
            e.preventDefault();
            setActiveTool('star');
            break;
          case 'i':
            e.preventDefault();
            setActiveTool('image');
            break;
        }
      }

      // Handle system shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch (e.key.toLowerCase()) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              redo();
            } else {
              undo();
            }
            break;
          case 'y':
            e.preventDefault();
            redo();
            break;
          case '0':
            e.preventDefault();
            resetView();
            break;
          case '1':
            e.preventDefault();
            fitToScreen();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [setActiveTool, undo, redo, resetView, fitToScreen]);
};
