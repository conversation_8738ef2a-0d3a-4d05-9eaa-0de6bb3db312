#!/usr/bin/env bash
# Run static application security testing (SAST) using CodeQL (GitHub CLI required)
set -e

if ! command -v codeql &> /dev/null; then
  echo "CodeQL CLI not found. Please install CodeQL CLI."
  exit 1
fi

codeql database create codeql-db --language=javascript --source-root=..
codeql database analyze codeql-db codeql-repo/javascript-queries --format=sarifv2.1.0 --output=codeql-results.sarif

if grep -q '"level": "error"' codeql-results.sarif; then
  echo "SAST scan found errors. Review codeql-results.sarif."
  exit 1
else
  echo "SAST scan passed."
fi
