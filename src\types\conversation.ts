import { ParsedOutput } from "@/services/openRouter/types/parsedOutput";
import { QuestionContext } from "@/components/conversation-planner/QuestionContextSelector";

export type ConversationStyle = "casual" | "professional" | "academic" | "scientific" | "medical" | "dating" | "marketing" | "copywriting";
export type Gender = 'male' | 'female' | 'neutral';

export interface DatingContext {
  userGender: Gender;
  partnerGender: Gender;
}

export interface ConversationSettings {
  style: ConversationStyle;
  openRouterApiKey: string;
  selectedModel: string;
  numberOfAnswers: number;
  analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles';
  selectedEmotions?: string[];
}

// Unify the Refinement types
export interface RefinementResult {
  id: string;
  prompt: string;
  result: string;
  timestamp: Date;
}

// Use a single AnalysisResult type everywhere (authoritative)
export interface AnalysisResult {
  id: string;
  question: string;
  style: ConversationStyle;
  model: string;
  analysis: string;
  parsedOutput?: ParsedOutput; // Ensure ParsedOutput is correctly typed and exported from its source
  timestamp: Date;
  followUpQuestions?: string[];
  rating?: number;
  refinements?: RefinementResult[];
  analysisType: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles'; // required
  characterPersona?: CharacterPersona;
  seedContext?: string;
  questionContext?: QuestionContext;
  datingContext?: DatingContext;
  selectedEmotions?: string[];
  archived?: boolean;
  archivedAt?: string;
}

// These are unchanged
export interface SavedAnalysis {
  id: string;
  title: string;
  results: AnalysisResult[];
  createdAt: Date;
  updatedAt: Date;
  folderId?: string;
}

export interface SavedFolder {
  id: string;
  name: string;
  createdAt: Date;
}

export interface UserNote {
  id: string;
  questionId: string;
  noteText: string;
  linkedAnswerIds: string[];
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  originalAnalysis?: string;
  analysisType?: 'multiple' | 'deep' | 'character' | 'pros-cons' | 'six-hats' | 'emotional-angles';
}

export interface CharacterPersona {
  id: string;
  name: string;
  coreRole: string;
  personalityTraits: string[];
  background: string;
  communicationStyle: string;
  expertise: string[];
  biasesWorldview: string;
  createdAt: Date;
  updatedAt: Date;
  openMindedness?: number;
  gender?: Gender;
}

// Export ParsedOutput if it's defined here or ensure it's exported from its original file.
// For now, assuming it's correctly handled by the import from "@/services/openRouter/types/parsedOutput";
// If not, it might need to be re-exported here or defined here if this is its intended source of truth.

// Custom annotation for future maintainers:
// This file is the **single source of truth** for all app types.
// DO NOT use src/types/domain.ts for React/feature code types.
