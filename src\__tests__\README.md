# Testing Suite Documentation

This comprehensive testing suite covers all aspects of the ChatCraft Trainer Pro application, including unit tests, integration tests, and end-to-end tests.

## Test Structure

```
src/__tests__/
├── components/           # Unit tests for React components
├── integration/         # Integration tests for system interactions
├── e2e/                # End-to-end tests for complete workflows
├── setup/              # Test configuration and utilities
└── README.md           # This documentation
```

## Test Types

### Unit Tests
- **Location**: `src/__tests__/components/`
- **Framework**: Vitest + React Testing Library
- **Purpose**: Test individual components in isolation
- **Coverage**: Components, hooks, utilities, services

### Integration Tests
- **Location**: `src/__tests__/integration/`
- **Framework**: Vitest + React Testing Library
- **Purpose**: Test component interactions and data flow
- **Coverage**: Canvas integration, state management, API interactions

### End-to-End Tests
- **Location**: `src/__tests__/e2e/`
- **Framework**: Playwright
- **Purpose**: Test complete user workflows
- **Coverage**: Full application workflows, cross-browser compatibility

## Running Tests

### All Tests
```bash
npm run test:all
```

### Unit Tests Only
```bash
npm run test:unit
```

### Integration Tests Only
```bash
npm run test:integration
```

### End-to-End Tests Only
```bash
npm run test:e2e
```

### Watch Mode (Development)
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

### Performance Tests
```bash
npm run test:performance
```

### Accessibility Tests
```bash
npm run test:a11y
```

## Test Configuration

### Vitest Configuration
- **File**: `vitest.config.ts`
- **Environment**: jsdom
- **Coverage**: v8 provider with 80% threshold
- **Parallel**: Multi-threaded execution

### Playwright Configuration
- **File**: `playwright.config.ts`
- **Browsers**: Chrome, Firefox, Safari, Edge
- **Devices**: Desktop, mobile, tablet
- **Features**: Screenshots, videos, traces on failure

## Test Utilities

### Mock Data Factories
```typescript
import { 
  createMockAnalysisResult,
  createMockDesignElement,
  createMockPresentation,
  createMockNodeData 
} from '@/__tests__/setup/testSetup';

const mockAnalysis = createMockAnalysisResult({
  question: 'Custom question',
  analysisType: 'pros-cons'
});
```

### Performance Testing
```typescript
import { measurePerformance } from '@/__tests__/setup/testSetup';

await measurePerformance(async () => {
  // Test code here
}, 'Test operation');
```

### Accessibility Testing
```typescript
import { checkAccessibility } from '@/__tests__/setup/testSetup';

const issues = await checkAccessibility(container);
expect(issues).toHaveLength(0);
```

## Test Patterns

### Component Testing
```typescript
describe('ComponentName', () => {
  it('renders with default props', () => {
    render(<ComponentName />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('handles user interactions', async () => {
    const user = userEvent.setup();
    render(<ComponentName />);
    
    await user.click(screen.getByRole('button'));
    expect(mockFunction).toHaveBeenCalled();
  });
});
```

### Integration Testing
```typescript
describe('Feature Integration', () => {
  it('completes full workflow', async () => {
    render(<IntegratedComponent />);
    
    // Step 1: Initial action
    await user.click(screen.getByTestId('start-button'));
    
    // Step 2: Verify intermediate state
    expect(screen.getByTestId('progress')).toBeVisible();
    
    // Step 3: Complete workflow
    await waitFor(() => {
      expect(screen.getByTestId('result')).toBeVisible();
    });
  });
});
```

### E2E Testing
```typescript
test('complete user workflow', async ({ page }) => {
  await test.step('Navigate to app', async () => {
    await page.goto('/');
    await expect(page).toHaveTitle(/ChatCraft/);
  });

  await test.step('Perform action', async () => {
    await page.click('[data-testid="action-button"]');
    await expect(page.locator('[data-testid="result"]')).toBeVisible();
  });
});
```

## Coverage Requirements

### Global Thresholds
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

### Critical Components
- **Visual Analysis**: 85%
- **Services**: 90%
- **Integration Points**: 85%

## Continuous Integration

### GitHub Actions
Tests run automatically on:
- Pull requests
- Pushes to main branch
- Scheduled nightly runs

### Test Reports
- **Unit/Integration**: HTML and JSON reports
- **E2E**: Playwright HTML report with traces
- **Coverage**: LCOV format for CI integration

## Debugging Tests

### Unit/Integration Tests
```bash
# Run specific test file
npm run test:unit -- ComponentName.test.tsx

# Run with debugging
npm run test:debug

# Run with UI
npm run test:ui
```

### E2E Tests
```bash
# Run in headed mode
npm run test:e2e -- --headed

# Run specific test
npm run test:e2e -- --grep "workflow name"

# Debug mode
npm run test:e2e:debug
```

## Best Practices

### Test Organization
1. Group related tests in describe blocks
2. Use descriptive test names
3. Follow AAA pattern (Arrange, Act, Assert)
4. Keep tests focused and independent

### Mock Strategy
1. Mock external dependencies
2. Use real implementations for internal code
3. Mock at the boundary (API calls, browser APIs)
4. Avoid over-mocking

### Data Management
1. Use factories for test data
2. Clean up after each test
3. Use realistic test data
4. Avoid shared mutable state

### Performance
1. Run tests in parallel when possible
2. Use selective test execution
3. Optimize test setup/teardown
4. Monitor test execution time

## Troubleshooting

### Common Issues

#### Tests Timeout
- Increase timeout in test configuration
- Check for infinite loops or hanging promises
- Verify mock implementations

#### Flaky Tests
- Add proper wait conditions
- Avoid timing-dependent assertions
- Use deterministic test data

#### Memory Leaks
- Clean up event listeners
- Clear timers and intervals
- Dispose of resources properly

#### CI Failures
- Check environment differences
- Verify test isolation
- Review CI-specific configuration

### Getting Help
1. Check test logs and error messages
2. Review test configuration files
3. Consult framework documentation
4. Ask team members for assistance

## Maintenance

### Regular Tasks
- Update test dependencies
- Review and update test data
- Monitor test performance
- Clean up obsolete tests

### Test Metrics
- Track test execution time
- Monitor coverage trends
- Identify flaky tests
- Review test effectiveness

### Documentation
- Keep test documentation updated
- Document complex test scenarios
- Maintain troubleshooting guides
- Share testing best practices
