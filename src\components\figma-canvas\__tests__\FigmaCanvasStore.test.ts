// Mock IndexedDB
const mockIndexedDB = {
  init: vi.fn().mockResolvedValue(undefined),
  saveCanvas: vi.fn().mockResolvedValue('test-id'),
  getCanvas: vi.fn().mockResolvedValue(null),
  autoSave: vi.fn().mockResolvedValue(undefined),
  getAutoSave: vi.fn().mockResolvedValue(null),
  clearAutoSave: vi.fn().mockResolvedValue(undefined),
};

vi.mock('../../../lib/storage/IndexedDBManager', () => ({
  indexedDBManager: mockIndexedDB,
}));

import { render } from '@/utils/test-utils';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useFigmaCanvasStore } from '../../../stores/useFigmaCanvasStore';
import { DrawingToolsFactory } from '../tools/DrawingToolsFactory';

describe('FigmaCanvasStore', () => {
  beforeEach(() => {
    // Reset store state
    const store = useFigmaCanvasStore.getState();
    store.objects = {};
    store.layers = {};
    store.selectedObjectIds = [];
    store.history = [];
    store.historyIndex = -1;
    
    // Create default layer
    const defaultLayerId = store.addLayer('Layer 1');
    store.setActiveLayer(defaultLayerId);
    
    vi.clearAllMocks();
  });

  describe('Object Management', () => {
    it('should add a new object', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);

      expect(objectId).toBeDefined();
      expect(store.objects[objectId]).toBeDefined();
      expect(store.objects[objectId].type).toBe('rectangle');
      expect(store.objects[objectId].transform.width).toBe(100);
      expect(store.objects[objectId].transform.height).toBe(100);
    });

    it('should update an object', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      
      store.updateObject(objectId, {
        style: { fill: '#ff0000' }
      });

      expect(store.objects[objectId].style.fill).toBe('#ff0000');
    });

    it('should delete an object', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      expect(store.objects[objectId]).toBeDefined();

      store.deleteObject(objectId);
      expect(store.objects[objectId]).toBeUndefined();
    });

    it('should duplicate an object', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const originalId = store.addObject(rectangle);
      const duplicateId = store.duplicateObject(originalId);

      expect(duplicateId).toBeDefined();
      expect(duplicateId).not.toBe(originalId);
      expect(store.objects[duplicateId]).toBeDefined();
      expect(store.objects[duplicateId].type).toBe('rectangle');
      expect(store.objects[duplicateId].transform.x).toBe(20); // Offset by 20
      expect(store.objects[duplicateId].transform.y).toBe(20); // Offset by 20
    });
  });

  describe('Selection Management', () => {
    it('should select objects', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      store.selectObjects([objectId]);

      expect(store.selectedObjectIds).toContain(objectId);
    });

    it('should clear selection', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      store.selectObjects([objectId]);
      expect(store.selectedObjectIds).toContain(objectId);

      store.clearSelection();
      expect(store.selectedObjectIds).toHaveLength(0);
    });

    it('should add to selection', () => {
      const store = useFigmaCanvasStore.getState();
      const rect1 = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );
      const rect2 = DrawingToolsFactory.createRectangle(
        { x: 200, y: 200 },
        { x: 300, y: 300 },
        store.activeLayerId
      );

      const id1 = store.addObject(rect1);
      const id2 = store.addObject(rect2);

      store.selectObjects([id1]);
      store.addToSelection(id2);

      expect(store.selectedObjectIds).toContain(id1);
      expect(store.selectedObjectIds).toContain(id2);
      expect(store.selectedObjectIds).toHaveLength(2);
    });
  });

  describe('Layer Management', () => {
    it('should add a new layer', () => {
      const store = useFigmaCanvasStore.getState();
      const layerId = store.addLayer('Test Layer');

      expect(layerId).toBeDefined();
      expect(store.layers[layerId]).toBeDefined();
      expect(store.layers[layerId].name).toBe('Test Layer');
    });

    it('should update a layer', () => {
      const store = useFigmaCanvasStore.getState();
      const layerId = store.addLayer('Test Layer');

      store.updateLayer(layerId, {
        visible: false,
        name: 'Updated Layer'
      });

      expect(store.layers[layerId].visible).toBe(false);
      expect(store.layers[layerId].name).toBe('Updated Layer');
    });

    it('should delete a layer', () => {
      const store = useFigmaCanvasStore.getState();
      const layerId = store.addLayer('Test Layer');
      expect(store.layers[layerId]).toBeDefined();

      store.deleteLayer(layerId);
      expect(store.layers[layerId]).toBeUndefined();
    });

    it('should not delete the last layer', () => {
      const store = useFigmaCanvasStore.getState();
      const layerCount = Object.keys(store.layers).length;
      
      // Try to delete the only layer
      store.deleteLayer(store.activeLayerId);
      
      // Should still have the same number of layers
      expect(Object.keys(store.layers)).toHaveLength(layerCount);
    });
  });

  describe('Transform Operations', () => {
    it('should move objects', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      const originalX = store.objects[objectId].transform.x;
      const originalY = store.objects[objectId].transform.y;

      store.moveObjects([objectId], { x: 50, y: 30 });

      expect(store.objects[objectId].transform.x).toBe(originalX + 50);
      expect(store.objects[objectId].transform.y).toBe(originalY + 30);
    });

    it('should resize objects', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);

      store.resizeObjects([objectId], { width: 200, height: 150 });

      expect(store.objects[objectId].transform.width).toBe(200);
      expect(store.objects[objectId].transform.height).toBe(150);
    });

    it('should rotate objects', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      const originalRotation = store.objects[objectId].transform.rotation;

      store.rotateObjects([objectId], 45);

      expect(store.objects[objectId].transform.rotation).toBe(originalRotation + 45);
    });
  });

  describe('History Management', () => {
    it('should save to history when adding objects', () => {
      const store = useFigmaCanvasStore.getState();
      const initialHistoryLength = store.history.length;

      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      store.addObject(rectangle);

      expect(store.history.length).toBeGreaterThan(initialHistoryLength);
    });

    it('should undo operations', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      expect(store.objects[objectId]).toBeDefined();

      store.undo();
      expect(store.objects[objectId]).toBeUndefined();
    });

    it('should redo operations', () => {
      const store = useFigmaCanvasStore.getState();
      const rectangle = DrawingToolsFactory.createRectangle(
        { x: 0, y: 0 },
        { x: 100, y: 100 },
        store.activeLayerId
      );

      const objectId = store.addObject(rectangle);
      store.undo();
      expect(store.objects[objectId]).toBeUndefined();

      store.redo();
      expect(store.objects[objectId]).toBeDefined();
    });
  });

  describe('Canvas Operations', () => {
    it('should set zoom level', () => {
      const store = useFigmaCanvasStore.getState();
      
      store.setZoom(2);
      expect(store.zoom).toBe(2);

      // Should clamp to valid range
      store.setZoom(20);
      expect(store.zoom).toBe(10); // Max zoom

      store.setZoom(-1);
      expect(store.zoom).toBe(0.1); // Min zoom
    });

    it('should set pan position', () => {
      const store = useFigmaCanvasStore.getState();
      
      store.setPan({ x: 100, y: 200 });
      expect(store.pan.x).toBe(100);
      expect(store.pan.y).toBe(200);
    });

    it('should toggle grid visibility', () => {
      const store = useFigmaCanvasStore.getState();
      const initialGridVisible = store.gridVisible;
      
      store.toggleGrid();
      expect(store.gridVisible).toBe(!initialGridVisible);
    });

    it('should toggle snap to grid', () => {
      const store = useFigmaCanvasStore.getState();
      const initialSnapToGrid = store.snapToGrid;
      
      store.toggleSnapToGrid();
      expect(store.snapToGrid).toBe(!initialSnapToGrid);
    });
  });

  describe('Offline Storage', () => {
    it('should save to IndexedDB', async () => {
      const store = useFigmaCanvasStore.getState();
      
      await store.saveToIndexedDB('Test Canvas');
      
      expect(mockIndexedDB.init).toHaveBeenCalled();
      expect(mockIndexedDB.saveCanvas).toHaveBeenCalledWith({
        name: 'Test Canvas',
        canvasState: expect.any(Object),
      });
    });

    it('should auto-save', async () => {
      const store = useFigmaCanvasStore.getState();
      
      await store.autoSave();
      
      expect(mockIndexedDB.init).toHaveBeenCalled();
      expect(mockIndexedDB.autoSave).toHaveBeenCalledWith(expect.any(Object));
    });

    it('should load auto-save', async () => {
      const store = useFigmaCanvasStore.getState();
      mockIndexedDB.getAutoSave.mockResolvedValueOnce({
        canvasState: {
          objects: { 'test-id': { id: 'test-id', type: 'rectangle' } },
          layers: {},
          selectedObjectIds: [],
          activeLayerId: 'layer-1',
          activeTool: 'select',
          zoom: 1,
          pan: { x: 0, y: 0 },
          gridVisible: true,
          snapToGrid: true,
          gridSize: 20,
          canvasSize: { width: 1920, height: 1080 },
          backgroundColor: '#ffffff',
        },
        timestamp: new Date(),
      });
      
      const result = await store.loadAutoSave();
      
      expect(result).toBe(true);
      expect(mockIndexedDB.init).toHaveBeenCalled();
      expect(mockIndexedDB.getAutoSave).toHaveBeenCalled();
      expect(store.objects['test-id']).toBeDefined();
    });
  });
});

// No render import or usage from @testing-library/react in this file, so no changes needed.
