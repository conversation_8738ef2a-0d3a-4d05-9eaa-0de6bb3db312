/**
 * Button Component
 * 
 * A comprehensive button component that follows the design system.
 * Supports multiple variants, sizes, and states.
 */

import * as React from 'react';
import { buttonVariants, type ButtonVariants } from '../variants';
import { cn } from '../../utils/cn';
import { Slot } from '@radix-ui/react-slot';
import { Loader2 } from 'lucide-react';

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    ButtonVariants {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      asChild = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';
    const isDisabled = disabled || loading;

    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, fullWidth, loading }),
          className
        )}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex items-center">{leftIcon}</span>
        )}
        {loading && loadingText ? loadingText : children}
        {!loading && rightIcon && (
          <span className="ml-2 flex items-center">{rightIcon}</span>
        )}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

// Button group component for related actions
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: ButtonVariants['size'];
  variant?: ButtonVariants['variant'];
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size,
  variant,
}) => {
  const groupClasses = cn(
    'inline-flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    '[&>button]:rounded-none',
    '[&>button:first-child]:rounded-l-md',
    '[&>button:last-child]:rounded-r-md',
    orientation === 'vertical' ? '[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none [&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none' : '',
    '[&>button:not(:first-child)]:border-l-0',
    orientation === 'vertical' ? '[&>button:not(:first-child)]:border-t-0' : '',
    className
  );

  return (
    <div className={groupClasses}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Button) {
          const extraProps: Record<string, unknown> = {};
          if (size && child.props.size === undefined) extraProps.size = size;
          if (variant && child.props.variant === undefined) extraProps.variant = variant;
          return React.cloneElement(child, extraProps);
        }
        return child;
      })}
    </div>
  );
};

// Icon button component for actions with only icons
export interface IconButtonProps
  extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'icon', ...props }, ref) => {
    return (
      <Button
        ref={ref}
        size={size}
        className={cn('shrink-0', className)}
        {...props}
      >
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Floating action button component
export interface FABProps extends Omit<ButtonProps, 'variant' | 'size'> {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'sm' | 'md' | 'lg';
}

export const FAB = React.forwardRef<HTMLButtonElement, FABProps>(
  ({ className, position = 'bottom-right', size = 'md', ...props }, ref) => {
    const positionClasses = {
      'bottom-right': 'fixed bottom-6 right-6',
      'bottom-left': 'fixed bottom-6 left-6',
      'top-right': 'fixed top-6 right-6',
      'top-left': 'fixed top-6 left-6',
    };

    const sizeClasses = {
      sm: 'h-12 w-12',
      md: 'h-14 w-14',
      lg: 'h-16 w-16',
    };

    return (
      <Button
        ref={ref}
        variant="primary"
        className={cn(
          positionClasses[position],
          sizeClasses[size],
          'rounded-full shadow-lg hover:shadow-xl z-50',
          'transition-all duration-200',
          className
        )}
        {...props}
      />
    );
  }
);

FAB.displayName = 'FAB';

// Export types
export type { ButtonVariants };
