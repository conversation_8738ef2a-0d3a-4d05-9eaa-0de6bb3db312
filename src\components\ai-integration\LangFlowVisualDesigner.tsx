import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, 
  Plus, 
  Play, 
  Pause,
  Settings,
  Link,
  Trash2,
  Copy,
  Download,
  Upload,
  Zap,
  MessageSquare,
  Database,
  Filter,
  Shuffle,
  Target,
  GitBranch,
  Code,
  FileText,
  Image,
  Mic,
  Video
} from 'lucide-react';

export interface LangFlowNode {
  id: string;
  type: 'input' | 'llm' | 'prompt' | 'chain' | 'memory' | 'tool' | 'output' | 'condition' | 'transform';
  position: { x: number; y: number };
  data: {
    label: string;
    config: Record<string, unknown>;
    inputs: Array<{ name: string; type: string; required: boolean }>;
    outputs: Array<{ name: string; type: string }>;
    metadata?: Record<string, unknown>;
  };
  connections: {
    inputs: Record<string, string>; // input_name -> source_node_id:output_name
    outputs: Record<string, string[]>; // output_name -> [target_node_id:input_name]
  };
}

export interface LangFlowWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: LangFlowNode[];
  metadata: {
    version: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    category: string;
  };
  settings: {
    maxTokens: number;
    temperature: number;
    timeout: number;
    retryCount: number;
  };
}

interface LangFlowVisualDesignerProps {
  workflow?: LangFlowWorkflow;
  onSave?: (workflow: LangFlowWorkflow) => void;
  onExecute?: (workflow: LangFlowWorkflow) => void;
  onExport?: (workflow: LangFlowWorkflow) => void;
  className?: string;
}

export const LangFlowVisualDesigner: React.FC<LangFlowVisualDesignerProps> = ({
  workflow,
  onSave,
  onExecute,
  onExport,
  className,
}) => {
  const [currentWorkflow, setCurrentWorkflow] = useState<LangFlowWorkflow>(
    workflow || {
      id: `workflow_${Date.now()}`,
      name: 'New Workflow',
      description: '',
      nodes: [],
      metadata: {
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        category: 'general',
      },
      settings: {
        maxTokens: 2048,
        temperature: 0.7,
        timeout: 30000,
        retryCount: 3,
      },
    }
  );

  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState<{ nodeId: string; output: string } | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResults, setExecutionResults] = useState<Record<string, unknown>>({});

  const canvasRef = useRef<HTMLDivElement>(null);

  // Node type configurations
  const nodeTypes: Array<{
    type: LangFlowNode['type'];
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    description: string;
    defaultConfig: Record<string, unknown>;
  }> = [
    {
      type: 'input',
      label: 'Input',
      icon: MessageSquare,
      color: 'bg-blue-100 border-blue-300',
      description: 'Data input node',
      defaultConfig: { inputType: 'text', placeholder: 'Enter input...' },
    },
    {
      type: 'llm',
      label: 'LLM',
      icon: Brain,
      color: 'bg-purple-100 border-purple-300',
      description: 'Large Language Model',
      defaultConfig: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000 },
    },
    {
      type: 'prompt',
      label: 'Prompt',
      icon: FileText,
      color: 'bg-green-100 border-green-300',
      description: 'Prompt template',
      defaultConfig: { template: 'You are a helpful assistant. {input}' },
    },
    {
      type: 'chain',
      label: 'Chain',
      icon: Link,
      color: 'bg-yellow-100 border-yellow-300',
      description: 'Chain multiple operations',
      defaultConfig: { chainType: 'sequential' },
    },
    {
      type: 'memory',
      label: 'Memory',
      icon: Database,
      color: 'bg-indigo-100 border-indigo-300',
      description: 'Conversation memory',
      defaultConfig: { memoryType: 'buffer', maxTokens: 2000 },
    },
    {
      type: 'tool',
      label: 'Tool',
      icon: Zap,
      color: 'bg-orange-100 border-orange-300',
      description: 'External tool integration',
      defaultConfig: { toolName: 'calculator', parameters: {} },
    },
    {
      type: 'condition',
      label: 'Condition',
      icon: GitBranch,
      color: 'bg-pink-100 border-pink-300',
      description: 'Conditional logic',
      defaultConfig: { condition: 'if', operator: 'equals', value: '' },
    },
    {
      type: 'transform',
      label: 'Transform',
      icon: Shuffle,
      color: 'bg-teal-100 border-teal-300',
      description: 'Data transformation',
      defaultConfig: { transformType: 'format', format: 'json' },
    },
    {
      type: 'output',
      label: 'Output',
      icon: Target,
      color: 'bg-red-100 border-red-300',
      description: 'Output result',
      defaultConfig: { outputType: 'text', format: 'plain' },
    },
  ];

  // Add new node
  const addNode = useCallback((type: LangFlowNode['type'], position: { x: number; y: number }) => {
    const nodeConfig = nodeTypes.find(nt => nt.type === type);
    if (!nodeConfig) return;

    const newNode: LangFlowNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      position,
      data: {
        label: nodeConfig.label,
        config: { ...nodeConfig.defaultConfig },
        inputs: getDefaultInputs(type),
        outputs: getDefaultOutputs(type),
      },
      connections: {
        inputs: {},
        outputs: {},
      },
    };

    setCurrentWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));

    setSelectedNodeId(newNode.id);
  }, []);

  // Get default inputs for node type
  const getDefaultInputs = (type: string): Array<{ name: string; type: string; required: boolean }> => {
    const inputConfigs: Record<string, Array<{ name: string; type: string; required: boolean }>> = {
      input: [],
      llm: [
        { name: 'prompt', type: 'string', required: true },
        { name: 'context', type: 'string', required: false },
      ],
      prompt: [
        { name: 'variables', type: 'object', required: false },
      ],
      chain: [
        { name: 'input', type: 'any', required: true },
      ],
      memory: [
        { name: 'message', type: 'string', required: true },
      ],
      tool: [
        { name: 'input', type: 'string', required: true },
        { name: 'parameters', type: 'object', required: false },
      ],
      condition: [
        { name: 'value', type: 'any', required: true },
      ],
      transform: [
        { name: 'data', type: 'any', required: true },
      ],
      output: [
        { name: 'result', type: 'any', required: true },
      ],
    };
    return inputConfigs[type] || [];
  };

  // Get default outputs for node type
  const getDefaultOutputs = (type: string): Array<{ name: string; type: string }> => {
    const outputConfigs: Record<string, Array<{ name: string; type: string }>> = {
      input: [{ name: 'value', type: 'string' }],
      llm: [{ name: 'response', type: 'string' }],
      prompt: [{ name: 'formatted', type: 'string' }],
      chain: [{ name: 'result', type: 'any' }],
      memory: [{ name: 'history', type: 'array' }],
      tool: [{ name: 'result', type: 'any' }],
      condition: [
        { name: 'true', type: 'any' },
        { name: 'false', type: 'any' },
      ],
      transform: [{ name: 'transformed', type: 'any' }],
      output: [],
    };
    return outputConfigs[type] || [];
  };

  // Update node
  const updateNode = useCallback((nodeId: string, updates: Partial<LangFlowNode>) => {
    setCurrentWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      ),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));
  }, []);

  // Delete node
  const deleteNode = useCallback((nodeId: string) => {
    setCurrentWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(node => node.id !== nodeId),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));
    setSelectedNodeId(null);
  }, []);

  // Start connection
  const startConnection = useCallback((nodeId: string, outputName: string) => {
    setIsConnecting(true);
    setConnectionSource({ nodeId, output: outputName });
  }, []);

  // Complete connection
  const completeConnection = useCallback((targetNodeId: string, inputName: string) => {
    if (!connectionSource || connectionSource.nodeId === targetNodeId) {
      setIsConnecting(false);
      setConnectionSource(null);
      return;
    }

    setCurrentWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => {
        if (node.id === connectionSource.nodeId) {
          // Update source node outputs
          const outputs = { ...node.connections.outputs };
          if (!outputs[connectionSource.output]) {
            outputs[connectionSource.output] = [];
          }
          outputs[connectionSource.output].push(`${targetNodeId}:${inputName}`);
          return {
            ...node,
            connections: { ...node.connections, outputs },
          };
        } else if (node.id === targetNodeId) {
          // Update target node inputs
          const inputs = { ...node.connections.inputs };
          inputs[inputName] = `${connectionSource.nodeId}:${connectionSource.output}`;
          return {
            ...node,
            connections: { ...node.connections, inputs },
          };
        }
        return node;
      }),
      metadata: { ...prev.metadata, updatedAt: new Date() },
    }));

    setIsConnecting(false);
    setConnectionSource(null);
  }, [connectionSource]);

  // Execute workflow
  const executeWorkflow = useCallback(async () => {
    setIsExecuting(true);
    setExecutionResults({});

    try {
      // Simulate workflow execution
      const results: Record<string, unknown> = {};
      
      for (const node of currentWorkflow.nodes) {
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate processing time
        
        switch (node.type) {
          case 'input':
            results[node.id] = { value: node.data.config.placeholder || 'Sample input' };
            break;
          case 'llm':
            results[node.id] = { response: 'This is a simulated LLM response.' };
            break;
          case 'prompt':
            results[node.id] = { formatted: 'Formatted prompt template' };
            break;
          case 'tool':
            results[node.id] = { result: 'Tool execution result' };
            break;
          case 'transform':
            results[node.id] = { transformed: 'Transformed data' };
            break;
          case 'output':
            results[node.id] = { final: 'Final workflow output' };
            break;
          default:
            results[node.id] = { result: 'Node executed successfully' };
        }
        
        setExecutionResults(prev => ({ ...prev, [node.id]: results[node.id] }));
      }

      onExecute?.(currentWorkflow);
    } catch (error) {
      console.error('Workflow execution failed:', error);
    } finally {
      setIsExecuting(false);
    }
  }, [currentWorkflow, onExecute]);

  // Save workflow
  const saveWorkflow = useCallback(() => {
    onSave?.(currentWorkflow);
  }, [currentWorkflow, onSave]);

  // Export workflow
  const exportWorkflow = useCallback(() => {
    onExport?.(currentWorkflow);
  }, [currentWorkflow, onExport]);

  // Get selected node
  const selectedNode = selectedNodeId 
    ? currentWorkflow.nodes.find(node => node.id === selectedNodeId)
    : null;

  // Render node
  const renderNode = (node: LangFlowNode) => {
    const nodeConfig = nodeTypes.find(nt => nt.type === node.type);
    if (!nodeConfig) return null;

    const isSelected = selectedNodeId === node.id;
    const hasResult = executionResults[node.id];

    return (
      <div
        key={node.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
        }`}
        style={{
          left: node.position.x,
          top: node.position.y,
        }}
        onClick={(e) => {
          e.stopPropagation();
          setSelectedNodeId(node.id);
        }}
      >
        <Card className={`w-48 ${nodeConfig.color} shadow-lg hover:shadow-xl transition-shadow`}>
          <CardContent className="p-3">
            <div className="flex items-center gap-2 mb-2">
              <nodeConfig.icon className="h-4 w-4" />
              <span className="font-medium text-sm">{node.data.label}</span>
              {hasResult && (
                <Badge variant="secondary" className="text-xs">
                  ✓
                </Badge>
              )}
            </div>

            {/* Input ports */}
            {node.data.inputs.map((input, index) => (
              <div
                key={input.name}
                className="flex items-center gap-1 text-xs mb-1"
                onClick={(e) => {
                  e.stopPropagation();
                  if (isConnecting) {
                    completeConnection(node.id, input.name);
                  }
                }}
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full cursor-pointer"></div>
                <span>{input.name}</span>
                {input.required && <span className="text-red-500">*</span>}
              </div>
            ))}

            {/* Output ports */}
            {node.data.outputs.map((output, index) => (
              <div
                key={output.name}
                className="flex items-center justify-end gap-1 text-xs mb-1"
                onClick={(e) => {
                  e.stopPropagation();
                  startConnection(node.id, output.name);
                }}
              >
                <span>{output.name}</span>
                <div className="w-2 h-2 bg-green-500 rounded-full cursor-pointer"></div>
              </div>
            ))}

            <div className="flex justify-between items-center mt-2">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  deleteNode(node.id);
                }}
              >
                <Trash2 className="h-3 w-3" />
              </Button>

              {hasResult && (
                <Badge variant="outline" className="text-xs">
                  Executed
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* Toolbar */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <Input
                  value={currentWorkflow.name}
                  onChange={(e) => setCurrentWorkflow(prev => ({
                    ...prev,
                    name: e.target.value,
                    metadata: { ...prev.metadata, updatedAt: new Date() },
                  }))}
                  className="font-medium"
                  placeholder="Workflow name..."
                />
              </div>

              <div className="flex items-center gap-2">
                {nodeTypes.slice(0, 6).map(nodeType => (
                  <Button
                    key={nodeType.type}
                    size="sm"
                    variant="outline"
                    onClick={() => addNode(nodeType.type, { x: 100, y: 100 })}
                    className="flex items-center gap-2"
                  >
                    <nodeType.icon className="h-4 w-4" />
                    {nodeType.label}
                  </Button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={saveWorkflow}>
                <Download className="h-4 w-4 mr-2" />
                Save
              </Button>
              
              <Button variant="outline" onClick={exportWorkflow}>
                <Upload className="h-4 w-4 mr-2" />
                Export
              </Button>

              <Button 
                onClick={executeWorkflow} 
                disabled={isExecuting || currentWorkflow.nodes.length === 0}
              >
                {isExecuting ? (
                  <>
                    <Pause className="h-4 w-4 mr-2" />
                    Executing...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Execute
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden bg-gray-50">
          <div
            ref={canvasRef}
            className="w-full h-full relative cursor-default"
            onClick={() => {
              setSelectedNodeId(null);
              if (isConnecting) {
                setIsConnecting(false);
                setConnectionSource(null);
              }
            }}
          >
            {/* Nodes */}
            {currentWorkflow.nodes.map(renderNode)}

            {/* Empty state */}
            {currentWorkflow.nodes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Start Building Your AI Workflow</h3>
                  <p className="text-sm mb-4">Add nodes to create intelligent processing pipelines</p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => addNode('input', { x: 200, y: 200 })}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Input Node
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Properties Panel */}
        {selectedNode && (
          <Card className="w-80 rounded-none border-y-0 border-r-0">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Node Properties
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Label</label>
                <Input
                  value={selectedNode.data.label}
                  onChange={(e) => updateNode(selectedNode.id, {
                    data: { ...selectedNode.data, label: e.target.value }
                  })}
                  placeholder="Node label..."
                  className="mt-1"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Configuration</label>
                <Textarea
                  value={JSON.stringify(selectedNode.data.config, null, 2)}
                  onChange={(e) => {
                    try {
                      const config = JSON.parse(e.target.value);
                      updateNode(selectedNode.id, {
                        data: { ...selectedNode.data, config }
                      });
                    } catch (error) {
                      // Invalid JSON, ignore
                    }
                  }}
                  placeholder="Node configuration (JSON)..."
                  className="mt-1 font-mono text-xs"
                  rows={8}
                />
              </div>

              {executionResults[selectedNode.id] && (
                <div>
                  <label className="text-sm font-medium">Execution Result</label>
                  <div className="mt-1 p-2 bg-gray-50 rounded text-xs font-mono">
                    {JSON.stringify(executionResults[selectedNode.id], null, 2)}
                  </div>
                </div>
              )}

              <div className="pt-4 border-t">
                <div className="text-sm font-medium mb-2">Node Info</div>
                <div className="space-y-1 text-xs text-muted-foreground">
                  <div>ID: {selectedNode.id}</div>
                  <div>Type: {selectedNode.type}</div>
                  <div>Inputs: {selectedNode.data.inputs.length}</div>
                  <div>Outputs: {selectedNode.data.outputs.length}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Status Bar */}
      <Card className="rounded-none border-x-0 border-b-0">
        <CardContent className="p-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Nodes: {currentWorkflow.nodes.length}</span>
              <span>Version: {currentWorkflow.metadata.version}</span>
              {isExecuting && (
                <Badge variant="secondary" className="text-xs">
                  <Zap className="h-3 w-3 mr-1 animate-pulse" />
                  Executing...
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <span>Max Tokens: {currentWorkflow.settings.maxTokens}</span>
              <span>Temperature: {currentWorkflow.settings.temperature}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
