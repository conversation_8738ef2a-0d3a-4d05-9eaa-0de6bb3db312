// Mock for three/examples/jsm/controls/OrbitControls
export class OrbitControls {
  constructor() {}
  update = () => {};
  dispose = () => {};
  addEventListener = () => {};
  removeEventListener = () => {};
  enableDamping = false;
  dampingFactor = 0.05;
  screenSpacePanning = false;
  minDistance = 0;
  maxDistance = 1000;
  minZoom = 0;
  maxZoom = 1000;
  minPolarAngle = 0;
  maxPolarAngle = Math.PI;
  minAzimuthAngle = -Infinity;
  maxAzimuthAngle = Infinity;
  target = { set: () => {} };
}
