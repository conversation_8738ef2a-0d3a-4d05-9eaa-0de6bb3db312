import { ChatAnalysisCanvasData, ConnectionData } from '@/components/visual-analysis/types';

/**
 * Canvas state for persistence
 */
export interface CanvasState {
  id: string;
  name: string;
  description?: string;
  canvasData: ChatAnalysisCanvasData;
  viewState: {
    camera: {
      position: { x: number; y: number; z: number };
      target: { x: number; y: number; z: number };
      zoom: number;
    };
    filters: {
      nodeTypes: string[];
      analysisTypes: string[];
      dateRange?: { start: string; end: string };
    };
    layout: {
      algorithm: 'force-directed' | 'grid' | 'circular' | 'hierarchical';
      parameters: Record<string, unknown>;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  isPublic: boolean;
  tags: string[];
}

/**
 * API service for canvas state persistence
 */
export class CanvasStateApi {
  private static baseUrl = '/api/canvas-states';

  /**
   * Get all canvas states for user
   */
  static async getAllCanvasStates(userId?: string): Promise<CanvasState[]> {
    try {
      const params = userId ? `?userId=${userId}` : '';
      const response = await fetch(`${this.baseUrl}${params}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch canvas states: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching canvas states:', error);
      // Return mock data for development
      return this.getMockCanvasStates();
    }
  }

  /**
   * Get canvas state by ID
   */
  static async getCanvasState(id: string): Promise<CanvasState | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error(`Failed to fetch canvas state: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching canvas state:', error);
      return null;
    }
  }

  /**
   * Save canvas state
   */
  static async saveCanvasState(canvasState: Omit<CanvasState, 'id' | 'createdAt' | 'updatedAt'>): Promise<CanvasState> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(canvasState),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save canvas state: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error saving canvas state:', error);
      // Return mock saved state for development
      const mockState: CanvasState = {
        ...canvasState,
        id: `canvas_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      return mockState;
    }
  }

  /**
   * Update canvas state
   */
  static async updateCanvasState(id: string, updates: Partial<CanvasState>): Promise<CanvasState> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          updatedAt: new Date().toISOString(),
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update canvas state: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating canvas state:', error);
      throw error;
    }
  }

  /**
   * Delete canvas state
   */
  static async deleteCanvasState(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to delete canvas state: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting canvas state:', error);
      throw error;
    }
  }

  /**
   * Duplicate canvas state
   */
  static async duplicateCanvasState(id: string, newName?: string): Promise<CanvasState> {
    const original = await this.getCanvasState(id);
    if (!original) {
      throw new Error('Canvas state not found');
    }

    const duplicate = {
      ...original,
      name: newName || `${original.name} (Copy)`,
      isPublic: false, // Duplicates are private by default
    };
    
    // Remove fields that should be regenerated
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...rest } = duplicate;
    
    return this.saveCanvasState(rest);
  }

  /**
   * Share canvas state (make public)
   */
  static async shareCanvasState(id: string): Promise<{ shareUrl: string; shareId: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/share`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to share canvas state: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error sharing canvas state:', error);
      // Return mock share data for development
      return {
        shareUrl: `${window.location.origin}/canvas/shared/${id}`,
        shareId: id
      };
    }
  }

  /**
   * Get public canvas states
   */
  static async getPublicCanvasStates(limit: number = 20): Promise<CanvasState[]> {
    try {
      const response = await fetch(`${this.baseUrl}/public?limit=${limit}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch public canvas states: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching public canvas states:', error);
      return [];
    }
  }

  /**
   * Search canvas states
   */
  static async searchCanvasStates(query: string, filters?: {
    userId?: string;
    isPublic?: boolean;
    tags?: string[];
    dateRange?: { start: string; end: string };
  }): Promise<CanvasState[]> {
    try {
      const params = new URLSearchParams();
      params.append('q', query);
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) {
            if (Array.isArray(value)) {
              params.append(key, value.join(','));
            } else if (typeof value === 'object') {
              params.append(key, JSON.stringify(value));
            } else {
              params.append(key, value.toString());
            }
          }
        });
      }

      const response = await fetch(`${this.baseUrl}/search?${params}`);
      if (!response.ok) {
        throw new Error(`Failed to search canvas states: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error searching canvas states:', error);
      return [];
    }
  }

  /**
   * Export canvas state
   */
  static async exportCanvasState(id: string, format: 'json' | 'png' | 'svg' = 'json'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}/export?format=${format}`);
      if (!response.ok) {
        throw new Error(`Failed to export canvas state: ${response.statusText}`);
      }
      return await response.blob();
    } catch (error) {
      console.error('Error exporting canvas state:', error);
      // Return mock export for development
      const state = await this.getCanvasState(id);
      const dataStr = JSON.stringify(state, null, 2);
      return new Blob([dataStr], { type: 'application/json' });
    }
  }

  /**
   * Import canvas state
   */
  static async importCanvasState(file: File): Promise<CanvasState> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${this.baseUrl}/import`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Failed to import canvas state: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error importing canvas state:', error);
      throw error;
    }
  }

  /**
   * Mock data for development
   */
  private static getMockCanvasStates(): CanvasState[] {
    return [
      {
        id: 'canvas_1',
        name: 'AI Research Analysis',
        description: 'Comprehensive analysis of AI research papers and findings',
        canvasData: {
          nodes: [],
          connections: [],
          clusters: [],
          simulations: [],
          metadata: {
            lastUpdated: new Date(),
            version: '1.0.0'
          }
        },
        viewState: {
          camera: {
            position: { x: 0, y: 0, z: 10 },
            target: { x: 0, y: 0, z: 0 },
            zoom: 1
          },
          filters: {
            nodeTypes: ['analysis-record'],
            analysisTypes: ['multiple', 'deep']
          },
          layout: {
            algorithm: 'force-directed',
            parameters: {
              strength: 0.5,
              distance: 100
            }
          }
        },
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-15'),
        userId: 'user_1',
        isPublic: false,
        tags: ['ai', 'research', 'analysis']
      }
    ];
  }
}
