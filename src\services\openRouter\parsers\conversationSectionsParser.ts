export interface ParsedConversationSections {
  responses: Array<{ number: number; text: string }>;
  followUpQuestions: string[];
  hasResponses: boolean;
  hasFollowUp: boolean;
}

function isResponseSectionHeader(line: string): boolean {
  const patterns = [
    /possible responses/i,
    /responses you might hear/i,
    /they might say/i,
    /typical responses/i,
    /ways you could respond/i,
    /sample answers/i,
    /example responses/i,
  ];
  return patterns.some((r) => r.test(line));
}

function isFollowUpSectionHeader(line: string): boolean {
  const patterns = [
    /keep the conversation going/i,
    /follow-?up questions?/i,
    /continue the conversation/i,
    /next questions?/i,
    /what else could you ask/i,
    /related questions/i,
  ];
  return patterns.some((r) => r.test(line));
}

// Helper: Clean up response text
function cleanText(text: string): string {
  return text
    .replace(/^[-*•]\s*/, "") // bullets
    .replace(/^["'“”‘’]|["'“”‘’]$/g, "") // any style of quotes
    .replace(/\s+/g, " ") // lots of spaces to one
    .trim();
}

// Helper: Validate minimum length and filter out noise
function isValidEntry(text: string, minLen = 10): boolean {
  return text && text.replace(/[^A-Za-z0-9]/g, "").length >= minLen;
}

export class ConversationSectionsParser {
  /**
   * Parse AI output to separate "Possible Responses" and "Follow-up Questions" sections.
   */
  static parse(content: string): ParsedConversationSections {
    const responses: Array<{ number: number; text: string }> = [];
    const followUpQuestions: string[] = [];

    // Split lines and scan for sections
    const lines = content.split(/\r?\n/);

    let currentSection: 'none' | 'responses' | 'followup' = 'none';
    let responseNumber = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Section boundaries
      if (isResponseSectionHeader(line)) {
        currentSection = 'responses';
        continue;
      }
      if (isFollowUpSectionHeader(line)) {
        currentSection = 'followup';
        continue;
      }
      // New section or unrelated text, stop cross-contamination
      if (
        (currentSection === 'responses' && isFollowUpSectionHeader(line)) ||
        (currentSection === 'followup' && isResponseSectionHeader(line))
      ) {
        currentSection = 'none';
        continue;
      }

      // -- RESPONSES Section
      if (currentSection === 'responses') {
        // Numbered response, e.g., '1. ...', '2) ...', or just '-' or quoted
        const numbered = line.match(/^(\d+)[\.\:\)]\s*(.+)/);
        if (numbered) {
          // Start new response
          let respText = cleanText(numbered[2]);
          // Glue multi-line responses together
          let lookahead = i + 1;
          while (
            lookahead < lines.length &&
            lines[lookahead].trim() &&
            !lines[lookahead].match(/^(\d+)[\.\:\)]\s+/) && // not a numbered item
            !isResponseSectionHeader(lines[lookahead]) &&
            !isFollowUpSectionHeader(lines[lookahead])
          ) {
            respText += " " + cleanText(lines[lookahead]);
            lookahead++;
          }
          i = lookahead - 1;
          if (isValidEntry(respText, 12)) {
            responses.push({ number: responseNumber++, text: respText });
          }
          continue;
        } else if (line.match(/^[-*•]\s*(.+)/)) {
          // Bullet response
          let respText = cleanText(RegExp.$1);
          let lookahead = i + 1;
          while (
            lookahead < lines.length &&
            lines[lookahead].trim() &&
            !lines[lookahead].match(/^[-*•]\s+/) &&
            !lines[lookahead].match(/^(\d+)[\.\:\)]\s+/) &&
            !isFollowUpSectionHeader(lines[lookahead]) &&
            !isResponseSectionHeader(lines[lookahead])
          ) {
            respText += " " + cleanText(lines[lookahead]);
            lookahead++;
          }
          i = lookahead - 1;
          if (isValidEntry(respText, 12)) {
            responses.push({ number: responseNumber++, text: respText });
          }
          continue;
        }
      }

      // -- FOLLOW UP Section
      if (currentSection === 'followup') {
        // More lenient parsing for questions: we don't require a '?' at the end.
        // We just need to handle numbered lists, bulleted lists, or plain text lines.
        const numberedMatch = line.match(/^(\d+)[\.\:\)]\s*(.+)/);
        const bulletMatch = line.match(/^[-*•]\s*(.+)/);

        let questionText = '';
        if (numberedMatch) {
          questionText = numberedMatch[2];
        } else if (bulletMatch) {
          questionText = bulletMatch[1];
        } else {
          questionText = line;
        }

        if (questionText) {
          const cleanedQ = cleanText(questionText);
          if (isValidEntry(cleanedQ, 10)) {
            followUpQuestions.push(cleanedQ);
          }
        }
      }
    }

    // Fallback for responses: try finding any numbered answer if none found
    if (responses.length === 0) {
      const fallbackMatches = [...content.matchAll(/(\d+)[\.\:\)]\s+(.{15,}?)(?=(?:\n\d+[\.\:\)]|\n[-*•]|\n|$))/g)];
      
      if (fallbackMatches.length > 0) {
        const potentialItems = fallbackMatches.map(([, , text]) => cleanText(text));
        const questionCount = potentialItems.filter(text => text.endsWith('?')).length;

        // Heuristic: If more than half of the items are questions, it's likely a list of 
        // follow-up questions, not responses. Let the follow-up parser handle it.
        const isLikelyFollowUpList = (potentialItems.length > 0) && (questionCount / potentialItems.length > 0.5);

        if (!isLikelyFollowUpList) {
          potentialItems.forEach(text => {
            if (isValidEntry(text, 12)) {
              responses.push({ number: responseNumber++, text });
            }
          });
        }
      }
    }

    // Fallback for followups if none found
    if (followUpQuestions.length === 0) {
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && trimmed.endsWith("?") && isValidEntry(trimmed, 10)) {
          followUpQuestions.push(cleanText(trimmed));
        }
        if (followUpQuestions.length >= 8) break;
      }
    }

    return {
      responses,
      followUpQuestions: followUpQuestions.slice(0, 8),
      hasResponses: responses.length > 0,
      hasFollowUp: followUpQuestions.length > 0,
    };
  }
}
