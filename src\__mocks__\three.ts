// Global mock for three.js for Vitest
// Use a minimal type for vi.fn to avoid 'any' and jest dependency
declare const vi: { fn: <T = unknown>(impl?: () => T) => () => T };

export const Scene = vi.fn(() => ({
  add: vi.fn(),
  remove: vi.fn(),
  traverse: vi.fn(),
}));
export const WebGLRenderer = vi.fn(() => ({
  setSize: vi.fn(),
  render: vi.fn(),
  domElement: document.createElement('canvas'),
  dispose: vi.fn(),
}));
export const OrthographicCamera = vi.fn(() => ({
  position: { set: vi.fn(), copy: vi.fn() },
  lookAt: vi.fn(),
  updateProjectionMatrix: vi.fn(),
}));
export const SphereGeometry = vi.fn();
export const MeshBasicMaterial = vi.fn();
export const Mesh = vi.fn(() => ({
  position: { set: vi.fn(), copy: vi.fn() },
  scale: { setScalar: vi.fn() },
  material: { color: { set: vi.fn() } },
  userData: {},
}));
export const Vector3 = vi.fn(() => ({
  set: vi.fn(),
  copy: vi.fn(),
  distanceTo: vi.fn(() => 10 as unknown),
}));
export const LineBasicMaterial = vi.fn();
export const Line = vi.fn();
export const BufferGeometry = vi.fn();
export const Frustum = vi.fn(() => ({
  setFromProjectionMatrix: vi.fn(),
  containsPoint: vi.fn(() => true as unknown),
}));
export const Matrix4 = vi.fn(() => ({
  multiplyMatrices: vi.fn(),
}));
export const Color = vi.fn(() => ({
  set: vi.fn(),
  copy: vi.fn(),
  lerp: vi.fn(),
}));
export const Raycaster = vi.fn(() => ({
  setFromCamera: vi.fn(),
  intersectObjects: vi.fn(() => []),
}));
