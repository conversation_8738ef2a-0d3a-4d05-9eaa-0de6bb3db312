import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import TestApp from "./TestApp";
import "./index.css";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { Toaster } from "@/components/ui/toaster"; // Added import

console.log('🔥 main.tsx loading...');
console.log('📦 React version:', React.version);

const rootElement = document.getElementById("root");
console.log('🎯 Root element:', rootElement);
console.log('🎯 Root element innerHTML:', rootElement?.innerHTML);
console.log('🎯 Document ready state:', document.readyState);
console.log('🎨 Body computed styles:', window.getComputedStyle(document.body));
console.log('🎨 Body background color:', window.getComputedStyle(document.body).backgroundColor);
console.log('🎨 Body color:', window.getComputedStyle(document.body).color);

if (!rootElement) {
  console.error('❌ Root element not found!');
  throw new Error('Root element not found');
}

// Add test styles to body to ensure CSS is working
document.body.style.margin = '0';
document.body.style.padding = '0';
document.body.style.minHeight = '100vh';
console.log('🧪 Applied test styles to body');

try {
  const root = ReactDOM.createRoot(rootElement);
  console.log('🏗️ React root created:', root);

  // First try to render a simple test component
  console.log('🧪 Testing simple render...');
  root.render(
    <div style={{
      padding: '20px',
      backgroundColor: 'red',
      color: 'white',
      fontSize: '24px',
      position: 'fixed',
      top: '0',
      left: '0',
      zIndex: 9999
    }}>
      TEST RENDER WORKING
    </div>
  );

  // Wait a moment then render the actual app
  setTimeout(() => {
    console.log('🚀 Rendering actual app...');
    root.render(
      <React.StrictMode>
        <ErrorBoundary>
          <Toaster /> {/* Added Toaster here */}
          <App />
        </ErrorBoundary>
      </React.StrictMode>
    );
    console.log('✅ React app rendered successfully');

    // Add test navigation after app loads
    setTimeout(() => {
      console.log('🧭 Testing navigation...');
      console.log('🧭 Current URL:', window.location.href);
      console.log('🧭 Current pathname:', window.location.pathname);

      // Test programmatic navigation
      if (window.location.pathname === '/') {
        console.log('🧭 On home page, testing navigation to /canvas');
        window.history.pushState({}, '', '/canvas');
        console.log('🧭 Pushed /canvas to history');
      }
    }, 2000);
  }, 1000);

} catch (error) {
  console.error('❌ Failed to render React app:', error);
  console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');

  // Fallback render
  rootElement.innerHTML = `
    <div style="padding: 20px; background: red; color: white; font-size: 24px;">
      FALLBACK: React failed to render. Check console for errors.
      <br>Error: ${error instanceof Error ? error.message : 'Unknown error'}
    </div>
  `;
}
