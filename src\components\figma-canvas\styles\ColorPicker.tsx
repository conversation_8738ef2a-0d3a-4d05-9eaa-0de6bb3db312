import React, { useState, useRef, useEffect } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Pa<PERSON>, <PERSON>pette, Gradient } from 'lucide-react';

interface ColorPickerProps {
  value?: string;
  onChange: (color: string) => void;
  showGradient?: boolean;
  className?: string;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  showGradient = false,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentColor, setCurrentColor] = useState(value);
  const [hsv, setHsv] = useState(hexToHsv(value));
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const hueCanvasRef = useRef<HTMLCanvasElement>(null);

  // Predefined color palette
  const colorPalette = [
    '#000000', '#333333', '#666666', '#999999', '#CCCCCC', '#FFFFFF',
    '#FF0000', '#FF6600', '#FFCC00', '#FFFF00', '#CCFF00', '#66FF00',
    '#00FF00', '#00FF66', '#00FFCC', '#00FFFF', '#00CCFF', '#0066FF',
    '#0000FF', '#6600FF', '#CC00FF', '#FF00FF', '#FF00CC', '#FF0066',
    '#8B4513', '#A0522D', '#CD853F', '#DEB887', '#F4A460', '#D2691E',
    '#FF69B4', '#FF1493', '#DC143C', '#B22222', '#8B0000', '#800000',
  ];

  useEffect(() => {
    setCurrentColor(value);
    setHsv(hexToHsv(value));
  }, [value]);

  useEffect(() => {
    drawColorPicker();
    drawHueSlider();
  }, [hsv]);

  const drawColorPicker = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;

    // Create saturation-value gradient
    const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
    gradient1.addColorStop(0, '#FFFFFF');
    gradient1.addColorStop(1, hsvToHex(hsv.h, 100, 100));

    ctx.fillStyle = gradient1;
    ctx.fillRect(0, 0, width, height);

    const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
    gradient2.addColorStop(0, 'rgba(0,0,0,0)');
    gradient2.addColorStop(1, '#000000');

    ctx.fillStyle = gradient2;
    ctx.fillRect(0, 0, width, height);

    // Draw current position indicator
    const x = (hsv.s / 100) * width;
    const y = ((100 - hsv.v) / 100) * height;

    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, 2 * Math.PI);
    ctx.stroke();

    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(x, y, 8, 0, 2 * Math.PI);
    ctx.stroke();
  };

  const drawHueSlider = () => {
    const canvas = hueCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;

    // Create hue gradient
    const gradient = ctx.createLinearGradient(0, 0, width, 0);
    for (let i = 0; i <= 360; i += 60) {
      gradient.addColorStop(i / 360, hsvToHex(i, 100, 100));
    }

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // Draw current hue indicator
    const x = (hsv.h / 360) * width;
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();

    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
  };

  const handleColorPickerClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const s = (x / canvas.width) * 100;
    const v = 100 - (y / canvas.height) * 100;

    const newHsv = { ...hsv, s: Math.max(0, Math.min(100, s)), v: Math.max(0, Math.min(100, v)) };
    setHsv(newHsv);
    
    const newColor = hsvToHex(newHsv.h, newHsv.s, newHsv.v);
    setCurrentColor(newColor);
    onChange(newColor);
  };

  const handleHueSliderClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = hueCanvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;

    const h = (x / canvas.width) * 360;
    const newHsv = { ...hsv, h: Math.max(0, Math.min(360, h)) };
    setHsv(newHsv);
    
    const newColor = hsvToHex(newHsv.h, newHsv.s, newHsv.v);
    setCurrentColor(newColor);
    onChange(newColor);
  };

  const handleHexChange = (hex: string) => {
    if (isValidHex(hex)) {
      setCurrentColor(hex);
      setHsv(hexToHsv(hex));
      onChange(hex);
    }
  };

  const handlePaletteColorClick = (color: string) => {
    setCurrentColor(color);
    setHsv(hexToHsv(color));
    onChange(color);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn("w-10 h-8 p-0 border-2", className)}
          style={{ backgroundColor: currentColor }}
          title="Color Picker"
        >
          <span className="sr-only">Pick color</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <Tabs defaultValue="picker" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="picker" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              Picker
            </TabsTrigger>
            <TabsTrigger value="palette" className="flex items-center gap-2">
              <Pipette className="w-4 h-4" />
              Palette
            </TabsTrigger>
          </TabsList>

          <TabsContent value="picker" className="space-y-4">
            {/* Color Picker Canvas */}
            <div className="space-y-2">
              <canvas
                ref={canvasRef}
                width={256}
                height={256}
                className="w-full h-48 border border-gray-200 rounded cursor-crosshair"
                onClick={handleColorPickerClick}
              />
              
              {/* Hue Slider */}
              <canvas
                ref={hueCanvasRef}
                width={256}
                height={20}
                className="w-full h-5 border border-gray-200 rounded cursor-crosshair"
                onClick={handleHueSliderClick}
              />
            </div>

            {/* Color Values */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div
                  className="w-8 h-8 border border-gray-200 rounded"
                  style={{ backgroundColor: currentColor }}
                />
                <Input
                  value={currentColor}
                  onChange={(e) => handleHexChange(e.target.value)}
                  className="flex-1 h-8 text-xs font-mono"
                  placeholder="#000000"
                />
              </div>

              {/* HSV Sliders */}
              <div className="space-y-2">
                <div>
                  <Label className="text-xs">Hue: {Math.round(hsv.h)}°</Label>
                  <Slider
                    value={[hsv.h]}
                    onValueChange={([h]) => {
                      const newHsv = { ...hsv, h };
                      setHsv(newHsv);
                      const newColor = hsvToHex(newHsv.h, newHsv.s, newHsv.v);
                      setCurrentColor(newColor);
                      onChange(newColor);
                    }}
                    max={360}
                    step={1}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <Label className="text-xs">Saturation: {Math.round(hsv.s)}%</Label>
                  <Slider
                    value={[hsv.s]}
                    onValueChange={([s]) => {
                      const newHsv = { ...hsv, s };
                      setHsv(newHsv);
                      const newColor = hsvToHex(newHsv.h, newHsv.s, newHsv.v);
                      setCurrentColor(newColor);
                      onChange(newColor);
                    }}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <Label className="text-xs">Value: {Math.round(hsv.v)}%</Label>
                  <Slider
                    value={[hsv.v]}
                    onValueChange={([v]) => {
                      const newHsv = { ...hsv, v };
                      setHsv(newHsv);
                      const newColor = hsvToHex(newHsv.h, newHsv.s, newHsv.v);
                      setCurrentColor(newColor);
                      onChange(newColor);
                    }}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="palette" className="space-y-4">
            {/* Predefined Colors */}
            <div className="grid grid-cols-6 gap-2">
              {colorPalette.map((color, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-8 h-8 border-2 rounded cursor-pointer hover:scale-110 transition-transform",
                    currentColor === color ? "border-blue-500" : "border-gray-200"
                  )}
                  style={{ backgroundColor: color }}
                  onClick={() => handlePaletteColorClick(color)}
                  title={color}
                />
              ))}
            </div>

            {/* Recent Colors */}
            <div>
              <Label className="text-xs">Recent Colors</Label>
              <div className="grid grid-cols-6 gap-2 mt-2">
                {/* This would be populated from a recent colors store */}
                {Array.from({ length: 6 }, (_, i) => (
                  <div
                    key={i}
                    className="w-8 h-8 border border-gray-200 rounded bg-gray-50"
                  />
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
};

// Utility functions
function hexToHsv(hex: string): { h: number; s: number; v: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;

  let h = 0;
  if (diff !== 0) {
    if (max === r) {
      h = ((g - b) / diff) % 6;
    } else if (max === g) {
      h = (b - r) / diff + 2;
    } else {
      h = (r - g) / diff + 4;
    }
  }
  h = Math.round(h * 60);
  if (h < 0) h += 360;

  const s = max === 0 ? 0 : Math.round((diff / max) * 100);
  const v = Math.round(max * 100);

  return { h, s, v };
}

function hsvToHex(h: number, s: number, v: number): string {
  const sNorm = s / 100;
  const vNorm = v / 100;

  const c = vNorm * sNorm;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = vNorm - c;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 60) {
    r = c; g = x; b = 0;
  } else if (60 <= h && h < 120) {
    r = x; g = c; b = 0;
  } else if (120 <= h && h < 180) {
    r = 0; g = c; b = x;
  } else if (180 <= h && h < 240) {
    r = 0; g = x; b = c;
  } else if (240 <= h && h < 300) {
    r = x; g = 0; b = c;
  } else if (300 <= h && h < 360) {
    r = c; g = 0; b = x;
  }

  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

function isValidHex(hex: string): boolean {
  return /^#[0-9A-F]{6}$/i.test(hex);
}
