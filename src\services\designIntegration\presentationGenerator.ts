/**
 * Presentation Generator
 * 
 * Creates professional presentations from analysis results with multiple
 * export formats and customizable templates.
 */

import { DesignElement, DesignTemplate } from './analysisToDesignConverter';

export interface PresentationSlide {
  id: string;
  title: string;
  content: SlideContent[];
  layout: SlideLayout;
  style: SlideStyle;
  notes?: string;
}

export interface SlideContent {
  type: 'text' | 'image' | 'chart' | 'list' | 'quote' | 'code';
  data: unknown;
  position: { x: number; y: number };
  size: { width: number; height: number };
  style?: unknown;
}

export interface SlideLayout {
  type: 'title' | 'content' | 'two-column' | 'image-text' | 'full-image' | 'comparison';
  background?: string;
  padding: { top: number; right: number; bottom: number; left: number };
}

export interface SlideStyle {
  backgroundColor: string;
  textColor: string;
  accentColor: string;
  fontFamily: string;
  fontSize: {
    title: number;
    subtitle: number;
    body: number;
    caption: number;
  };
}

export interface Presentation {
  id: string;
  title: string;
  subtitle?: string;
  author: string;
  slides: PresentationSlide[];
  theme: PresentationTheme;
  metadata: PresentationMetadata;
}

export interface PresentationTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
    muted: string;
  };
  fonts: {
    heading: string;
    body: string;
    code: string;
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
}

export interface PresentationMetadata {
  createdAt: number;
  analysisIds: string[];
  template: string;
  exportFormats: string[];
  version: string;
}

export class PresentationGenerator {
  private themes: Record<string, PresentationTheme> = {
    professional: {
      name: 'Professional',
      colors: {
        primary: '#2563eb',
        secondary: '#64748b',
        accent: '#0ea5e9',
        background: '#ffffff',
        text: '#1e293b',
        muted: '#64748b'
      },
      fonts: {
        heading: 'Inter',
        body: 'Inter',
        code: 'JetBrains Mono'
      },
      spacing: { small: 8, medium: 16, large: 32 }
    },
    creative: {
      name: 'Creative',
      colors: {
        primary: '#7c3aed',
        secondary: '#ec4899',
        accent: '#06b6d4',
        background: '#fafafa',
        text: '#1f2937',
        muted: '#6b7280'
      },
      fonts: {
        heading: 'Poppins',
        body: 'Open Sans',
        code: 'Fira Code'
      },
      spacing: { small: 12, medium: 20, large: 40 }
    },
    minimal: {
      name: 'Minimal',
      colors: {
        primary: '#374151',
        secondary: '#9ca3af',
        accent: '#059669',
        background: '#ffffff',
        text: '#111827',
        muted: '#6b7280'
      },
      fonts: {
        heading: 'Roboto',
        body: 'Roboto',
        code: 'Source Code Pro'
      },
      spacing: { small: 6, medium: 12, large: 24 }
    }
  };

  generatePresentation(
    analysisResults: AnalysisResult[],
    options: {
      title?: string;
      template?: string;
      theme?: string;
      includeMetadata?: boolean;
      includeFollowUps?: boolean;
    } = {}
  ): Presentation {
    const {
      title = 'Analysis Results Presentation',
      template = 'comprehensive',
      theme = 'professional',
      includeMetadata = true,
      includeFollowUps = true
    } = options;

    const selectedTheme = this.themes[theme] || this.themes.professional;
    const slides: PresentationSlide[] = [];

    // Title slide
    slides.push(this.createTitleSlide(title, analysisResults, selectedTheme));

    // Overview slide
    if (analysisResults.length > 1) {
      slides.push(this.createOverviewSlide(analysisResults, selectedTheme));
    }

    // Individual analysis slides
    analysisResults.forEach((analysis, index) => {
      slides.push(...this.createAnalysisSlides(analysis, index + 1, selectedTheme, includeFollowUps));
    });

    // Summary slide
    if (analysisResults.length > 1) {
      slides.push(this.createSummarySlide(analysisResults, selectedTheme));
    }

    // Metadata slide
    if (includeMetadata) {
      slides.push(this.createMetadataSlide(analysisResults, selectedTheme));
    }

    return {
      id: `presentation-${Date.now()}`,
      title,
      subtitle: `Analysis of ${analysisResults.length} question${analysisResults.length > 1 ? 's' : ''}`,
      author: 'ChatCraft Trainer Pro',
      slides,
      theme: selectedTheme,
      metadata: {
        createdAt: Date.now(),
        analysisIds: analysisResults.map(a => a.id),
        template,
        exportFormats: ['pdf', 'pptx', 'html'],
        version: '1.0'
      }
    };
  }

  private createTitleSlide(title: string, analyses: AnalysisResult[], theme: PresentationTheme): PresentationSlide {
    return {
      id: 'title-slide',
      title: title,
      content: [
        {
          type: 'text',
          data: { text: title, level: 'h1' },
          position: { x: 50, y: 200 },
          size: { width: 700, height: 100 },
          style: { 
            fontSize: theme.spacing.large + 8,
            fontFamily: theme.fonts.heading,
            color: theme.colors.primary,
            textAlign: 'center',
            fontWeight: 'bold'
          }
        },
        {
          type: 'text',
          data: { 
            text: `Comprehensive analysis of ${analyses.length} question${analyses.length > 1 ? 's' : ''}`,
            level: 'subtitle'
          },
          position: { x: 50, y: 320 },
          size: { width: 700, height: 50 },
          style: {
            fontSize: theme.spacing.medium + 4,
            fontFamily: theme.fonts.body,
            color: theme.colors.secondary,
            textAlign: 'center'
          }
        },
        {
          type: 'text',
          data: { 
            text: `Generated on ${new Date().toLocaleDateString()}`,
            level: 'caption'
          },
          position: { x: 50, y: 500 },
          size: { width: 700, height: 30 },
          style: {
            fontSize: theme.spacing.small + 4,
            fontFamily: theme.fonts.body,
            color: theme.colors.muted,
            textAlign: 'center'
          }
        }
      ],
      layout: {
        type: 'title',
        background: `linear-gradient(135deg, ${theme.colors.background} 0%, ${theme.colors.primary}10 100%)`,
        padding: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      style: this.createSlideStyle(theme)
    };
  }

  private createOverviewSlide(analyses: AnalysisResult[], theme: PresentationTheme): PresentationSlide {
    const analysisTypes = [...new Set(analyses.map(a => a.analysisType))];
    const models = [...new Set(analyses.map(a => a.model))];
    
    return {
      id: 'overview-slide',
      title: 'Analysis Overview',
      content: [
        {
          type: 'text',
          data: { text: 'Analysis Overview', level: 'h1' },
          position: { x: 50, y: 50 },
          size: { width: 700, height: 60 }
        },
        {
          type: 'list',
          data: {
            items: [
              `Total Analyses: ${analyses.length}`,
              `Analysis Types: ${analysisTypes.join(', ')}`,
              `AI Models Used: ${models.join(', ')}`,
              `Date Range: ${new Date(Math.min(...analyses.map(a => a.timestamp.getTime()))).toLocaleDateString()} - ${new Date(Math.max(...analyses.map(a => a.timestamp.getTime()))).toLocaleDateString()}`
            ]
          },
          position: { x: 50, y: 150 },
          size: { width: 700, height: 300 }
        }
      ],
      layout: {
        type: 'content',
        padding: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      style: this.createSlideStyle(theme)
    };
  }

  private createAnalysisSlides(
    analysis: AnalysisResult, 
    index: number, 
    theme: PresentationTheme,
    includeFollowUps: boolean
  ): PresentationSlide[] {
    const slides: PresentationSlide[] = [];

    // Main analysis slide
    slides.push({
      id: `analysis-${analysis.id}`,
      title: `Analysis ${index}: ${analysis.analysisType}`,
      content: [
        {
          type: 'text',
          data: { text: `Analysis ${index}: ${analysis.analysisType.charAt(0).toUpperCase() + analysis.analysisType.slice(1)}`, level: 'h1' },
          position: { x: 50, y: 50 },
          size: { width: 700, height: 60 }
        },
        {
          type: 'quote',
          data: { text: analysis.question, author: 'Question' },
          position: { x: 50, y: 130 },
          size: { width: 700, height: 80 }
        },
        {
          type: 'text',
          data: { text: analysis.analysis, level: 'body' },
          position: { x: 50, y: 230 },
          size: { width: 700, height: 300 }
        }
      ],
      layout: {
        type: 'content',
        padding: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      style: this.createSlideStyle(theme),
      notes: `Analysis performed using ${analysis.model} with ${analysis.style} conversation style.`
    });

    // Follow-up questions slide
    if (includeFollowUps && analysis.followUpQuestions && analysis.followUpQuestions.length > 0) {
      slides.push({
        id: `followup-${analysis.id}`,
        title: `Follow-up Questions`,
        content: [
          {
            type: 'text',
            data: { text: 'Follow-up Questions', level: 'h1' },
            position: { x: 50, y: 50 },
            size: { width: 700, height: 60 }
          },
          {
            type: 'list',
            data: { items: analysis.followUpQuestions },
            position: { x: 50, y: 130 },
            size: { width: 700, height: 400 }
          }
        ],
        layout: {
          type: 'content',
          padding: { top: 50, right: 50, bottom: 50, left: 50 }
        },
        style: this.createSlideStyle(theme)
      });
    }

    return slides;
  }

  private createSummarySlide(analyses: AnalysisResult[], theme: PresentationTheme): PresentationSlide {
    const avgRating = analyses
      .filter(a => a.rating)
      .reduce((sum, a) => sum + (a.rating || 0), 0) / analyses.filter(a => a.rating).length;

    return {
      id: 'summary-slide',
      title: 'Summary & Insights',
      content: [
        {
          type: 'text',
          data: { text: 'Summary & Insights', level: 'h1' },
          position: { x: 50, y: 50 },
          size: { width: 700, height: 60 }
        },
        {
          type: 'text',
          data: { 
            text: `This presentation covered ${analyses.length} comprehensive analyses across different perspectives and methodologies. The average quality rating was ${avgRating.toFixed(1)}/10, indicating high-quality insights.`,
            level: 'body'
          },
          position: { x: 50, y: 130 },
          size: { width: 700, height: 100 }
        },
        {
          type: 'list',
          data: {
            title: 'Key Takeaways:',
            items: [
              'Multiple analytical perspectives provide comprehensive understanding',
              'AI-powered analysis enables rapid insight generation',
              'Follow-up questions guide deeper exploration',
              'Structured analysis frameworks improve decision-making'
            ]
          },
          position: { x: 50, y: 250 },
          size: { width: 700, height: 250 }
        }
      ],
      layout: {
        type: 'content',
        padding: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      style: this.createSlideStyle(theme)
    };
  }

  private createMetadataSlide(analyses: AnalysisResult[], theme: PresentationTheme): PresentationSlide {
    return {
      id: 'metadata-slide',
      title: 'Technical Details',
      content: [
        {
          type: 'text',
          data: { text: 'Technical Details', level: 'h1' },
          position: { x: 50, y: 50 },
          size: { width: 700, height: 60 }
        },
        {
          type: 'text',
          data: {
            text: analyses.map(a => 
              `Analysis ID: ${a.id}\nModel: ${a.model}\nStyle: ${a.style}\nType: ${a.analysisType}\nTimestamp: ${a.timestamp.toISOString()}`
            ).join('\n\n'),
            level: 'code'
          },
          position: { x: 50, y: 130 },
          size: { width: 700, height: 400 }
        }
      ],
      layout: {
        type: 'content',
        padding: { top: 50, right: 50, bottom: 50, left: 50 }
      },
      style: this.createSlideStyle(theme)
    };
  }

  private createSlideStyle(theme: PresentationTheme): SlideStyle {
    return {
      backgroundColor: theme.colors.background,
      textColor: theme.colors.text,
      accentColor: theme.colors.accent,
      fontFamily: theme.fonts.body,
      fontSize: {
        title: theme.spacing.large + 8,
        subtitle: theme.spacing.medium + 4,
        body: theme.spacing.medium,
        caption: theme.spacing.small + 4
      }
    };
  }

  // Export methods
  async exportToPDF(presentation: Presentation): Promise<Blob> {
    // Implementation would use a library like jsPDF or Puppeteer
    console.log('Exporting to PDF:', presentation.title);
    return new Blob(['PDF content'], { type: 'application/pdf' });
  }

  async exportToPowerPoint(presentation: Presentation): Promise<Blob> {
    // Implementation would use a library like PptxGenJS
    console.log('Exporting to PowerPoint:', presentation.title);
    return new Blob(['PPTX content'], { type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' });
  }

  async exportToHTML(presentation: Presentation): Promise<string> {
    // Generate HTML presentation (like reveal.js)
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${presentation.title}</title>
          <style>
            body { font-family: ${presentation.theme.fonts.body}; }
            .slide { width: 800px; height: 600px; padding: 50px; }
            h1 { color: ${presentation.theme.colors.primary}; }
          </style>
        </head>
        <body>
          ${presentation.slides.map(slide => `
            <div class="slide">
              <h1>${slide.title}</h1>
              ${slide.content.map(content => this.renderContentToHTML(content)).join('')}
            </div>
          `).join('')}
        </body>
      </html>
    `;
    return html;
  }

  private renderContentToHTML(content: SlideContent): string {
    switch (content.type) {
      case 'text':
        return `<p>${content.data.text}</p>`;
      case 'list':
        return `<ul>${content.data.items.map((item: string) => `<li>${item}</li>`).join('')}</ul>`;
      case 'quote':
        return `<blockquote>${content.data.text}</blockquote>`;
      default:
        return '';
    }
  }
}

export const presentationGenerator = new PresentationGenerator();
