
import { useToast } from "@/hooks/use-toast";
import { useDiscovery } from "@/hooks/useDiscovery";
import { UserNote } from "@/types/conversation";
import { ConnectionSuggestion } from "@/services/aiCanvasService";
import { Connection } from "@/stores/useCanvasStore";

interface ToggleConnectionResult {
  success: boolean;
  connected?: boolean;
  chain?: boolean;
  count?: number;
}

interface UseCanvasEventHandlersProps {
  notes: UserNote[];
  connections: Connection[];
  getNodePosition: (nodeId: string) => { x: number; y: number };
  runAIAnalysis: (notes: UserNote[], connections: Connection[], positions: { id: string; x: number; y: number }[]) => void;
  acceptSuggestion: (suggestion: ConnectionSuggestion) => void;
  handleConnectNodes: (from?: string, to?: string) => boolean;
  handleToggleConnection: () => ToggleConnectionResult | null;
  handleDeleteNote: (noteId: string) => void;
}

export const useCanvasEventHandlers = ({
  notes,
  connections,
  getNodePosition,
  runAIAnalysis,
  acceptSuggestion,
  handleConnectNodes,
  handleToggleConnection,
  handleDeleteNote,
}: UseCanvasEventHandlersProps) => {
  const { toast } = useToast();
  const { discoverContext } = useDiscovery();
  const handleConnectNodesWithToast = () => {
    const result = handleToggleConnection();
    if (result && result.success) {
      const typedResult = result as ToggleConnectionResult;
      if (typedResult.chain) {
        if (typedResult.count && typedResult.count > 0) {          toast({
            title: "Node chain created",
            description: `Successfully created ${typedResult.count} new connection(s) in a chain.`,
          });
        } else {
          toast({
            title: "No new connections",
            description: "The selected nodes were already connected in a chain.",
          });
        }
      } else if (result.connected) {
        toast({
          title: "Nodes connected",
          description: "Successfully connected the selected historical analyses.",
        });
      } else {
        toast({
          title: "Nodes disconnected",
          description: "The connection between the selected analyses has been removed.",
        });
      }
    }
  };

  const handleDeleteNoteWithToast = (noteId: string) => {
    handleDeleteNote(noteId);
    toast({
      title: "Analysis deleted",
      description: "The historical analysis has been removed.",
    });
  };

  const handleDiscover = (note: UserNote) => {
    discoverContext(note);
  };

  const handleRunAIAnalysis = () => {
    const currentPositions = notes.map(note => ({
      id: note.id,
      ...getNodePosition(note.id)
    }));
    runAIAnalysis(notes, connections, currentPositions);
  };

  const handleAcceptSuggestion = (suggestion: ConnectionSuggestion) => {
    acceptSuggestion(suggestion);
    handleConnectNodes(suggestion.fromId, suggestion.toId);
    toast({
      title: "Connection Added",
      description: "AI suggestion has been applied to the canvas.",
    });
  };

  return {
    handleConnectNodesWithToast,
    handleDeleteNoteWithToast,
    handleDiscover,
    handleRunAIAnalysis,
    handleAcceptSuggestion,
  };
};
