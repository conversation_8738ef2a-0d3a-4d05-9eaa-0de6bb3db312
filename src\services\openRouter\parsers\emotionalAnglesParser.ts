import { ParsedEmotionalAnglesAnalysis, ParsedFallback } from '../types/parsedOutput';

export class EmotionalAnglesParser {
  static parse(rawText: string): ParsedEmotionalAnglesAnalysis | ParsedFallback {
    // Basic placeholder implementation
    // This will need a robust way to parse multiple angles
    const angles = [];
    const angleMatches = rawText.matchAll(/Emotion: (.*?)\nAnalysis: ([\s\S]*?)(?=Emotion:|$)/gi);

    for (const match of angleMatches) {
      angles.push({
        emotion: match[1].trim(),
        analysis: match[2].trim(),
      });
    }

    if (angles.length > 0) {
      return {
        type: 'emotional-angles',
        angles,
      };
    }

    return {
      type: 'fallback',
      isFallback: true,
      content: rawText,
      warning: 'Could not parse Emotional Angles structure.',
    };
  }
}
