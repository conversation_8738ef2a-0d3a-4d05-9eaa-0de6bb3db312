import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { SavedAnalysis, SavedFolder } from "@/types/conversation";

// NodePosition and Connection types are moved to useCanvasStore

interface LibraryState {
  savedAnalyses: SavedAnalysis[];
  savedFolders: SavedFolder[];
  historicalAnalysisSearchQuery: string;
  setHistoricalAnalysisSearchQuery: (query: string) => void;
  saveAnalysis: (analysis: SavedAnalysis) => Promise<void>;
  deleteAnalysis: (id: string) => void;
  updateSavedAnalysis: (id: string, updatedAnalysis: SavedAnalysis) => Promise<void>;
  addFolder: (folder: SavedFolder) => void;
  deleteFolder: (id: string) => void;
  loadSavedAnalyses: () => void;
  loadSavedFolders: () => void;
  createFolder: (folderName: string) => Promise<void>;
  moveAnalysisToFolder: (analysisId: string, folderId: string) => void;
}

// Reviver function to convert date strings back to Date objects
const dateReviver = (key: string, value: unknown) => {
  if (['createdAt', 'updatedAt', 'timestamp'].includes(key) && typeof value === 'string') {
    const date = new Date(value);
    // Check if the parsed date is valid
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  return value;
};

const cleanState = (storageValue: unknown) => {
  if (!storageValue || typeof storageValue !== 'object' || storageValue === null || !('state' in storageValue)) {
    return { state: {}, version: 0 };
  }
  const { state, version } = storageValue as { state: object; version: number };
  const newState = state as { savedAnalyses?: unknown };
  if (newState.savedAnalyses && Array.isArray(newState.savedAnalyses)) {
    newState.savedAnalyses = (newState.savedAnalyses as SavedAnalysis[]).map((a: SavedAnalysis) => ({
      ...a,
      createdAt: new Date(a.createdAt),
      updatedAt: new Date(a.updatedAt),
    }));
  }
  return { state: newState, version };
};


export const useLibraryStore = create<LibraryState>()(
  persist(
    (set, get) => ({
      savedAnalyses: [],
      savedFolders: [],
      historicalAnalysisSearchQuery: "",
      setHistoricalAnalysisSearchQuery: (query) => set({ historicalAnalysisSearchQuery: query }),
      saveAnalysis: async (analysis) => {
        set((state) => ({
          savedAnalyses: [...state.savedAnalyses, analysis],
        }));
        // Simulate async operation without failure
        await new Promise(resolve => setTimeout(resolve, 10));
      },
      deleteAnalysis: (id) =>
        set((state) => ({
          savedAnalyses: state.savedAnalyses.filter((a) => a.id !== id),
        })),
      updateSavedAnalysis: async (id, updatedAnalysis) => {
        set((state) => ({
          savedAnalyses: state.savedAnalyses.map((a) =>
            a.id === id ? { ...updatedAnalysis, updatedAt: new Date() } : a
          ),
        }));
        // Simulate async operation without failure
        await new Promise(resolve => setTimeout(resolve, 10));
      },
      addFolder: (folder) =>
        set((state) => ({
          savedFolders: [...state.savedFolders, folder],
        })),
      deleteFolder: (id) =>
        set((state) => ({
          savedFolders: state.savedFolders.filter((f) => f.id !== id),
          // Also remove folder reference from analyses
          savedAnalyses: state.savedAnalyses.map(a => 
            a.folderId === id ? { ...a, folderId: undefined } : a
          ),
        })),
      
      // All note and canvas state/actions have been moved to useCanvasStore
      
      loadSavedAnalyses: () => {
        // This is a no-op because zustand/persist handles loading from localStorage.
      },
      loadSavedFolders: () => {
        // This is a no-op because zustand/persist handles loading from localStorage.
      },
      createFolder: async (folderName) => {
        const newFolder: SavedFolder = {
          id: `folder_${Date.now()}`,
          name: folderName,
          createdAt: new Date(),
        };
        get().addFolder(newFolder);
      },
      moveAnalysisToFolder: (analysisId, folderId) => {
        set((state) => ({
          savedAnalyses: state.savedAnalyses.map((a) =>
            a.id === analysisId ? { ...a, folderId: folderId, updatedAt: new Date() } : a
          ),
        }));
      },
    }),
    {
      name: "library_planner_data",
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          try {
            const storedValue = JSON.parse(str);
            const cleanedValue = cleanState(storedValue);
            return JSON.stringify(cleanedValue);
          } catch {
            return str; // Return original string if parsing fails
          }
        },
        setItem: (name, value) => {
          try {
            const stateToStore = JSON.parse(value);
            const cleanedState = cleanState(stateToStore);
            localStorage.setItem(name, JSON.stringify(cleanedState));
          } catch {
            localStorage.setItem(name, value);
          }
        },
        removeItem: (name) => localStorage.removeItem(name),
      }), {
        reviver: dateReviver,
      }),
      partialize: (state) => ({
        savedAnalyses: state.savedAnalyses,
        savedFolders: state.savedFolders,
      }),
    }
  )
);
