// Enforce accessibility best practices using eslint-plugin-jsx-a11y
const { execSync } = require('child_process');

try {
  const output = execSync('npx eslint "src/**/*.{ts,tsx,js,jsx}" --plugin jsx-a11y --rule "jsx-a11y/*: 2"', { encoding: 'utf8' });
  console.log('Accessibility linting passed.');
} catch (e) {
  console.error('Accessibility linting failed. Please fix accessibility issues.');
  process.exit(1);
}
