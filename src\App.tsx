import { Suspense, lazy, useEffect } from "react";
// import { Toaster } from "@/components/ui/toaster"; // Removed import
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation, useNavigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { SavedAnalysisDialog } from "@/components/library/SavedAnalysisDialog";
import { PageLoader } from "@/components/ui/loading-fallback";
import { ThemeProvider as DesignSystemThemeProvider, injectGlobalStyles, debugDesignSystem } from "@/design-system";
import { ServerDevelopmentTools } from "@/components/ServerStatus";
import { ErrorBoundaryEnhanced } from "@/components/ErrorBoundaryEnhanced";
import { useComponentErrorTracking } from "@/hooks/useErrorBoundary";
import { GlobalErrorListener } from "@/components/GlobalErrorListener";
import { ErrorReporter } from "@/components/ErrorReporter";

// Lazy load heavy components
const Index = lazy(() => {
  console.log('🧭 Loading Index page...');
  return import("./pages/Index").then(module => {
    console.log('✅ Index page loaded successfully');
    return module;
  }).catch(error => {
    console.error('❌ Failed to load Index page:', error);
    throw error;
  });
});

const NotFound = lazy(() => {
  console.log('🧭 Loading NotFound page...');
  return import("./pages/NotFound").then(module => {
    console.log('✅ NotFound page loaded successfully');
    return module;
  }).catch(error => {
    console.error('❌ Failed to load NotFound page:', error);
    throw error;
  });
});

const LivingDataCanvasContainer = lazy(() => {
  console.log('🧭 Loading LivingDataCanvasContainer...');
  return import("@/components/visual-analysis/LivingDataCanvasWithFallback").then(module => {
    console.log('✅ LivingDataCanvasContainer loaded successfully');
    return { default: module.default };
  }).catch(error => {
    console.error('❌ Failed to load LivingDataCanvasContainer:', error);
    throw error;
  });
});

const LivingDataCanvasSafeContainer = lazy(() => {
  console.log('🧭 Loading LivingDataCanvasSafeContainer...');
  return import("@/components/visual-analysis/LivingDataCanvasSafe").then(module => {
    console.log('✅ LivingDataCanvasSafeContainer loaded successfully');
    return { default: module.LivingDataCanvasSafeContainer };
  }).catch(error => {
    console.error('❌ Failed to load LivingDataCanvasSafeContainer:', error);
    throw error;
  });
});

const queryClient = new QueryClient();

// Route logging component to debug navigation
const RouteLogger = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.log('🧭 Route changed:', {
      pathname: location.pathname,
      search: location.search,
      hash: location.hash,
      state: location.state,
      key: location.key
    });

    // Log browser history state
    console.log('🧭 Browser history state:', {
      length: window.history.length,
      state: window.history.state
    });

    // Log current URL
    console.log('🧭 Current URL:', window.location.href);

  }, [location]);

  useEffect(() => {
    console.log('🧭 RouteLogger mounted, initial location:', location);
    console.log('🧭 Navigate function available:', typeof navigate);
  }, []);

  return null;
};

// Suspense fallback with logging
const SuspenseLogger = () => {
  console.log('⏳ Suspense fallback triggered - component is loading...');

  useEffect(() => {
    console.log('⏳ SuspenseLogger mounted');
    return () => {
      console.log('✅ SuspenseLogger unmounted - component loaded');
    };
  }, []);

  return <PageLoader />;
};

// Route wrapper with logging
const LoggedRoute = ({ path, element, ...props }: { path: string; element: React.ReactElement; [key: string]: any }) => {
  console.log('🧭 Route matched:', path);
  return <Route path={path} element={element} {...props} />;
};

const App = () => {
  console.log('🚀 App component rendering...');

  // Add error tracking for the App component
  const { trackLifecycleError, trackRenderError } = useComponentErrorTracking('App');

  // Initialize design system
  useEffect(() => {
    console.log('🎨 Initializing design system...');
    try {
      injectGlobalStyles();
      console.log('✅ Global styles injected');
      if (process.env.NODE_ENV === 'development') {
        debugDesignSystem();
        console.log('✅ Design system debug completed');
      }
    } catch (error) {
      console.error('❌ Design system initialization failed:', error);
      trackLifecycleError('mount', error instanceof Error ? error : new Error(String(error)));
    }
  }, [trackLifecycleError]);

  // Add browser navigation event listeners
  useEffect(() => {
    console.log('🧭 Setting up navigation event listeners...');

    const handlePopState = (event: PopStateEvent) => {
      console.log('🧭 Browser back/forward navigation:', {
        state: event.state,
        url: window.location.href,
        pathname: window.location.pathname
      });
    };

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      console.log('🧭 Page about to unload:', window.location.href);
    };

    const handleHashChange = (event: HashChangeEvent) => {
      console.log('🧭 Hash changed:', {
        oldURL: event.oldURL,
        newURL: event.newURL
      });
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  try {
    console.log('🎯 App render attempt...');
    return (
      <GlobalErrorListener>
        <ErrorBoundaryEnhanced context="App Root" level="page" showDetails={true}>
          <QueryClientProvider client={queryClient}>
          <ErrorBoundaryEnhanced context="Theme Provider" level="feature">
            <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
              <DesignSystemThemeProvider>
                <ErrorBoundaryEnhanced context="UI Provider" level="feature">
                  <TooltipProvider>
                    {/* <Toaster /> */} {/* Removed Toaster from here */}
                    <Sonner />
                    <ErrorBoundaryEnhanced context="Dialogs" level="component">
                      <SavedAnalysisDialog />
                      <ServerDevelopmentTools />
                      <ErrorReporter showInDevelopment={true} />
                    </ErrorBoundaryEnhanced>
                    <ErrorBoundaryEnhanced context="Router" level="page">
                      <BrowserRouter>
                        <RouteLogger />
                        <ErrorBoundaryEnhanced context="Route Suspense" level="feature">
                          <Suspense fallback={<SuspenseLogger />}>
                            <Routes>
                              <LoggedRoute path="/" element={
                                <ErrorBoundaryEnhanced context="Index Page" level="page">
                                  <Index />
                                </ErrorBoundaryEnhanced>
                              } />
                              <LoggedRoute path="/canvas" element={
                                <ErrorBoundaryEnhanced context="Canvas Page" level="page">
                                  <LivingDataCanvasContainer />
                                </ErrorBoundaryEnhanced>
                              } />
                              <LoggedRoute path="/canvas-safe" element={
                                <ErrorBoundaryEnhanced context="Safe Canvas Page" level="page">
                                  <LivingDataCanvasSafeContainer />
                                </ErrorBoundaryEnhanced>
                              } />
                              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                              <LoggedRoute path="*" element={
                                <ErrorBoundaryEnhanced context="Not Found Page" level="page">
                                  <NotFound />
                                </ErrorBoundaryEnhanced>
                              } />
                            </Routes>
                          </Suspense>
                        </ErrorBoundaryEnhanced>
                      </BrowserRouter>
                    </ErrorBoundaryEnhanced>
                  </TooltipProvider>
                </ErrorBoundaryEnhanced>
              </DesignSystemThemeProvider>
            </ThemeProvider>
          </ErrorBoundaryEnhanced>
        </QueryClientProvider>
      </ErrorBoundaryEnhanced>
      </GlobalErrorListener>
    );
  } catch (error) {
    console.error('❌ App render failed:', error);
    trackRenderError(error instanceof Error ? error : new Error(String(error)));
    return (
      <div style={{
        padding: '20px',
        backgroundColor: 'red',
        color: 'white',
        fontSize: '18px',
        minHeight: '100vh'
      }}>
        ❌ App Render Failed: {error instanceof Error ? error.message : 'Unknown error'}
        <br />
        Check console for details.
      </div>
    );
  }
};

export default App;
