import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { X, Plus, Save } from "lucide-react";
import { CharacterPersona } from "@/types/conversation";
import { useToast } from "@/hooks/use-toast";
import { generateUUID } from '@/utils/uuid';

interface CharacterPersonaFormProps {
  onSave: (persona: CharacterPersona) => void;
  onCancel: () => void;
  initialData?: CharacterPersona | null;
}

const defaultFormData = {
  name: "",
  coreRole: "",
  personalityTraits: [] as string[],
  background: "",
  communicationStyle: "",
  expertise: [] as string[],
  biasesWorldview: "",
  openMindedness: 5,
  gender: 'neutral' as 'male' | 'female' | 'neutral',
};

export const CharacterPersonaForm: React.FC<CharacterPersonaFormProps> = ({
  onSave,
  onCancel,
  initialData,
}) => {
  const [formData, setFormData] = useState(defaultFormData);
  const [newTrait, setNewTrait] = useState("");
  const [newExpertise, setNewExpertise] = useState("");
  const { toast } = useToast();

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name,
        coreRole: initialData.coreRole,
        personalityTraits: [...initialData.personalityTraits],
        background: initialData.background,
        communicationStyle: initialData.communicationStyle,
        expertise: [...initialData.expertise],
        biasesWorldview: initialData.biasesWorldview,
        openMindedness: initialData.openMindedness ?? 5,
        gender: initialData.gender ?? 'neutral',
      });
    } else {
      setFormData(defaultFormData);
    }
  }, [initialData]);

  const addTrait = () => {
    if (newTrait.trim() && !formData.personalityTraits.includes(newTrait.trim())) {
      setFormData(prev => ({
        ...prev,
        personalityTraits: [...prev.personalityTraits, newTrait.trim()]
      }));
      setNewTrait("");
    }
  };

  const removeTrait = (trait: string) => {
    setFormData(prev => ({
      ...prev,
      personalityTraits: prev.personalityTraits.filter(t => t !== trait)
    }));
  };

  const addExpertise = () => {
    if (newExpertise.trim() && !formData.expertise.includes(newExpertise.trim())) {
      setFormData(prev => ({
        ...prev,
        expertise: [...prev.expertise, newExpertise.trim()]
      }));
      setNewExpertise("");
    }
  };

  const removeExpertise = (expertise: string) => {
    setFormData(prev => ({
      ...prev,
      expertise: prev.expertise.filter(e => e !== expertise)
    }));
  };

  const handleSave = () => {
    if (!formData.name.trim() || !formData.coreRole.trim()) {
      toast({
        title: "Missing Required Fields",
        description: "Please fill in the character name and core role.",
        variant: "destructive"
      });
      return;
    }

    const personaToSave: CharacterPersona = {
      id: initialData?.id || generateUUID(),
      createdAt: initialData?.createdAt || new Date(),
      updatedAt: new Date(),
      name: formData.name.trim(),
      coreRole: formData.coreRole.trim(),
      personalityTraits: formData.personalityTraits,
      background: formData.background.trim(),
      communicationStyle: formData.communicationStyle.trim(),
      expertise: formData.expertise,
      biasesWorldview: formData.biasesWorldview.trim(),
      openMindedness: formData.openMindedness,
      gender: formData.gender,
    };
    
    onSave(personaToSave);
  };

  return (
    <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Character Name / Title *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Professor Alistair Finch"
              />
            </div>
            
            <div>
              <Label htmlFor="coreRole">Core Role *</Label>
              <Input
                id="coreRole"
                value={formData.coreRole}
                onChange={(e) => setFormData(prev => ({ ...prev, coreRole: e.target.value }))}
                placeholder="A cynical, disgraced historian specializing in forgotten empires"
              />
            </div>
        </div>

        <div>
            <Label>Gender</Label>
            <RadioGroup
              value={formData.gender}
              onValueChange={(value: 'male' | 'female' | 'neutral') => setFormData(prev => ({ ...prev, gender: value }))}
              className="flex gap-4 pt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="male" id="male" />
                <Label htmlFor="male">Male</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="female" id="female" />
                <Label htmlFor="female">Female</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="neutral" id="neutral" />
                <Label htmlFor="neutral">Neutral / Not specified</Label>
              </div>
            </RadioGroup>
        </div>

        <div>
            <Label>Personality Traits</Label>
            <div className="flex gap-2 mb-2">
              <Input
                value={newTrait}
                onChange={(e) => setNewTrait(e.target.value)}
                placeholder="Add a personality trait..."
                onKeyPress={(e) => e.key === 'Enter' && addTrait()}
              />
              <Button type="button" onClick={addTrait}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.personalityTraits.map(trait => (
                <Badge key={trait} variant="secondary" className="cursor-pointer" onClick={() => removeTrait(trait)}>
                  {trait} <X className="h-3 w-3 ml-1" />
                </Badge>
              ))}
            </div>
        </div>

        <div>
            <Label htmlFor="background">Background & Biography</Label>
            <Textarea
              id="background"
              value={formData.background}
              onChange={(e) => setFormData(prev => ({ ...prev, background: e.target.value }))}
              placeholder="Detailed history, motivations, and key life events..."
              rows={4}
            />
        </div>

        <div>
            <Label htmlFor="communicationStyle">Communication Style & Quirks</Label>
            <Textarea
              id="communicationStyle"
              value={formData.communicationStyle}
              onChange={(e) => setFormData(prev => ({ ...prev, communicationStyle: e.target.value }))}
              placeholder="How the character speaks, tone, vocabulary, common phrases..."
              rows={3}
            />
        </div>

        <div>
            <Label>Areas of Expertise</Label>
            <div className="flex gap-2 mb-2">
              <Input
                value={newExpertise}
                onChange={(e) => setNewExpertise(e.target.value)}
                placeholder="Add an area of expertise..."
                onKeyPress={(e) => e.key === 'Enter' && addExpertise()}
              />
              <Button type="button" onClick={addExpertise}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.expertise.map(expertise => (
                <Badge key={expertise} variant="outline" className="cursor-pointer" onClick={() => removeExpertise(expertise)}>
                  {expertise} <X className="h-3 w-3 ml-1" />
                </Badge>
              ))}
            </div>
        </div>

        <div>
            <Label htmlFor="biasesWorldview">Inherent Biases & Worldview</Label>
            <Textarea
              id="biasesWorldview"
              value={formData.biasesWorldview}
              onChange={(e) => setFormData(prev => ({ ...prev, biasesWorldview: e.target.value }))}
              placeholder="Fundamental beliefs, blind spots, philosophical leanings..."
              rows={3}
            />
        </div>

        <div>
            <Label htmlFor="openMindedness">Open-mindedness (1: Closed-minded, 10: Very Open-minded)</Label>
            <div className="flex items-center gap-4 pt-2">
                <Slider
                    id="openMindedness"
                    min={1}
                    max={10}
                    step={1}
                    value={[formData.openMindedness]}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, openMindedness: value[0] }))}
                />
                <span className="font-semibold text-lg w-12 text-center rounded-md bg-secondary p-1">{formData.openMindedness}</span>
            </div>
        </div>

        <div className="flex gap-2 justify-end">
            <Button variant="outline" onClick={onCancel}>
              Back to Library
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save Character
            </Button>
        </div>
    </div>
  );
};
