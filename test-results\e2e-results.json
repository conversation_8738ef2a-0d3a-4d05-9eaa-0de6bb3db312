{"config": {"configFile": "F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\playwright.config.ts", "rootDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\globalSetup.ts", "globalTeardown": "F:\\AI App Builds\\My_Github_Projects\\chat-craft-trainer-pro-80\\src\\__tests__\\setup\\globalTeardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "chromium", "name": "chromium", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "firefox", "name": "firefox", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "webkit", "name": "webkit", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "iPad", "name": "iPad", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "High DPI", "name": "High DPI", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Slow Network", "name": "Slow Network", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/test-results/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"test-type": "e2e", "app-version": "0.0.0", "node-version": "v22.15.0"}, "id": "Offline", "name": "Offline", "testDir": "F:/AI App Builds/My_Github_Projects/chat-craft-trainer-pro-80/src/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.0", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "Error: Process from config.webServer was not able to start. Exit code: 1", "stack": "Error: Process from config.webServer was not able to start. Exit code: 1"}], "stats": {"startTime": "2025-06-18T19:34:44.797Z", "duration": 76662.58499999999, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}