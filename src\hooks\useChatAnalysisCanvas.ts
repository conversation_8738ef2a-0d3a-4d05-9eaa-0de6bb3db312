import { useState, useEffect, useCallback } from 'react';
import { AnalysisResult } from '@/types/conversation';
import { ChatAnalysisCanvasData, AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';
import { ChatAnalysisCanvasService } from '@/services/visual-analysis/chatAnalysisCanvasService';
import { ChatAnalysisApi } from '@/services/api/chatAnalysisApi';
import { ClusterApi } from '@/services/api/clusterApi';
import { SimulationApi } from '@/services/api/simulationApi';
import { performanceProfiler } from '@/services/performance/performanceProfiler';
import { useDataProcessingWorker } from '@/hooks/useWebWorker';
import { useAnalysisStore } from '@/stores/useAnalysisStore';
import { useLibraryStore } from '@/stores/useLibraryStore';

/**
 * Hook for managing chat analysis canvas data integration
 */
export const useChatAnalysisCanvas = () => {
  const [canvasData, setCanvasData] = useState<ChatAnalysisCanvasData | null>(null);
  const [clusters, setClusters] = useState<AnalysisCluster[]>([]);
  const [simulations, setSimulations] = useState<ChatSimulation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get analysis results from stores
  const { analysisResults } = useAnalysisStore();
  const { savedAnalyses } = useLibraryStore();

  // Performance optimization hooks
  const dataProcessingWorker = useDataProcessingWorker();
  const { processAnalysisResults, calculateSimilarities, isWorkerAvailable } = dataProcessingWorker;

  /**
   * Load canvas data from analysis results and API with performance optimization
   */
  const loadCanvasData = useCallback(async () => {
    performanceProfiler.startTiming('canvas-data-loading');
    setIsLoading(true);
    setError(null);

    try {
      // Load data from API services
      const [apiAnalysisResults, apiClusters, apiSimulations] = await Promise.all([
        ChatAnalysisApi.getAllAnalysisResults(),
        ClusterApi.getAllClusters(),
        SimulationApi.getAllSimulations()
      ]);

      // Combine API results with local store data
      const allAnalysisResults: AnalysisResult[] = [
        ...apiAnalysisResults,
        ...analysisResults,
        ...savedAnalyses.flatMap(saved => saved.results)
      ];

      // Remove duplicates based on ID
      const uniqueAnalysisResults = allAnalysisResults.filter((result, index, self) =>
        index === self.findIndex(r => r.id === result.id)
      );

      // Update local state with API data
      setClusters(apiClusters);
      setSimulations(apiSimulations);

      if (uniqueAnalysisResults.length === 0) {
        setCanvasData({
          nodes: [],
          connections: [],
          clusters: apiClusters,
          simulations: apiSimulations,
          metadata: {
            lastUpdated: new Date(),
            version: '1.0.0'
          }
        });
        performanceProfiler.endTiming('canvas-data-loading');
        return;
      }

      // Use web worker for large datasets
      if (isWorkerAvailable && uniqueAnalysisResults.length > 100) {
        performanceProfiler.startTiming('worker-data-processing');

        try {
          // Process analysis results in worker
          const processedData = await processAnalysisResults(uniqueAnalysisResults);

          // Calculate similarities in worker
          const similarityData = await calculateSimilarities(uniqueAnalysisResults) as SimilarityResult;

          performanceProfiler.endTiming('worker-data-processing');
          performanceProfiler.recordMetric('worker-processing-success', {
            resultCount: uniqueAnalysisResults.length,
            similarityCount: similarityData.similarities.length
          });

          // Generate connections from similarity data
          const connections = similarityData.similarities.map((sim: { sourceId: string; targetId: string; score: number; factors: unknown }) => ({
            id: `${sim.sourceId}-${sim.targetId}`,
            source: sim.sourceId,
            target: sim.targetId,
            strength: sim.score,
            type: 'similarity',
            metadata: { factors: sim.factors }
          }));

          // Convert to canvas data
          const newCanvasData = ChatAnalysisCanvasService.analysisResultsToCanvasData(
            uniqueAnalysisResults,
            connections,
            apiClusters
          );

          newCanvasData.simulations = apiSimulations;
          setCanvasData(newCanvasData);

        } catch (workerError) {
          console.warn('Worker processing failed, falling back to main thread:', workerError);
          // Fallback to main thread processing
          await processDataOnMainThread(uniqueAnalysisResults, apiClusters, apiSimulations);
        }
      } else {
        // Process on main thread for smaller datasets
        await processDataOnMainThread(uniqueAnalysisResults, apiClusters, apiSimulations);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load canvas data');
      console.error('Error loading canvas data:', err);

      // Fallback to local data only
      try {
        const localAnalysisResults: AnalysisResult[] = [
          ...analysisResults,
          ...savedAnalyses.flatMap(saved => saved.results)
        ];

        await processDataOnMainThread(localAnalysisResults, clusters, simulations);
      } catch (fallbackErr) {
        console.error('Fallback data loading failed:', fallbackErr);
      }
    } finally {
      setIsLoading(false);
      performanceProfiler.endTiming('canvas-data-loading');
    }
  }, [analysisResults, savedAnalyses, clusters, simulations, isWorkerAvailable, processAnalysisResults, calculateSimilarities]);

  /**
   * Process data on main thread (fallback)
   */
  const processDataOnMainThread = useCallback(async (
    analysisResults: AnalysisResult[],
    clusters: unknown[],
    simulations: unknown[]
  ) => {
    performanceProfiler.startTiming('main-thread-processing');

    // Generate connections based on analysis similarity
    const connections = ChatAnalysisCanvasService.generateConnectionsFromAnalysisResults(analysisResults);

    // Convert to canvas data
    const newCanvasData = ChatAnalysisCanvasService.analysisResultsToCanvasData(
      analysisResults,
      connections,
      clusters as AnalysisCluster[]
    );

    // Add simulations
    newCanvasData.simulations = simulations as ChatSimulation[];
    setCanvasData(newCanvasData);

    performanceProfiler.endTiming('main-thread-processing');
  }, []);

  /**
   * Create a new cluster from selected node IDs
   */
  const createCluster = useCallback(async (nodeIds: string[], name: string, description?: string) => {
    try {
      const newCluster = await ClusterApi.createCluster({
        name,
        description,
        nodeIds,
        color: Math.floor(Math.random() * 0xffffff), // Random color
        tags: ['user-created']
      });

      setClusters(prev => [...prev, newCluster]);

      // Update canvas data to reflect cluster changes
      if (canvasData) {
        const updatedCanvasData = {
          ...canvasData,
          clusters: [...clusters, newCluster],
          nodes: canvasData.nodes.map(node =>
            nodeIds.includes(node.id)
              ? { ...node, clusterId: newCluster.id }
              : node
          )
        };
        setCanvasData(updatedCanvasData);
      }

      return newCluster;
    } catch (error) {
      console.error('Error creating cluster:', error);
      setError('Failed to create cluster');
      throw error;
    }
  }, [clusters, canvasData]);

  /**
   * Update an existing cluster
   */
  const updateCluster = useCallback(async (clusterId: string, updates: Partial<AnalysisCluster>) => {
    try {
      const updatedCluster = await ClusterApi.updateCluster(clusterId, updates);

      setClusters(prev => prev.map(cluster =>
        cluster.id === clusterId ? updatedCluster : cluster
      ));

      // Update canvas data
      if (canvasData) {
        const updatedCanvasData = {
          ...canvasData,
          clusters: clusters.map(cluster =>
            cluster.id === clusterId ? updatedCluster : cluster
          )
        };
        setCanvasData(updatedCanvasData);
      }
    } catch (error) {
      console.error('Error updating cluster:', error);
      setError('Failed to update cluster');
      throw error;
    }
  }, [clusters, canvasData]);

  /**
   * Delete a cluster
   */
  const deleteCluster = useCallback(async (clusterId: string) => {
    try {
      await ClusterApi.deleteCluster(clusterId);

      setClusters(prev => prev.filter(cluster => cluster.id !== clusterId));

      // Update canvas data to remove cluster references
      if (canvasData) {
        const updatedCanvasData = {
          ...canvasData,
          clusters: clusters.filter(cluster => cluster.id !== clusterId),
          nodes: canvasData.nodes.map(node =>
            node.clusterId === clusterId
              ? { ...node, clusterId: undefined }
              : node
          )
        };
        setCanvasData(updatedCanvasData);
      }
    } catch (error) {
      console.error('Error deleting cluster:', error);
      setError('Failed to delete cluster');
      throw error;
    }
  }, [clusters, canvasData]);

  /**
   * Create a new simulation
   */
  const createSimulation = useCallback(async (
    name: string,
    description: string,
    sourceNodeIds: string[],
    prompts: any[]
  ) => {
    try {
      const newSimulation = await SimulationApi.createSimulation({
        name,
        description,
        sourceNodeIds,
        prompts,
        results: [],
        status: 'pending'
      });

      setSimulations(prev => [...prev, newSimulation]);

      // Update canvas data
      if (canvasData) {
        const updatedCanvasData = {
          ...canvasData,
          simulations: [...simulations, newSimulation]
        };
        setCanvasData(updatedCanvasData);
      }

      return newSimulation;
    } catch (error) {
      console.error('Error creating simulation:', error);
      setError('Failed to create simulation');
      throw error;
    }
  }, [simulations, canvasData]);

  /**
   * Update simulation status and results
   */
  const updateSimulation = useCallback(async (simulationId: string, updates: Partial<ChatSimulation>) => {
    try {
      const updatedSimulation = await SimulationApi.updateSimulation(simulationId, updates);

      setSimulations(prev => prev.map(sim =>
        sim.id === simulationId ? updatedSimulation : sim
      ));

      // Update canvas data
      if (canvasData) {
        const updatedCanvasData = {
          ...canvasData,
          simulations: simulations.map(sim =>
            sim.id === simulationId ? updatedSimulation : sim
          )
        };
        setCanvasData(updatedCanvasData);
      }
    } catch (error) {
      console.error('Error updating simulation:', error);
      setError('Failed to update simulation');
      throw error;
    }
  }, [simulations, canvasData]);

  /**
   * Get analysis record by node ID
   */
  const getAnalysisRecordByNodeId = useCallback((nodeId: string): AnalysisResult | null => {
    if (!canvasData) return null;
    
    const node = canvasData.nodes.find(n => n.id === nodeId);
    return node?.analysisRecord || null;
  }, [canvasData]);

  /**
   * Get cluster by ID
   */
  const getClusterById = useCallback((clusterId: string): AnalysisCluster | null => {
    return clusters.find(cluster => cluster.id === clusterId) || null;
  }, [clusters]);

  /**
   * Get nodes in a cluster
   */
  const getNodesInCluster = useCallback((clusterId: string) => {
    if (!canvasData) return [];
    return canvasData.nodes.filter(node => node.clusterId === clusterId);
  }, [canvasData]);

  // Load canvas data when dependencies change
  useEffect(() => {
    loadCanvasData();
  }, [loadCanvasData]);

  return {
    canvasData,
    clusters,
    simulations,
    isLoading,
    error,
    loadCanvasData,
    createCluster,
    updateCluster,
    deleteCluster,
    createSimulation,
    updateSimulation,
    getAnalysisRecordByNodeId,
    getClusterById,
    getNodesInCluster
  };
};

type SimilarityResult = {
  similarities: Array<{ sourceId: string; targetId: string; score: number; factors: unknown }>;
  statistics: {
    totalComparisons: number;
    meaningfulSimilarities: number;
    processingTime: number;
    averageScore: number;
  };
};
