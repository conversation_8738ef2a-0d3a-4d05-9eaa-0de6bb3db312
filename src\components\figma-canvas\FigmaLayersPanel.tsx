import React, { useState } from 'react';
import { 
  Eye, 
  EyeOff, 
  Lock, 
  Unlock, 
  Plus, 
  Trash2, 
  GripVertical,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Layer, DrawingObject } from '@/types/figma';

interface FigmaLayersPanelProps {
  className?: string;
}

export const FigmaLayersPanel: React.FC<FigmaLayersPanelProps> = ({ className }) => {
  const {
    layers,
    objects,
    activeLayerId,
    selectedObjectIds,
    addLayer,
    updateLayer,
    deleteLayer,
    setActiveLayer,
    reorderLayers,
    selectObjects,
  } = useFigmaCanvasStore();

  const [editingLayerId, setEditingLayerId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [expandedLayers, setExpandedLayers] = useState<Set<string>>(new Set());

  // Get sorted layers
  const sortedLayers = Object.values(layers).sort((a, b) => b.order - a.order); // Reverse order for display

  const handleAddLayer = () => {
    const layerName = `Layer ${Object.keys(layers).length + 1}`;
    const layerId = addLayer(layerName);
    setActiveLayer(layerId);
  };

  const handleDeleteLayer = (layerId: string) => {
    if (Object.keys(layers).length <= 1) return; // Keep at least one layer
    deleteLayer(layerId);
  };

  const handleToggleVisibility = (layerId: string) => {
    const layer = layers[layerId];
    updateLayer(layerId, { visible: !layer.visible });
  };

  const handleToggleLock = (layerId: string) => {
    const layer = layers[layerId];
    updateLayer(layerId, { locked: !layer.locked });
  };

  const handleStartEditing = (layerId: string, currentName: string) => {
    setEditingLayerId(layerId);
    setEditingName(currentName);
  };

  const handleFinishEditing = () => {
    if (editingLayerId && editingName.trim()) {
      updateLayer(editingLayerId, { name: editingName.trim() });
    }
    setEditingLayerId(null);
    setEditingName('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleFinishEditing();
    } else if (e.key === 'Escape') {
      setEditingLayerId(null);
      setEditingName('');
    }
  };

  const handleToggleExpanded = (layerId: string) => {
    const newExpanded = new Set(expandedLayers);
    if (newExpanded.has(layerId)) {
      newExpanded.delete(layerId);
    } else {
      newExpanded.add(layerId);
    }
    setExpandedLayers(newExpanded);
  };

  const handleObjectClick = (objectId: string, e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      // Add to selection
      const newSelection = selectedObjectIds.includes(objectId)
        ? selectedObjectIds.filter(id => id !== objectId)
        : [...selectedObjectIds, objectId];
      selectObjects(newSelection);
    } else {
      // Replace selection
      selectObjects([objectId]);
    }
  };

  const getObjectIcon = (type: string) => {
    const icons: Record<string, string> = {
      rectangle: '▭',
      circle: '●',
      line: '─',
      arrow: '→',
      text: 'T',
      pen: '✎',
      polygon: '⬟',
      star: '★',
      image: '🖼',
    };
    return icons[type] || '?';
  };

  return (
    <div className={cn("w-64 bg-white border-l border-gray-200 flex flex-col", className)}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">Layers</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddLayer}
            className="w-6 h-6 p-0"
            title="Add Layer"
          >
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Layers List */}
      <ScrollArea className="flex-1">
        <OptimizedList
          items={sortedLayers}
          renderItem={(layer) => (
            <LayerItem
              key={layer.id}
              layer={layer}
              objects={objects}
              isActive={layer.id === activeLayerId}
              isExpanded={expandedLayers.has(layer.id)}
              selectedObjectIds={selectedObjectIds}
              editingLayerId={editingLayerId}
              editingName={editingName}
              onSetActive={() => setActiveLayer(layer.id)}
              onToggleVisibility={() => handleToggleVisibility(layer.id)}
              onToggleLock={() => handleToggleLock(layer.id)}
              onDelete={() => handleDeleteLayer(layer.id)}
              onStartEditing={(name) => handleStartEditing(layer.id, name)}
              onFinishEditing={handleFinishEditing}
              onEditingNameChange={setEditingName}
              onKeyDown={handleKeyDown}
              onToggleExpanded={() => handleToggleExpanded(layer.id)}
              onObjectClick={handleObjectClick}
              getObjectIcon={getObjectIcon}
            />
          )}
          itemHeight={48} // Adjust as needed for your layer row height
          containerHeight={400} // Adjust as needed for your scrollable area
          className="p-2 space-y-1"
        />
      </ScrollArea>
    </div>
  );
};

interface LayerItemProps {
  layer: Layer;
  objects: Record<string, DrawingObject>;
  isActive: boolean;
  isExpanded: boolean;
  selectedObjectIds: string[];
  editingLayerId: string | null;
  editingName: string;
  onSetActive: () => void;
  onToggleVisibility: () => void;
  onToggleLock: () => void;
  onDelete: () => void;
  onStartEditing: (name: string) => void;
  onFinishEditing: () => void;
  onEditingNameChange: (name: string) => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  onToggleExpanded: () => void;
  onObjectClick: (objectId: string, e: React.MouseEvent) => void;
  getObjectIcon: (type: string) => string;
}

const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  objects,
  isActive,
  isExpanded,
  selectedObjectIds,
  editingLayerId,
  editingName,
  onSetActive,
  onToggleVisibility,
  onToggleLock,
  onDelete,
  onStartEditing,
  onFinishEditing,
  onEditingNameChange,
  onKeyDown,
  onToggleExpanded,
  onObjectClick,
  getObjectIcon,
}) => {
  const hasObjects = layer.objects.length > 0;

  return (
    <div className="space-y-1">
      {/* Layer Header */}
      <div
        className={cn(
          "flex items-center gap-1 p-2 rounded hover:bg-gray-50 cursor-pointer group",
          isActive && "bg-blue-50 border border-blue-200"
        )}
        onClick={onSetActive}
      >
        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          className="w-4 h-4 p-0 opacity-60 hover:opacity-100"
          onClick={(e) => {
            e.stopPropagation();
            onToggleExpanded();
          }}
        >
          {hasObjects ? (
            isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )
          ) : (
            <div className="w-3 h-3" />
          )}
        </Button>

        {/* Drag Handle */}
        <GripVertical className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100" />

        {/* Layer Name */}
        <div className="flex-1 min-w-0">
          {editingLayerId === layer.id ? (
            <Input
              value={editingName}
              onChange={(e) => onEditingNameChange(e.target.value)}
              onBlur={onFinishEditing}
              onKeyDown={onKeyDown}
              className="h-6 px-1 text-xs"
              autoFocus
            />
          ) : (
            <span
              className="text-xs truncate block"
              onDoubleClick={() => onStartEditing(layer.name)}
            >
              {layer.name}
            </span>
          )}
        </div>

        {/* Object Count */}
        {hasObjects && (
          <span className="text-xs text-gray-400 px-1">
            {layer.objects.length}
          </span>
        )}

        {/* Controls */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
          <Button
            variant="ghost"
            size="sm"
            className="w-4 h-4 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleVisibility();
            }}
            title={layer.visible ? "Hide Layer" : "Show Layer"}
          >
            {layer.visible ? (
              <Eye className="w-3 h-3" />
            ) : (
              <EyeOff className="w-3 h-3 text-gray-400" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="w-4 h-4 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleLock();
            }}
            title={layer.locked ? "Unlock Layer" : "Lock Layer"}
          >
            {layer.locked ? (
              <Lock className="w-3 h-3 text-gray-400" />
            ) : (
              <Unlock className="w-3 h-3" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="w-4 h-4 p-0 text-red-500 hover:text-red-700"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            title="Delete Layer"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Layer Objects */}
      {isExpanded && hasObjects && (
        <div className="ml-6 space-y-1">
          {layer.objects.map((objectId) => {
            const object = objects[objectId];
            if (!object) return null;

            const isSelected = selectedObjectIds.includes(objectId);

            return (
              <div
                key={objectId}
                className={cn(
                  "flex items-center gap-2 p-1 rounded text-xs cursor-pointer hover:bg-gray-50",
                  isSelected && "bg-blue-50 border border-blue-200"
                )}
                onClick={(e) => onObjectClick(objectId, e)}
              >
                <span className="w-4 text-center">
                  {getObjectIcon(object.type)}
                </span>
                <span className="flex-1 truncate">
                  {object.name}
                </span>
                {!object.visible && (
                  <EyeOff className="w-3 h-3 text-gray-400" />
                )}
                {object.locked && (
                  <Lock className="w-3 h-3 text-gray-400" />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
