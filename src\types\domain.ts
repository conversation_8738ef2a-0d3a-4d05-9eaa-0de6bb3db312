/**
 * @file domain.ts
 *
 * NOTE: This file is not used in the main React application or feature code.
 * It is a reference for enterprise domain models only.
 *
 * If you do not need these types for backend, documentation, or future reference,
 * you may safely delete this file.
 *
 * See src/types/conversation.ts for all app-facing types.
 */

/**
 * Domain models for enterprise-grade production applications.
 * Extend as needed for comprehensive type safety.
 */

export type UUID = string;

export type Language = "en" | "es" | "fr" | "de" | "zh" | "ja" | "other";

export interface User {
  id: UUID;
  name: string;
  email: string;
  avatarUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language: Language;
  notificationsEnabled: boolean;
  darkMode: boolean;
  betaFeaturesEnabled?: boolean;
}

export type ConversationStyle =
  | "professional"
  | "casual"
  | "scientific"
  | "academic"
  | "medical";

export interface AnalysisResult {
  id: UUID;
  question: string;
  style: ConversationStyle;
  model: string;
  analysis: string;
  timestamp: Date;
  analysisType: "multiple" | "deep" | "character";
  refinements?: Refinement[];
}

export interface Refinement {
  prompt: string;
  refinedAnalysis: string;
  timestamp: Date;
}

export interface SavedAnalysis {
  id: UUID;
  title: string;
  results: AnalysisResult[];
  createdAt: Date;
  updatedAt: Date;
  userId?: UUID;
  folderId?: UUID;
}

export interface SavedFolder {
  id: UUID;
  name: string;
  createdAt: Date;
  userId?: UUID;
}

export interface UserNote {
  id: UUID;
  questionId: string;
  linkedAnswerIds: string[];
  noteText: string;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  originalAnalysis: string;
  analysisType: "multiple" | "deep" | "character";
}

