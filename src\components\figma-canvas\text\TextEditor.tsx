import React, { useState, useRef, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Type
} from 'lucide-react';
import { TextObject, TextStyle } from '@/types/figma';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

interface TextEditorProps {
  textObject: TextObject;
  onUpdate: (updates: Partial<TextObject>) => void;
  onClose: () => void;
  className?: string;
}

export const TextEditor: React.FC<TextEditorProps> = ({
  textObject,
  onUpdate,
  onClose,
  className
}) => {
  const [content, setContent] = useState(textObject.content);
  const [textStyle, setTextStyle] = useState(textObject.textStyle);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Font families available
  const fontFamilies = [
    'Inter, sans-serif',
    'Arial, sans-serif',
    'Helvetica, sans-serif',
    'Times New Roman, serif',
    'Georgia, serif',
    'Courier New, monospace',
    'Monaco, monospace',
    'Comic Sans MS, cursive',
    'Impact, sans-serif',
    'Verdana, sans-serif',
  ];

  // Font sizes
  const fontSizes = [8, 10, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 64, 72, 96];

  // Font weights
  const fontWeights = [
    { value: '100', label: 'Thin' },
    { value: '200', label: 'Extra Light' },
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semi Bold' },
    { value: '700', label: 'Bold' },
    { value: '800', label: 'Extra Bold' },
    { value: '900', label: 'Black' },
  ];

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
      textareaRef.current.select();
    }
  }, []);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    onUpdate({ content: newContent });
  };

  const handleStyleChange = (styleUpdates: Partial<TextStyle>) => {
    const newTextStyle = { ...textStyle, ...styleUpdates };
    setTextStyle(newTextStyle);
    onUpdate({ textStyle: newTextStyle });
  };

  const toggleBold = () => {
    const newWeight = textStyle.fontWeight === 'bold' ? 'normal' : 'bold';
    handleStyleChange({ fontWeight: newWeight });
  };

  const toggleItalic = () => {
    const newStyle = textStyle.fontStyle === 'italic' ? 'normal' : 'italic';
    handleStyleChange({ fontStyle: newStyle });
  };

  const toggleUnderline = () => {
    const newDecoration = textStyle.textDecoration === 'underline' ? 'none' : 'underline';
    handleStyleChange({ textDecoration: newDecoration });
  };

  const toggleStrikethrough = () => {
    const newDecoration = textStyle.textDecoration === 'line-through' ? 'none' : 'line-through';
    handleStyleChange({ textDecoration: newDecoration });
  };

  const setAlignment = (align: 'left' | 'center' | 'right' | 'justify') => {
    handleStyleChange({ textAlign: align });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      onClose();
    }
  };

  return (
    <div className={cn("bg-white border border-gray-200 rounded-lg shadow-lg p-4 space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Type className="w-4 h-4" />
          <span className="text-sm font-medium">Text Editor</span>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          ×
        </Button>
      </div>

      {/* Text Content */}
      <div>
        <Label htmlFor="text-content" className="text-xs">Content</Label>
        <textarea
          ref={textareaRef}
          id="text-content"
          value={content}
          onChange={(e) => handleContentChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full h-24 p-2 border border-gray-200 rounded text-sm resize-none"
          placeholder="Enter your text..."
          style={{
            fontFamily: textStyle.fontFamily,
            fontSize: textStyle.fontSize,
            fontWeight: textStyle.fontWeight,
            fontStyle: textStyle.fontStyle,
            textAlign: textStyle.textAlign,
            textDecoration: textStyle.textDecoration,
            lineHeight: textStyle.lineHeight,
            letterSpacing: textStyle.letterSpacing,
          }}
        />
      </div>

      {/* Font Family */}
      <div>
        <Label className="text-xs">Font Family</Label>
        <Select
          value={textStyle.fontFamily}
          onValueChange={(value) => handleStyleChange({ fontFamily: value })}
        >
          <SelectTrigger className="h-8 text-xs">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {fontFamilies.map((font) => (
              <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                {font.split(',')[0]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Font Size and Weight */}
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label className="text-xs">Size</Label>
          <Select
            value={textStyle.fontSize.toString()}
            onValueChange={(value) => handleStyleChange({ fontSize: parseInt(value) })}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}px
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label className="text-xs">Weight</Label>
          <Select
            value={textStyle.fontWeight.toString()}
            onValueChange={(value) => handleStyleChange({ fontWeight: value as TextStyle['fontWeight'] })}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontWeights.map((weight) => (
                <SelectItem key={weight.value} value={weight.value}>
                  {weight.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Text Formatting */}
      <div>
        <Label className="text-xs">Formatting</Label>
        <div className="flex items-center gap-1 mt-1">
          <Button
            variant={textStyle.fontWeight === 'bold' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleBold}
            className="w-8 h-8 p-0"
            title="Bold"
          >
            <Bold className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.fontStyle === 'italic' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleItalic}
            className="w-8 h-8 p-0"
            title="Italic"
          >
            <Italic className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.textDecoration === 'underline' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleUnderline}
            className="w-8 h-8 p-0"
            title="Underline"
          >
            <Underline className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.textDecoration === 'line-through' ? 'default' : 'ghost'}
            size="sm"
            onClick={toggleStrikethrough}
            className="w-8 h-8 p-0"
            title="Strikethrough"
          >
            <Strikethrough className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Text Alignment */}
      <div>
        <Label className="text-xs">Alignment</Label>
        <div className="flex items-center gap-1 mt-1">
          <Button
            variant={textStyle.textAlign === 'left' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setAlignment('left')}
            className="w-8 h-8 p-0"
            title="Align Left"
          >
            <AlignLeft className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.textAlign === 'center' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setAlignment('center')}
            className="w-8 h-8 p-0"
            title="Align Center"
          >
            <AlignCenter className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.textAlign === 'right' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setAlignment('right')}
            className="w-8 h-8 p-0"
            title="Align Right"
          >
            <AlignRight className="w-4 h-4" />
          </Button>
          
          <Button
            variant={textStyle.textAlign === 'justify' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setAlignment('justify')}
            className="w-8 h-8 p-0"
            title="Justify"
          >
            <AlignJustify className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Line Height and Letter Spacing */}
      <div className="grid grid-cols-2 gap-2">
        <div>
          <Label htmlFor="line-height" className="text-xs">Line Height</Label>
          <Input
            id="line-height"
            type="number"
            min="0.5"
            max="3"
            step="0.1"
            value={textStyle.lineHeight}
            onChange={(e) => handleStyleChange({ lineHeight: parseFloat(e.target.value) || 1 })}
            className="h-8 text-xs"
          />
        </div>
        
        <div>
          <Label htmlFor="letter-spacing" className="text-xs">Letter Spacing</Label>
          <Input
            id="letter-spacing"
            type="number"
            min="-5"
            max="10"
            step="0.1"
            value={textStyle.letterSpacing}
            onChange={(e) => handleStyleChange({ letterSpacing: parseFloat(e.target.value) || 0 })}
            className="h-8 text-xs"
          />
        </div>
      </div>

      <Separator />

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button variant="outline" size="sm" onClick={onClose}>
          Cancel
        </Button>
        <Button size="sm" onClick={onClose}>
          Done
        </Button>
      </div>
    </div>
  );
};
