import React, { useRef } from 'react';
import { UserNote } from '@/types/conversation';
import { Connection, NodePosition } from '@/stores/useCanvasStore';
import { TPointerEventInfo } from 'fabric';

import { useFabricSetup } from './fabric-hooks/useFabricSetup';
import { useFabricRenderer } from './fabric-hooks/useFabricRenderer';
import { useFabricEvents } from './fabric-hooks/useFabricEvents';

interface FabricCanvasProps {
  notes: UserNote[];
  connections: Connection[];
  nodePositions: NodePosition[];
  selectedNodes: string[];
  connectingNodeId: string | null;
  onNodeMove: (updates: { id:string; x: number; y: number }[]) => void;
  onSelectionChange: (selectedIds: string[], isMultiSelect: boolean) => void;
  onNodeDoubleClick: (note: UserNote) => void;
  onCanvasClick: () => void;
  onConnectStart: (nodeId: string) => void;
  onConnectEnd: (fromId: string, toId: string) => void;
  onConnectCancel: () => void;
  onContextMenu: (e: TPointerEventInfo) => void;
  onZoomChange?: (zoom: number) => void; // Added
  onPanChange?: (pan: { x: number; y: number }) => void; // Added
}

export const FabricCanvas: React.FC<FabricCanvasProps> = (props) => {
  const canvasEl = useRef<HTMLCanvasElement>(null);
  const canvasWrapperEl = useRef<HTMLDivElement>(null);
  
  const canvas = useFabricSetup(canvasEl, canvasWrapperEl, props.onZoomChange, props.onPanChange); // Pass callbacks
  
  useFabricRenderer({
    canvas,
    notes: props.notes,
    connections: props.connections,
    nodePositions: props.nodePositions,
    selectedNodes: props.selectedNodes,
  });

  useFabricEvents({ canvas, ...props });

  return (
    <div ref={canvasWrapperEl} className="w-full h-full relative">
      <canvas ref={canvasEl} />
    </div>
  );
};
