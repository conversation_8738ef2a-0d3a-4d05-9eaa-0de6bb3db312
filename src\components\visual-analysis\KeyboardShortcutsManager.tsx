import React, { useEffect, useCallback, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Keyboard, X, Search, Command } from 'lucide-react';

interface KeyboardShortcut {
  id: string;
  keys: string[];
  description: string;
  category: string;
  action: () => void;
  enabled?: boolean;
}

interface KeyboardShortcutsManagerProps {
  isVisible: boolean;
  onToggle: () => void;
  shortcuts: KeyboardShortcut[];
  onShortcutExecute?: (shortcutId: string) => void;
}

export const KeyboardShortcutsManager: React.FC<KeyboardShortcutsManagerProps> = ({
  isVisible,
  onToggle,
  shortcuts,
  onShortcutExecute
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [pressedKeys, setPressedKeys] = useState<Set<string>>(new Set());

  // Group shortcuts by category
  const categories = ['all', ...Array.from(new Set(shortcuts.map(s => s.category)))];
  
  const filteredShortcuts = shortcuts.filter(shortcut => {
    const matchesSearch = shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         shortcut.keys.some(key => key.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'all' || shortcut.category === activeCategory;
    return matchesSearch && matchesCategory && shortcut.enabled !== false;
  });

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const key = event.key.toLowerCase();
    const modifiers = [];
    
    if (event.ctrlKey || event.metaKey) modifiers.push('ctrl');
    if (event.shiftKey) modifiers.push('shift');
    if (event.altKey) modifiers.push('alt');
    
    const keyCombo = [...modifiers, key].join('+');
    setPressedKeys(prev => new Set([...prev, keyCombo]));

    // Find matching shortcut
    const matchingShortcut = shortcuts.find(shortcut => {
      const shortcutCombo = shortcut.keys.join('+').toLowerCase();
      return shortcutCombo === keyCombo && shortcut.enabled !== false;
    });

    if (matchingShortcut) {
      event.preventDefault();
      event.stopPropagation();
      matchingShortcut.action();
      onShortcutExecute?.(matchingShortcut.id);
    }
  }, [shortcuts, onShortcutExecute]);

  const handleKeyUp = useCallback(() => {
    setPressedKeys(new Set());
  }, []);

  useEffect(() => {
    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
      
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('keyup', handleKeyUp);
      };
    }
  }, [isVisible, handleKeyDown, handleKeyUp]);

  const formatKeyCombo = (keys: string[]) => {
    return keys.map(key => {
      const keyMap: Record<string, string> = {
        'ctrl': '⌘',
        'shift': '⇧',
        'alt': '⌥',
        'meta': '⌘',
        'enter': '↵',
        'escape': 'Esc',
        'arrowup': '↑',
        'arrowdown': '↓',
        'arrowleft': '←',
        'arrowright': '→',
        'backspace': '⌫',
        'delete': '⌦',
        'tab': '⇥',
        'space': '␣'
      };
      
      return keyMap[key.toLowerCase()] || key.toUpperCase();
    });
  };

  const isKeyPressed = (keys: string[]) => {
    const keyCombo = keys.join('+').toLowerCase();
    return pressedKeys.has(keyCombo);
  };

  if (!isVisible) {
    return (
      <Button
        onClick={onToggle}
        className="fixed bottom-4 right-4 z-20 bg-white/10 hover:bg-white/20 backdrop-blur-md border border-white/20"
        size="sm"
      >
        <Keyboard className="w-4 h-4" />
      </Button>
    );
  }

  return (
    <Card className="fixed inset-4 z-30 bg-black/90 backdrop-blur-md border-white/20 text-white overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center">
            <Keyboard className="w-5 h-5 mr-2" />
            Keyboard Shortcuts
          </CardTitle>
          <Button
            onClick={onToggle}
            variant="ghost"
            size="sm"
            className="text-white hover:bg-white/10"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex gap-4 items-center mt-4">
          <div className="relative flex-1">
            <Search className="w-4 h-4 absolute left-3 top-2.5 text-gray-400" />
            <input
              type="text"
              placeholder="Search shortcuts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg focus:outline-none focus:border-white/40"
            />
          </div>
          
          <Badge variant="secondary" className="bg-blue-600">
            {filteredShortcuts.length} shortcuts
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="h-full overflow-hidden">
        <Tabs value={activeCategory} onValueChange={setActiveCategory}>
          <TabsList className="grid w-full grid-cols-auto bg-white/10 mb-4">
            {categories.map((category) => (
              <TabsTrigger 
                key={category} 
                value={category}
                className="text-xs capitalize"
              >
                {category}
                {category !== 'all' && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {shortcuts.filter(s => s.category === category).length}
                  </Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>

          <div className="h-full overflow-y-auto">
            <div className="grid gap-3">
              {filteredShortcuts.map((shortcut) => (
                <div
                  key={shortcut.id}
                  className={`p-4 rounded-lg border transition-all duration-200 ${
                    isKeyPressed(shortcut.keys)
                      ? 'bg-blue-600/20 border-blue-400'
                      : 'bg-white/5 border-white/10 hover:bg-white/10'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="font-medium text-sm mb-1">
                        {shortcut.description}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {shortcut.category}
                      </Badge>
                    </div>
                    
                    <div className="flex gap-1 ml-4">
                      {formatKeyCombo(shortcut.keys).map((key, index) => (
                        <React.Fragment key={index}>
                          <kbd className={`px-2 py-1 text-xs rounded border transition-colors ${
                            isKeyPressed(shortcut.keys)
                              ? 'bg-blue-600 border-blue-400 text-white'
                              : 'bg-gray-700 border-gray-600 text-gray-300'
                          }`}>
                            {key}
                          </kbd>
                          {index < formatKeyCombo(shortcut.keys).length - 1 && (
                            <span className="text-gray-400 text-xs self-center">+</span>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                  
                  <Button
                    onClick={() => {
                      shortcut.action();
                      onShortcutExecute?.(shortcut.id);
                    }}
                    size="sm"
                    variant="ghost"
                    className="mt-2 text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-600/10"
                  >
                    Execute
                  </Button>
                </div>
              ))}
              
              {filteredShortcuts.length === 0 && (
                <div className="text-center py-8 text-gray-400">
                  <Keyboard className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <div className="text-lg font-medium mb-2">No shortcuts found</div>
                  <div className="text-sm">
                    {searchQuery 
                      ? `No shortcuts match "${searchQuery}"`
                      : 'No shortcuts available in this category'
                    }
                  </div>
                </div>
              )}
            </div>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
};

/**
 * Hook for managing keyboard shortcuts
 */
export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
  const [isVisible, setIsVisible] = useState(false);
  const [executedShortcuts, setExecutedShortcuts] = useState<string[]>([]);

  const handleShortcutExecute = useCallback((shortcutId: string) => {
    setExecutedShortcuts(prev => [...prev, shortcutId]);
    
    // Remove from executed list after 2 seconds
    setTimeout(() => {
      setExecutedShortcuts(prev => prev.filter(id => id !== shortcutId));
    }, 2000);
  }, []);

  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  // Global shortcut to show/hide shortcuts panel
  useEffect(() => {
    const handleGlobalShortcut = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === '?') {
        event.preventDefault();
        toggleVisibility();
      }
    };

    document.addEventListener('keydown', handleGlobalShortcut);
    return () => document.removeEventListener('keydown', handleGlobalShortcut);
  }, [toggleVisibility]);

  return {
    isVisible,
    executedShortcuts,
    toggleVisibility,
    handleShortcutExecute
  };
};

/**
 * Default shortcuts for the Living Data Canvas
 */
export const defaultCanvasShortcuts: KeyboardShortcut[] = [
  // Navigation
  {
    id: 'pan-up',
    keys: ['arrowup'],
    description: 'Pan canvas up',
    category: 'navigation',
    action: () => console.log('Pan up')
  },
  {
    id: 'pan-down',
    keys: ['arrowdown'],
    description: 'Pan canvas down',
    category: 'navigation',
    action: () => console.log('Pan down')
  },
  {
    id: 'pan-left',
    keys: ['arrowleft'],
    description: 'Pan canvas left',
    category: 'navigation',
    action: () => console.log('Pan left')
  },
  {
    id: 'pan-right',
    keys: ['arrowright'],
    description: 'Pan canvas right',
    category: 'navigation',
    action: () => console.log('Pan right')
  },
  {
    id: 'zoom-in',
    keys: ['ctrl', '+'],
    description: 'Zoom in',
    category: 'navigation',
    action: () => console.log('Zoom in')
  },
  {
    id: 'zoom-out',
    keys: ['ctrl', '-'],
    description: 'Zoom out',
    category: 'navigation',
    action: () => console.log('Zoom out')
  },
  {
    id: 'fit-to-screen',
    keys: ['ctrl', '0'],
    description: 'Fit canvas to screen',
    category: 'navigation',
    action: () => console.log('Fit to screen')
  },

  // Selection
  {
    id: 'select-all',
    keys: ['ctrl', 'a'],
    description: 'Select all nodes',
    category: 'selection',
    action: () => console.log('Select all')
  },
  {
    id: 'deselect-all',
    keys: ['escape'],
    description: 'Deselect all nodes',
    category: 'selection',
    action: () => console.log('Deselect all')
  },

  // Actions
  {
    id: 'create-cluster',
    keys: ['c'],
    description: 'Create cluster from selection',
    category: 'actions',
    action: () => console.log('Create cluster')
  },
  {
    id: 'create-simulation',
    keys: ['s'],
    description: 'Create simulation from selection',
    category: 'actions',
    action: () => console.log('Create simulation')
  },
  {
    id: 'delete-selected',
    keys: ['delete'],
    description: 'Delete selected items',
    category: 'actions',
    action: () => console.log('Delete selected')
  },

  // UI
  {
    id: 'toggle-controls',
    keys: ['ctrl', 'shift', 'c'],
    description: 'Toggle advanced controls',
    category: 'ui',
    action: () => console.log('Toggle controls')
  },
  {
    id: 'toggle-help',
    keys: ['ctrl', 'shift', '?'],
    description: 'Toggle keyboard shortcuts',
    category: 'ui',
    action: () => console.log('Toggle help')
  },

  // Data
  {
    id: 'save',
    keys: ['ctrl', 's'],
    description: 'Save canvas state',
    category: 'data',
    action: () => console.log('Save')
  },
  {
    id: 'export',
    keys: ['ctrl', 'e'],
    description: 'Export canvas data',
    category: 'data',
    action: () => console.log('Export')
  },
  {
    id: 'import',
    keys: ['ctrl', 'i'],
    description: 'Import canvas data',
    category: 'data',
    action: () => console.log('Import')
  }
];
