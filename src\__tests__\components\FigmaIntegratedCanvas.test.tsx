/**
 * Unit Tests for FigmaIntegratedCanvas Component
 * 
 * Tests the integration between Figma design tools and Visual Canvas,
 * including mode switching, tool selection, and canvas interactions.
 */

import React from 'react';
import { render } from '@/__tests__/setup/customRender';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FigmaIntegratedCanvas } from '@/components/visual-analysis/FigmaIntegratedCanvas';

// Mock the lazy-loaded components
vi.mock('@/components/figma-canvas/FigmaCanvasContainer', () => ({
  FigmaCanvasContainer: vi.fn(({ className, onSelectionChange, onObjectCreated }) => (
    <div 
      data-testid="figma-canvas"
      className={className}
      onClick={() => {
        onSelectionChange?.(['test-object']);
        onObjectCreated?.({ id: 'test', type: 'rectangle' });
      }}
    >
      Figma Canvas Mock
    </div>
  ))
}));

vi.mock('@/components/visual-analysis/LivingDataCanvas', () => ({
  LivingDataCanvas: vi.fn(({ enableChatAnalysisIntegration }) => (
    <div data-testid="living-data-canvas">
      Living Data Canvas Mock (Chat Analysis: {enableChatAnalysisIntegration ? 'enabled' : 'disabled'})
    </div>
  ))
}));

vi.mock('@/components/visual-analysis/DesignAnalysisBridge', () => ({
  default: vi.fn(({ analysisResults, onCreateDesignElement, onExportPresentation }) => (
    <div 
      data-testid="design-analysis-bridge"
      onClick={() => {
        onCreateDesignElement?.({ id: 'test-element', type: 'text' });
        onExportPresentation?.('pdf');
      }}
    >
      Design Analysis Bridge Mock
    </div>
  ))
}));

describe('FigmaIntegratedCanvas', () => {
  const defaultProps = {
    className: 'test-canvas',
    initialMode: 'hybrid' as const,
    showModeToggle: true,
    enableDataBinding: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Rendering', () => {
    it('renders with default props', async () => {
      render(<FigmaIntegratedCanvas />, {});
      
      expect(screen.getByText('Design Mode')).toBeInTheDocument();
      expect(screen.getByText('Analysis Mode')).toBeInTheDocument();
      expect(screen.getByText('Hybrid Mode')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = render(<FigmaIntegratedCanvas className="custom-class" />, {});
      expect(container.firstChild).toHaveClass('custom-class');
    });

    it('shows mode toggle when enabled', () => {
      render(<FigmaIntegratedCanvas showModeToggle={true} />, {});
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('hides mode toggle when disabled', () => {
      render(<FigmaIntegratedCanvas showModeToggle={false} />, {});
      expect(screen.queryByRole('tablist')).not.toBeInTheDocument();
    });
  });

  describe('Mode Switching', () => {
    it('starts with initial mode', async () => {
      render(<FigmaIntegratedCanvas initialMode="design" />, {});
      
      await waitFor(() => {
        expect(screen.getByTestId('figma-canvas')).toBeInTheDocument();
      });
    });

    it('switches to analysis mode', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="design" />, {});
      
      const analysisTab = screen.getByRole('tab', { name: /analysis mode/i });
      await user.click(analysisTab);
      
      await waitFor(() => {
        expect(screen.getByTestId('living-data-canvas')).toBeInTheDocument();
      });
    });

    it('switches to hybrid mode', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="design" />, {});
      
      const hybridTab = screen.getByRole('tab', { name: /hybrid mode/i });
      await user.click(hybridTab);
      
      await waitFor(() => {
        expect(screen.getByTestId('figma-canvas')).toBeInTheDocument();
      });
    });
  });

  describe('Tool Selection', () => {
    it('renders all design tools', () => {
      render(<FigmaIntegratedCanvas />, {});
      
      expect(screen.getByTitle('Select')).toBeInTheDocument();
      expect(screen.getByTitle('Rectangle')).toBeInTheDocument();
      expect(screen.getByTitle('Circle')).toBeInTheDocument();
      expect(screen.getByTitle('Text')).toBeInTheDocument();
      expect(screen.getByTitle('Pen')).toBeInTheDocument();
      expect(screen.getByTitle('Move')).toBeInTheDocument();
    });

    it('selects tool when clicked', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas />, {});
      const rectangleTool = screen.getByTitle('Rectangle');
      await user.click(rectangleTool);
      // The selected tool uses variant="default" which applies bg-primary, not bg-background
      expect(rectangleTool).toHaveClass('bg-primary'); // Selected state
    });

    it('deselects previous tool when new tool is selected', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas />, {});
      const selectTool = screen.getByTitle('Select');
      const rectangleTool = screen.getByTitle('Rectangle');
      await user.click(rectangleTool);
      expect(rectangleTool).toHaveClass('bg-primary');
      await user.click(selectTool);
      expect(selectTool).toHaveClass('bg-primary');
      expect(rectangleTool).not.toHaveClass('bg-primary');
    });
  });

  describe('Panel Management', () => {
    it('shows layers panel by default', () => {
      render(<FigmaIntegratedCanvas />, {});
      expect(screen.getByText('Layers')).toBeInTheDocument();
    });

    it('toggles layers panel', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas />, {});
      // The layers toggle is the button with Layers icon (no accessible name)
      const layersButton = screen.getAllByRole('button').find(btn => btn.querySelector('svg.lucide-layers'));
      expect(layersButton).toBeDefined();
      if (layersButton) {
        await user.click(layersButton);
        expect(screen.queryByText('Layers')).not.toBeInTheDocument();
      }
    });

    it('shows properties panel by default', () => {
      render(<FigmaIntegratedCanvas />, {});
      expect(screen.getByText('Properties')).toBeInTheDocument();
    });

    it('shows analysis panel in analysis mode', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="analysis" />, {});
      
      await waitFor(() => {
        expect(screen.getByText('Analysis Tools')).toBeInTheDocument();
      });
    });

    it('shows analysis panel in hybrid mode', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="hybrid" />, {});
      
      await waitFor(() => {
        expect(screen.getByText('Analysis Tools')).toBeInTheDocument();
      });
    });
  });

  describe('Canvas Interactions', () => {
    it('handles Figma canvas selection changes', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="design" />, {});
      
      await waitFor(() => {
        const figmaCanvas = screen.getByTestId('figma-canvas');
        expect(figmaCanvas).toBeInTheDocument();
      });
      
      const figmaCanvas = screen.getByTestId('figma-canvas');
      await user.click(figmaCanvas);
      
      // Verify the mock was called with expected parameters
      expect(figmaCanvas).toBeInTheDocument();
    });

    it('handles design element creation', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas initialMode="hybrid" />, {});
      
      await waitFor(() => {
        const bridge = screen.getByTestId('design-analysis-bridge');
        expect(bridge).toBeInTheDocument();
      });
      
      const bridge = screen.getByTestId('design-analysis-bridge');
      await user.click(bridge);
      
      expect(bridge).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('adapts to mobile viewport', () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      
      render(<FigmaIntegratedCanvas />, {});
      
      // In mobile view, some elements might be hidden or reorganized
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('shows full interface on desktop', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200,
      });
      
      render(<FigmaIntegratedCanvas />, {});
      
      expect(screen.getByText('Layers')).toBeInTheDocument();
      expect(screen.getByText('Properties')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles canvas loading errors gracefully', async () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<FigmaIntegratedCanvas />, {});
      
      // The component should still render even if canvas fails to load
      expect(screen.getByText('Design Mode')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });

    it('shows loading state while components load', () => {
      render(<FigmaIntegratedCanvas />, {});
      // Should show loading indicator
      expect(screen.getByText('Loading Canvas...')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<FigmaIntegratedCanvas />, {});
      
      const tablist = screen.getByRole('tablist');
      expect(tablist).toBeInTheDocument();
      
      const tabs = screen.getAllByRole('tab');
      expect(tabs).toHaveLength(3);
      
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected');
      });
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas />, {});
      
      const firstTab = screen.getByRole('tab', { name: /design mode/i });
      firstTab.focus();
      
      await user.keyboard('{ArrowRight}');
      
      const secondTab = screen.getByRole('tab', { name: /analysis mode/i });
      expect(secondTab).toHaveFocus();
    });

    it('has proper heading structure', () => {
      render(<FigmaIntegratedCanvas />, {});
      
      // Check for proper heading hierarchy
      expect(screen.getByText('Layers')).toBeInTheDocument();
      expect(screen.getByText('Properties')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('lazy loads canvas components', async () => {
      render(<FigmaIntegratedCanvas />, {});
      // Initially shows loading state
      expect(screen.getByText('Loading Canvas...')).toBeInTheDocument();
      // After loading, shows the actual component
      await waitFor(() => {
        expect(screen.queryByText('Loading Canvas...')).not.toBeInTheDocument();
      });
    });
    it('does not render hidden panels', async () => {
      const user = userEvent.setup();
      render(<FigmaIntegratedCanvas />, {});
      // The layers toggle is the button with Layers icon (no accessible name)
      const layersButton = screen.getAllByRole('button').find(btn => btn.querySelector('svg.lucide-layers'));
      expect(layersButton).toBeDefined();
      if (layersButton) {
        await user.click(layersButton);
        // Hidden panel should not be in DOM
        expect(screen.queryByText('Layers panel will be integrated here')).not.toBeInTheDocument();
      }
    });
  });
});
